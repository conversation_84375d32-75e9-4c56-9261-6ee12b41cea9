import hashlib
import os
import re
import shutil
import time
import traceback
import uuid
import json
import platform
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import subprocess
import sys
import threading
import pyautogui
import pyperclip
import win32gui
import win32con
import win32api
import functools
import random
import logging
from typing import Callable, Any, Optional

import requests
import subprocess
import psutil
from selenium import webdriver
from selenium.common import NoSuchElementException, TimeoutException, WebDriverException, InvalidSessionIdException
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from abc import ABC, abstractmethod
from global_logger import log_info, log_warning, log_error, log_success, log_step, log_task_start, log_task_end
from selenium.webdriver.chrome.options import Options
from ziniao_socket_client import ZiniaoSocketClient

def get_app_path():
    """获取应用程序路径"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe
        return os.path.dirname(sys.executable)
    else:
        # 如果是python脚本
        return os.path.dirname(os.path.abspath(__file__))


class WebDriverStabilityError(Exception):
    """WebDriver稳定性相关错误的基类"""
    pass


class BrowserCrashError(WebDriverStabilityError):
    """浏览器崩溃错误"""
    pass


class ElementNotFoundError(WebDriverStabilityError):
    """元素未找到错误"""
    pass


class NetworkTimeoutError(WebDriverStabilityError):
    """网络超时错误"""  
    pass


def retry_with_exponential_backoff(max_retries: int = 3, base_delay: float = 1.0, max_delay: float = 10.0):
    """
    重试装饰器，使用指数退避策略
    
    Args:
        max_retries: 最大重试次数
        base_delay: 基础延迟时间(秒)
        max_delay: 最大延迟时间(秒)
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_retries + 1):  # +1 是因为包含首次尝试
                try:
                    if attempt > 0:
                        # 计算延迟时间：指数退避 + 随机抖动
                        delay = min(base_delay * (2 ** (attempt - 1)), max_delay)
                        jitter = random.uniform(0, 0.1 * delay)  # 10%随机抖动
                        total_delay = delay + jitter
                        
                        log_step(f"重试第{attempt}次，等待 {total_delay:.1f}s...")
                        time.sleep(total_delay)
                    
                    return func(*args, **kwargs)
                    
                except (WebDriverException, InvalidSessionIdException, BrowserCrashError) as e:
                    last_exception = e
                    error_type = type(e).__name__
                    log_warning(f"尝试{attempt + 1}失败 ({error_type}): {str(e)}")
                    
                    if attempt == max_retries:
                        log_error(f"所有重试已耗尽，操作最终失败")
                        break
                    
                    # 根据错误类型决定是否继续重试
                    if isinstance(e, InvalidSessionIdException):
                        log_error("WebDriver会话已失效，需要重新建立连接")
                        break
                        
                except Exception as e:
                    # 非WebDriver相关错误，不重试
                    log_error(f"非WebDriver错误，不进行重试: {str(e)}")
                    raise e
            
            # 所有重试都失败了，抛出最后的异常
            if last_exception:
                raise last_exception
            
        return wrapper
    return decorator

def get_config_path():
    """获取配置文件路径"""
    app_path = get_app_path()
    return os.path.join(app_path, "ziniao_config.json")

def get_driver_folder_path():
    """获取WebDriver文件夹路径"""
    app_path = get_app_path()
    return os.path.join(app_path, "webdriver")

# 国家列表（日本作为默认首选）
COUNTRIES = [
    "日本", "美国", "英国", "德国", "法国", "意大利", "西班牙", "加拿大",
    "澳大利亚", "墨西哥", "荷兰", "比利时", "波兰", "瑞典", "爱尔兰",
    "新加坡", "阿拉伯联合酋长国", "沙特阿拉伯", "埃及"
]

class BaseZiniaoRPA(ABC):
    """紫鸟浏览器RPA基础类"""

    # Amazon Seller Central主页URL匹配模式
    HOME_URL_PATTERN = re.compile(r"^https?://sellercentral(-[a-z]+)?\.amazon\.[a-z.]+/home(?:[/?#].*)?$")

    def __init__(self):
        # 初始化任务统计
        self.task_statistics = {
            'total_stores': 0,
            'successful_stores': 0,
            'failed_stores': [],
            'total_countries': 0,
            'successful_countries': 0,
            'failed_countries': [],
            'login_failures': [],
            'setting_failures': [],
            'module_failures': [],  # 模块执行失败
            'save_failures': [],  # 保存设置失败
            'navigation_failures': [],  # 页面导航失败
            'exception_failures': []  # 异常导致的失败
        }

        # 新增：按国家分组的详细统计
        self.country_statistics = {
            # 店铺名: {
            #     'successful_countries': [国家列表],
            #     'failed_countries': {
            #         国家: {'step': 步骤, 'reason': 原因, 'timestamp': 时间戳}
            #     }
            # }
        }
        self.client_path = None
        self.driver_folder_path = "./drivers"  # 设置默认值
        self.user_info = {}
        self.socket_port = 16851

        # 任务控制支持（由GUI设置）
        self.task_control = None

        # 初始化Socket客户端（使用不同端口避免与HTTP冲突）
        self.socket_client_port = 9222  # Socket专用端口
        self.socket_client = ZiniaoSocketClient(port=self.socket_client_port)
        self.socket_initialized = False

        # 初始化操作节奏控制管理器
        self.throttle_manager = ThrottledOperationManager(
            min_operation_interval=2.0,  # 操作间隔2秒
            dom_settle_time=1.5,         # DOM稳定等待1.5秒
            batch_break_size=3,          # 每3个操作休息一次
            batch_break_time=4.0         # 休息4秒
        )

        # 初始化ChromeDriver管理器
        self.chrome_driver_manager = ChromeDriverManager(
            driver_folder_path=self.driver_folder_path
        )

    def initialize_socket_client(self):
        """初始化Socket客户端连接"""
        if self.socket_initialized:
            return True

        try:
            # 设置用户信息
            if self.user_info:
                self.socket_client.set_user_info(
                    self.user_info.get('company', ''),
                    self.user_info.get('username', ''),
                    self.user_info.get('password', '')
                )

            # 确保紫鸟浏览器主进程运行
            if self.client_path and self.socket_client.ensure_ziniao_process_running(self.client_path):
                self.socket_initialized = True
                log_success("✅ Socket客户端初始化成功")
                return True
            else:
                log_error("❌ Socket客户端初始化失败")
                return False

        except Exception as e:
            log_error(f"Socket客户端初始化异常: {str(e)}")
            return False

    def shutdown_socket_and_restart_http(self):
        """彻底关闭Socket方式，重新启动HTTP方式"""
        try:
            log_warning("🔄 Socket方式失败，彻底关闭Socket进程，重新启动HTTP方式...")

            # 关闭Socket连接
            if self.socket_client:
                self.socket_client.disconnect()
                self.socket_initialized = False

            # 杀死所有可能存在的紫鸟进程
            try:
                import subprocess
                subprocess.run(['taskkill', '/f', '/im', 'superbrowser.exe'],
                             capture_output=True, timeout=10)
                subprocess.run(['taskkill', '/f', '/im', 'starter.exe'],
                             capture_output=True, timeout=10)
                log_info("已终止所有紫鸟浏览器进程")
                time.sleep(3)  # 增加等待时间，确保进程完全关闭
            except:
                pass

            # 重新启动HTTP模式的紫鸟浏览器
            if self.client_path:
                cmd = [
                    self.client_path,
                    '--run_type=web_driver',
                    '--ipc_type=http',
                    '--port=' + str(self.socket_port)
                ]
                log_info(f"重新启动HTTP模式: {' '.join(cmd)}")
                subprocess.Popen(cmd)

                # 等待HTTP服务启动并验证
                for i in range(10):  # 等待最多10秒
                    time.sleep(1)
                    try:
                        import requests
                        response = requests.get(f"http://127.0.0.1:{self.socket_port}/", timeout=2)
                        log_success("✅ HTTP模式重新启动完成")
                        return True
                    except:
                        log_info(f"等待HTTP服务启动... ({i+1}/10)")
                        continue

                log_warning("⚠️ HTTP服务启动验证超时，但进程已启动")
                return True  # 即使验证超时，也认为启动成功
            else:
                log_error("❌ 无法重新启动HTTP模式：client_path未设置")
                return False

        except Exception as e:
            log_error(f"重新启动HTTP模式失败: {str(e)}")
            return False

    @abstractmethod
    def execute_country_task(self, driver, country, upload_file_path, store_info):
        """
        在指定国家执行具体任务的抽象方法
        子类必须实现此方法来定义具体的业务逻辑
        
        :param driver: WebDriver实例
        :param country: 当前国家
        :param upload_file_path: 上传文件路径
        :param store_info: 店铺信息
        :return: 执行结果 (True/False)
        """
        pass
    
    @abstractmethod
    def get_task_name(self):
        """
        获取任务名称，用于显示在GUI标题中
        
        :return: 任务名称字符串
        """
        pass
    
    def requires_country_iteration(self):
        """
        是否需要进行国家遍历
        子类可以重写此方法来指定是否需要执行国家遍历逻辑
        
        :return: True需要国家遍历，False不需要 (默认True保持向后兼容)
        """
        return True
    
    @abstractmethod 
    def execute_main_task(self, driver, upload_file_path, store_info):
        """
        执行主要任务的抽象方法
        在进入商铺主页并处理登录后执行
        子类必须实现此方法来定义具体的业务逻辑
        
        :param driver: WebDriver实例
        :param upload_file_path: 上传文件路径  
        :param store_info: 店铺信息
        :return: 执行结果 (True/False)
        """
        pass

    def record_country_result(self, store_name, country, success, step=None, reason=None):
        """
        记录单个国家/站点的处理结果

        :param store_name: 店铺名称
        :param country: 国家/站点名称
        :param success: 是否成功 (True/False)
        :param step: 失败时的步骤名称
        :param reason: 失败原因
        """
        # 确保店铺在统计字典中存在
        if store_name not in self.country_statistics:
            self.country_statistics[store_name] = {
                'successful_countries': [],
                'failed_countries': {}
            }

        if success:
            # 记录成功
            if country not in self.country_statistics[store_name]['successful_countries']:
                self.country_statistics[store_name]['successful_countries'].append(country)
        else:
            # 记录失败
            import time
            self.country_statistics[store_name]['failed_countries'][country] = {
                'step': step or '未知步骤',
                'reason': reason or '未知原因',
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }

    def record_store_level_failure(self, store_name, countries, step, reason):
        """
        记录店铺级别的失败（影响所有计划处理的国家）

        :param store_name: 店铺名称
        :param countries: 计划处理的国家列表
        :param step: 失败步骤
        :param reason: 失败原因
        """
        # 确保店铺在统计字典中存在
        if store_name not in self.country_statistics:
            self.country_statistics[store_name] = {
                'successful_countries': [],
                'failed_countries': {}
            }

        # 将所有计划处理的国家标记为失败
        for country in countries:
            self.record_country_result(store_name, country, False, step, reason)

    def print_country_statistics(self):
        """
        打印按国家分组的详细统计信息
        """
        from global_logger import log_info, log_success, log_error

        if not self.country_statistics:
            log_info("📊 没有国家统计数据")
            return

        log_info("=" * 80)
        log_info("📊 按店铺和国家分组的详细统计报告")
        log_info("=" * 80)

        for store_name, stats in self.country_statistics.items():
            log_info(f"\n🏪 {store_name}:")

            # 显示成功的站点
            if stats['successful_countries']:
                success_countries = ', '.join(stats['successful_countries'])
                log_success(f"   ✅ 成功站点: {success_countries}")
            else:
                log_info(f"   ✅ 成功站点: 无")

            # 显示失败的站点
            if stats['failed_countries']:
                log_error(f"   ❌ 失败站点:")
                for country, failure_info in stats['failed_countries'].items():
                    step = failure_info.get('step', '未知步骤')
                    reason = failure_info.get('reason', '未知原因')
                    log_error(f"      • {country}: {step}失败，原因是{reason}")
            else:
                log_info(f"   ❌ 失败站点: 无")

        log_info("=" * 80)

    def requires_upload_folder(self):
        """
        是否需要上传文件夹
        子类可以重写此方法来指定是否需要上传文件夹功能
        
        :return: True需要，False不需要 (默认True保持向后兼容)
        """
        return True
    
    def validate_task_requirements(self, variable_manager, selected_stores):
        """
        验证任务执行所需的条件
        子类可以重写此方法来添加自定义的验证逻辑
        
        :param variable_manager: 变量管理器，可获取GUI中的配置值
        :param selected_stores: 选择的店铺列表
        :return: (验证结果, 错误信息) - (True, None)表示验证通过，(False, "错误信息")表示验证失败
        """
        # 如果需要上传文件夹，执行默认的文件验证逻辑
        if self.requires_upload_folder():
            return self._validate_upload_files_default(variable_manager, selected_stores)
        
        # 不需要上传文件夹，默认验证通过
        return True, None
    
    def _validate_upload_files_default(self, variable_manager, selected_stores):
        """
        默认的上传文件验证逻辑
        
        :param variable_manager: 变量管理器
        :param selected_stores: 选择的店铺列表
        :return: (验证结果, 错误信息)
        """
        upload_folder_var = variable_manager.get_variable('upload_folder')
        if not upload_folder_var or not upload_folder_var.get():
            return False, "请选择有效的上传文件文件夹"
        
        upload_folder = upload_folder_var.get()
        if not os.path.exists(upload_folder):
            return False, "上传文件文件夹不存在"
        
        missing_files = []
        for store in selected_stores:
            store_name = store['browserName']
            matching_files = [f for f in os.listdir(upload_folder) if f.startswith(store_name)]
            if not matching_files:
                missing_files.append(store_name)
        
        if missing_files:
            error_msg = f"以下店铺没有找到对应的上传文件：\n{', '.join(missing_files[:10])}"
            if len(missing_files) > 10:
                error_msg += f"\n... 还有 {len(missing_files) - 10} 个店铺"
            return False, error_msg
        
        return True, None
    
    def get_upload_file_path(self, variable_manager, store_name):
        """
        获取店铺对应的上传文件路径
        子类可以重写此方法来自定义文件路径获取逻辑
        
        :param variable_manager: 变量管理器
        :param store_name: 店铺名称
        :return: 上传文件路径，如果没有则返回None
        """
        if not self.requires_upload_folder():
            return None
            
        upload_folder_var = variable_manager.get_variable('upload_folder')
        if not upload_folder_var or not upload_folder_var.get():
            return None
            
        upload_folder = upload_folder_var.get()
        if not os.path.exists(upload_folder):
            return None
            
        matching_files = [f for f in os.listdir(upload_folder) if f.startswith(store_name)]
        if matching_files:
            return os.path.join(upload_folder, matching_files[0])
        return None
    
    def validate_upload_file(self, upload_file_path, store_name):
        """
        验证上传文件是否符合要求
        子类可以重写此方法来添加特定的文件验证逻辑
        
        :param upload_file_path: 上传文件路径
        :param store_name: 店铺名称
        :return: 验证结果 (True/False)
        """
        return os.path.exists(upload_file_path)
    
    def pre_execute_hook(self, driver, store_info):
        """
        执行任务前的钩子方法
        子类可以重写此方法来添加预处理逻辑

        :param driver: WebDriver实例
        :param store_info: 店铺信息
        """
        # 不再强制导航到主页，让URL状态判断来决定下一步操作
        pass
    
    def post_execute_hook(self, driver, store_info):
        """
        执行任务后的钩子方法
        子类可以重写此方法来添加后处理逻辑
        
        :param driver: WebDriver实例
        :param store_info: 店铺信息
        """
        pass
    
    def kill_process(self):
        """
        终止紫鸟客户端已启动的进程
        """
        if platform.system() == 'Windows':
            os.system('taskkill /f /t /im SuperBrowser.exe')
        elif platform.system() == 'Darwin':
            os.system('killall ziniao')
            time.sleep(3)

    def start_browser(self):
        """
        启动客户端
        :return:
        """
        try:
            if platform.system() == 'Windows':
                cmd = [self.client_path, '--run_type=web_driver', '--ipc_type=http', '--port=' + str(self.socket_port)]
            elif platform.system() == 'Darwin':
                cmd = ['open', '-a', self.client_path, '--args', '--run_type=web_driver', '--ipc_type=http',
                       '--port=' + str(self.socket_port)]
            else:
                exit()
            subprocess.Popen(cmd)
            time.sleep(5)
        except Exception:
            print('start browser process failed: ' + traceback.format_exc())
            exit()

    def update_core(self):
        """
        下载所有内核，打开店铺前调用，需客户端版本5.285.7以上
        因为http有超时时间，所以这个action适合循环调用，直到返回成功
        """     
        data = {
            "action": "updataCore",
            "requestId": str(uuid.uuid4()),
        }
        data.update(self.user_info)
        while True:
            result = self.send_http(data)
            if result is None:
                log_step("等待客户端启动...")
                time.sleep(2)
                continue
            if result.get("statusCode") is None or result.get("statusCode") == -10003:
                log_warning("当前版本不支持此接口，请升级客户端")
                return
            elif result.get("statusCode") == 0:
                log_success("更新内核完成")
                return
            else:
                log_step(f"等待更新内核: {json.dumps(result)}")
                time.sleep(2)

    def send_http(self, data):
        """
        通讯方式
        :param data:
        :return:
        """
        try:
            url = 'http://127.0.0.1:{}'.format(self.socket_port)
            response = requests.post(url, json.dumps(data).encode('utf-8'), timeout=120)
            return json.loads(response.text)
        except Exception as err:
            print(err)

    def open_store(self, store_info, isWebDriverReadOnlyMode=0, isprivacy=0, isHeadless=0, cookieTypeSave=0, jsInfo=""):
        """
        启动店铺（保持原有HTTP方式，确保兼容性）
        """
        request_id = str(uuid.uuid4())
        data = {
            "action": "startBrowser"
            , "isWaitPluginUpdate": 0
            , "isHeadless": isHeadless
            , "requestId": request_id
            , "isWebDriverReadOnlyMode": isWebDriverReadOnlyMode
            , "cookieTypeLoad": 0
            , "cookieTypeSave": cookieTypeSave
            , "runMode": "1"
            , "isLoadUserPlugin": False
            , "pluginIdType": 1
            , "privacyMode": isprivacy
        }
        data.update(self.user_info)

        if store_info.isdigit():
            data["browserId"] = store_info
        else:
            data["browserOauth"] = store_info

        if len(str(jsInfo)) > 2:
            data["injectJsInfo"] = json.dumps(jsInfo)

        r = self.send_http(data)
        if str(r.get("statusCode")) == "0":
            return r
        elif str(r.get("statusCode")) == "-10003":
            print(f"login Err {json.dumps(r, ensure_ascii=False)}")
            exit()
        else:
            print(f"Fail {json.dumps(r, ensure_ascii=False)} ")
            exit()

    def open_store_with_socket(self, store_info, isHeadless=0):
        """
        启动店铺（新的Socket版本，作为可选功能）

        :param store_info: 店铺信息（browserOauth或browserId）
        :param isHeadless: 是否无头模式
        :return: 启动结果
        """
        try:
            log_step(f"正在通过Socket启动店铺: {store_info}")

            # 初始化Socket客户端
            if not self.initialize_socket_client():
                log_warning("Socket客户端初始化失败，回退到HTTP方式")
                return self.open_store(store_info, isHeadless=isHeadless)

            # 确定店铺ID
            if isinstance(store_info, dict):
                browser_oauth = store_info.get('browserOauth')
            elif isinstance(store_info, str):
                browser_oauth = store_info
            else:
                browser_oauth = str(store_info)

            # 通过Socket启动店铺
            result = self.socket_client.start_browser(browser_oauth, bool(isHeadless))

            if result:
                log_success(f"✅ Socket方式启动店铺成功: {browser_oauth}")
                return result
            else:
                log_warning("Socket方式启动失败，彻底切换到HTTP方式")
                # 彻底关闭Socket，重新启动HTTP
                if self.shutdown_socket_and_restart_http():
                    log_step("🔄 重新使用HTTP方式启动店铺...")
                    return self.open_store(store_info, isHeadless=isHeadless)
                else:
                    log_error("❌ HTTP方式重启失败")
                    return None

        except Exception as e:
            log_warning(f"Socket启动异常，彻底切换到HTTP方式: {str(e)}")
            # 彻底关闭Socket，重新启动HTTP
            if self.shutdown_socket_and_restart_http():
                log_step("🔄 重新使用HTTP方式启动店铺...")
                return self.open_store(store_info, isHeadless=isHeadless)
            else:
                log_error("❌ HTTP方式重启失败")
                return None

    def close_store(self, browser_oauth):
        """
        关闭店铺（保持原有HTTP方式，确保兼容性）
        """
        request_id = str(uuid.uuid4())
        data = {
            "action": "stopBrowser"
            , "requestId": request_id
            , "duplicate": 0
            , "browserOauth": browser_oauth
        }
        data.update(self.user_info)

        r = self.send_http(data)

        # 检查响应是否为空
        if r is None:
            log_error(f"HTTP关闭店铺请求失败，响应为空: {browser_oauth}")
            return False

        # 检查响应是否有statusCode
        if not isinstance(r, dict) or "statusCode" not in r:
            log_error(f"HTTP关闭店铺响应格式错误: {r}")
            return False

        if str(r.get("statusCode")) == "0":
            log_success(f"HTTP方式关闭店铺成功: {browser_oauth}")
            return r
        elif str(r.get("statusCode")) == "-10003":
            log_error(f"HTTP关闭店铺登录错误: {json.dumps(r, ensure_ascii=False)}")
            return False
        else:
            log_error(f"HTTP关闭店铺失败: {json.dumps(r, ensure_ascii=False)}")
            return False

    def close_store_with_socket(self, browser_oauth):
        """
        关闭店铺（新的Socket版本，作为可选功能）

        :param browser_oauth: 店铺ID
        :return: 关闭是否成功
        """
        try:
            log_step(f"正在通过Socket关闭店铺: {browser_oauth}")

            # 如果Socket客户端未初始化，回退到HTTP方式
            if not self.socket_initialized:
                if not self.initialize_socket_client():
                    log_warning("Socket客户端未初始化，回退到HTTP方式")
                    return self.close_store(browser_oauth)

            # 通过Socket关闭店铺
            result = self.socket_client.stop_browser(browser_oauth)

            if result:
                log_success(f"✅ Socket方式关闭店铺成功: {browser_oauth}")
                return True
            else:
                log_warning("Socket方式关闭失败，回退到HTTP方式")
                return self.close_store(browser_oauth)

        except Exception as e:
            log_warning(f"Socket关闭异常，回退到HTTP方式: {str(e)}")
            try:
                return self.close_store(browser_oauth)
            except Exception as http_e:
                log_error(f"HTTP方式关闭也失败: {str(http_e)}")
                return False

    def get_browser_list(self) -> list:
        request_id = str(uuid.uuid4())
        data = {
            "action": "getBrowserList",
            "requestId": request_id
        }
        data.update(self.user_info)

        r = self.send_http(data)
        if str(r.get("statusCode")) == "0":
            log_success(f"获取到 {len(r.get('browserList', []))} 个店铺")
            return r.get("browserList")
        elif str(r.get("statusCode")) == "-10003":
            log_error(f"登录错误: {json.dumps(r, ensure_ascii=False)}")
            exit()
        else:
            log_error(f"获取店铺列表失败: {json.dumps(r, ensure_ascii=False)}")
            exit()

    def get_driver(self, open_ret_json, isHeadless=0):
        """
        获取WebDriver实例（保持原有逻辑，确保兼容性）
        """
        core_type = open_ret_json.get('core_type')
        if core_type == 'Chromium' or core_type == 0:
            # 获取Chrome版本信息（必须使用紫鸟返回的版本）
            log_step("正在检测Chrome版本...")

            # 必须使用紫鸟返回的版本（紫鸟浏览器的实际版本）
            core_version = open_ret_json.get('core_version')
            if core_version:
                log_info(f"✅ 紫鸟返回Chrome版本: {core_version}")
            else:
                # 紫鸟未返回版本，使用默认版本131
                log_warning("⚠️ 紫鸟未返回版本信息，使用默认版本131")
                core_version = '131.0.0.0'  # 紫鸟浏览器常用版本

            major = core_version.split('.')[0]
            log_info(f"HTTP方式检测到Chrome版本: {core_version}, 主版本号: {major}")
            if platform.system() == 'Windows':
                chrome_driver_path = os.path.join(self.driver_folder_path, 'chromedriver%s.exe') % major
            else:
                chrome_driver_path = os.path.join(self.driver_folder_path, 'chromedriver%s') % major

            # 检查驱动是否存在，如果不存在则下载
            if not os.path.exists(chrome_driver_path):
                log_info(f"ChromeDriver {major} 不存在，开始下载...")
                if self.chrome_driver_manager.download_driver_for_version(major):
                    log_success(f"ChromeDriver {major} 下载成功")
                else:
                    log_error(f"ChromeDriver {major} 下载失败")

                    # 尝试查找其他可用版本
                    log_info("尝试查找其他可用的ChromeDriver版本...")
                    available_drivers = self._find_available_chrome_drivers()
                    if available_drivers:
                        log_info(f"找到可用的ChromeDriver版本: {[os.path.basename(d) for d in available_drivers]}")
                        # 使用最匹配的版本
                        best_match = self._find_best_matching_driver(major, available_drivers)
                        if best_match:
                            chrome_driver_path = best_match
                            driver_version = self._extract_version_from_path(best_match)
                            log_warning(f"使用最匹配的替代版本: {os.path.basename(chrome_driver_path)} (版本{driver_version})")
                        else:
                            chrome_driver_path = available_drivers[0]
                            log_warning(f"使用第一个可用版本: {os.path.basename(chrome_driver_path)}")
                    else:
                        log_error("未找到任何可用的ChromeDriver")
                        return None

            print(f"chrome_driver_path: {chrome_driver_path}")
            port = open_ret_json.get('debuggingPort')

            # 使用优化的Chrome配置，传递正确的无头模式参数
            is_headless_bool = bool(isHeadless)
            options = self.get_optimized_chrome_options(is_headless=is_headless_bool)
            options.add_experimental_option("debuggerAddress", '127.0.0.1:' + str(port))

            # 调试日志
            log_info(f"Chrome配置: 无头模式={is_headless_bool} (原始参数: {isHeadless})")

            log_info(f"使用优化的Chrome配置启动浏览器，端口: {port}")
            try:
                driver = webdriver.Chrome(service=Service(chrome_driver_path), options=options)
                return driver
            except Exception as e:
                error_msg = str(e)
                if "This version of ChromeDriver only supports Chrome version" in error_msg:
                    log_error(f"ChromeDriver版本不匹配: {error_msg}")

                    # 从错误信息中提取实际的浏览器版本（"Current browser version is"后面的版本）
                    import re
                    actual_version_match = re.search(r'Current browser version is (\d+)\.', error_msg)
                    if actual_version_match:
                        actual_browser_version = actual_version_match.group(1)
                        log_info(f"🔍 从错误信息中提取到正确的浏览器版本: {actual_browser_version}")

                        # 尝试使用正确浏览器版本的ChromeDriver
                        if platform.system() == 'Windows':
                            correct_chrome_driver_path = os.path.join(self.driver_folder_path, f'chromedriver{actual_browser_version}.exe')
                        else:
                            correct_chrome_driver_path = os.path.join(self.driver_folder_path, f'chromedriver{actual_browser_version}')

                        if os.path.exists(correct_chrome_driver_path):
                            log_info(f"✅ 找到正确版本的ChromeDriver: {correct_chrome_driver_path}")
                            driver = webdriver.Chrome(service=Service(correct_chrome_driver_path), options=options)
                            return driver
                        else:
                            log_warning(f"⚠️ 正确版本{actual_browser_version}的ChromeDriver不存在，尝试下载...")
                            # 尝试下载正确版本的ChromeDriver
                            if self.chrome_driver_manager.download_driver_for_version(actual_browser_version):
                                log_success(f"✅ ChromeDriver {actual_browser_version} 下载成功")
                                driver = webdriver.Chrome(service=Service(correct_chrome_driver_path), options=options)
                                return driver
                            else:
                                log_warning(f"⚠️ ChromeDriver {actual_browser_version} 下载失败，查找最佳匹配...")
                                # 查找最佳匹配
                                available_drivers = self._find_available_chrome_drivers()
                                if available_drivers:
                                    best_match = self._find_best_matching_driver(actual_browser_version, available_drivers)
                                    if best_match:
                                        match_version = self._extract_version_from_path(best_match)
                                        log_warning(f"使用最佳匹配的ChromeDriver: {os.path.basename(best_match)} (版本{match_version})")
                                        driver = webdriver.Chrome(service=Service(best_match), options=options)
                                        return driver
                                    else:
                                        log_error("❌ 未找到合适的ChromeDriver版本")
                                        raise e
                                else:
                                    log_error("❌ 未找到任何可用的ChromeDriver")
                                    raise e
                    else:
                        log_error("❌ 无法从错误信息中提取浏览器版本")
                        raise e
                else:
                    raise e
        else:
            return None

    def get_driver_with_socket(self, open_ret_json, isHeadless=0):
        """
        获取WebDriver实例（新的Socket版本，作为可选功能）

        :param open_ret_json: 店铺启动返回的信息
        :param isHeadless: 无头模式（已通过Socket控制，此参数仅用于日志）
        :return: WebDriver实例
        """
        try:
            # 获取调试端口
            port = open_ret_json.get('debuggingPort')
            if not port:
                log_error("未找到调试端口，无法连接到浏览器")
                return None

            log_step(f"正在连接到紫鸟浏览器，调试端口: {port}")

            # 获取Chrome版本信息（必须使用紫鸟返回的版本）
            log_step("正在检测Chrome版本...")

            # 必须使用紫鸟返回的版本（紫鸟浏览器的实际版本）
            core_version = open_ret_json.get('core_version')
            if core_version:
                log_info(f"✅ 紫鸟返回Chrome版本: {core_version}")
            else:
                # 紫鸟未返回版本，使用默认版本131
                log_warning("⚠️ 紫鸟未返回版本信息，使用默认版本131")
                core_version = '131.0.0.0'  # 紫鸟浏览器常用版本

            major_version = core_version.split('.')[0]
            log_info(f"检测到Chrome版本: {core_version}, 主版本号: {major_version}")

            # 获取ChromeDriver路径
            if platform.system() == 'Windows':
                chrome_driver_path = os.path.join(self.driver_folder_path, f'chromedriver{major_version}.exe')
            else:
                chrome_driver_path = os.path.join(self.driver_folder_path, f'chromedriver{major_version}')

            # 检查并下载ChromeDriver
            if not os.path.exists(chrome_driver_path):
                log_info(f"ChromeDriver {major_version} 不存在，开始下载...")
                if self.chrome_driver_manager.download_driver_for_version(major_version):
                    log_success(f"ChromeDriver {major_version} 下载成功")
                else:
                    log_error(f"ChromeDriver {major_version} 下载失败")

                    # 尝试查找其他可用版本
                    log_info("尝试查找其他可用的ChromeDriver版本...")
                    available_drivers = self._find_available_chrome_drivers()
                    if available_drivers:
                        log_info(f"找到可用的ChromeDriver版本: {[os.path.basename(d) for d in available_drivers]}")
                        # 使用最接近的版本（优先选择版本号接近的）
                        best_match = self._find_best_matching_driver(major_version, available_drivers)
                        if best_match:
                            chrome_driver_path = best_match
                            driver_version = self._extract_version_from_path(best_match)
                            log_warning(f"使用最匹配的替代版本: {os.path.basename(chrome_driver_path)} (版本{driver_version})")
                        else:
                            chrome_driver_path = available_drivers[0]
                            log_warning(f"使用第一个可用版本: {os.path.basename(chrome_driver_path)}")
                    else:
                        log_error("未找到任何可用的ChromeDriver")
                        return None

            # 配置Chrome选项（连接到已启动的浏览器）
            options = webdriver.ChromeOptions()
            options.add_experimental_option("debuggerAddress", f"127.0.0.1:{port}")

            # 调试日志
            log_info(f"🔧 Socket连接配置: 端口={port}, ChromeDriver={major_version}")
            log_info(f"📋 无头模式通过Socket控制: {bool(isHeadless)}")

            # 连接到已启动的浏览器
            log_step("正在建立WebDriver连接...")
            try:
                driver = webdriver.Chrome(service=Service(chrome_driver_path), options=options)
            except Exception as e:
                error_msg = str(e)
                if "This version of ChromeDriver only supports Chrome version" in error_msg:
                    log_error(f"ChromeDriver版本不匹配: {error_msg}")

                    # 从错误信息中提取实际的浏览器版本（"Current browser version is"后面的版本）
                    import re
                    actual_version_match = re.search(r'Current browser version is (\d+)\.', error_msg)
                    if actual_version_match:
                        actual_browser_version = actual_version_match.group(1)
                        log_info(f"🔍 从错误信息中提取到正确的浏览器版本: {actual_browser_version}")

                        # 尝试使用正确浏览器版本的ChromeDriver
                        if platform.system() == 'Windows':
                            correct_chrome_driver_path = os.path.join(self.driver_folder_path, f'chromedriver{actual_browser_version}.exe')
                        else:
                            correct_chrome_driver_path = os.path.join(self.driver_folder_path, f'chromedriver{actual_browser_version}')

                        if os.path.exists(correct_chrome_driver_path):
                            log_info(f"✅ 找到正确版本的ChromeDriver: {correct_chrome_driver_path}")
                            driver = webdriver.Chrome(service=Service(correct_chrome_driver_path), options=options)
                        else:
                            log_warning(f"⚠️ 正确版本{actual_browser_version}的ChromeDriver不存在，尝试下载...")
                            # 尝试下载正确版本的ChromeDriver
                            if self.chrome_driver_manager.download_driver_for_version(actual_browser_version):
                                log_success(f"✅ ChromeDriver {actual_browser_version} 下载成功")
                                driver = webdriver.Chrome(service=Service(correct_chrome_driver_path), options=options)
                            else:
                                log_warning(f"⚠️ ChromeDriver {actual_browser_version} 下载失败，查找最佳匹配...")
                                # 查找最佳匹配
                                available_drivers = self._find_available_chrome_drivers()
                                if available_drivers:
                                    best_match = self._find_best_matching_driver(actual_browser_version, available_drivers)
                                    if best_match:
                                        match_version = self._extract_version_from_path(best_match)
                                        log_warning(f"使用最佳匹配的ChromeDriver: {os.path.basename(best_match)} (版本{match_version})")
                                        driver = webdriver.Chrome(service=Service(best_match), options=options)
                                    else:
                                        log_error("❌ 未找到合适的ChromeDriver版本")
                                        raise e
                                else:
                                    log_error("❌ 未找到任何可用的ChromeDriver")
                                    raise e
                    else:
                        log_error("❌ 无法从错误信息中提取浏览器版本")
                        raise e
                else:
                    raise e

            # 导航到启动页面（如果有），优先使用日本站点
            launcher_page = open_ret_json.get('launcherPage')
            if launcher_page:
                # 如果是美国站点的主页，改为日本站点
                if launcher_page == "https://sellercentral.amazon.com/home":
                    launcher_page = "https://sellercentral-japan.amazon.com/home"
                    log_step(f"🔄 将启动页面从美国站点改为日本站点: {launcher_page}")
                else:
                    log_step(f"🔄 正在导航到启动页面: {launcher_page}")
                driver.get(launcher_page)

                # 检查导航后的URL，如果是未注册页面则导航到日本站点
                time.sleep(3)  # 等待页面加载
                current_url = driver.current_url
                if "authorization/failed/continue-registration" in current_url:
                    log_warning(f"⚠️ 检测到站点未注册: {current_url}")
                    log_step("🔄 导航到日本站点首页...")
                    driver.get("https://sellercentral-japan.amazon.com/home")
                    time.sleep(3)

            log_success("✅ WebDriver连接成功")
            return driver

        except Exception as e:
            log_error(f"❌ WebDriver连接失败: {str(e)}")
            return None

    def _find_available_chrome_drivers(self):
        """查找driver文件夹中所有可用的ChromeDriver"""
        available_drivers = []
        try:
            if not os.path.exists(self.driver_folder_path):
                return available_drivers

            # 查找所有chromedriver文件
            for file in os.listdir(self.driver_folder_path):
                if file.startswith('chromedriver') and (file.endswith('.exe') or '.' not in file):
                    full_path = os.path.join(self.driver_folder_path, file)
                    if os.path.isfile(full_path):
                        available_drivers.append(full_path)
                        log_info(f"找到ChromeDriver: {file}")

            # 按版本号排序（尝试提取版本号）
            def extract_version(filename):
                try:
                    # 提取文件名中的数字作为版本号
                    import re
                    match = re.search(r'chromedriver(\d+)', filename)
                    return int(match.group(1)) if match else 0
                except:
                    return 0

            available_drivers.sort(key=lambda x: extract_version(os.path.basename(x)), reverse=True)
            log_info(f"按版本排序后的ChromeDriver: {[os.path.basename(d) for d in available_drivers]}")

        except Exception as e:
            log_error(f"查找ChromeDriver失败: {str(e)}")

        return available_drivers

    def _find_best_matching_driver(self, target_version, available_drivers):
        """
        从可用的ChromeDriver中找到最匹配的版本

        Args:
            target_version: 目标Chrome版本号（字符串）
            available_drivers: 可用的ChromeDriver路径列表

        Returns:
            str: 最匹配的ChromeDriver路径，如果没有找到返回None
        """
        try:
            target_version_int = int(target_version)
            best_match = None
            min_diff = float('inf')

            for driver_path in available_drivers:
                driver_version = self._extract_version_from_path(driver_path)
                if driver_version:
                    diff = abs(driver_version - target_version_int)
                    if diff < min_diff:
                        min_diff = diff
                        best_match = driver_path

            return best_match
        except Exception as e:
            log_error(f"查找最匹配ChromeDriver时出错: {str(e)}")
            return None

    def _extract_version_from_path(self, driver_path):
        """
        从ChromeDriver路径中提取版本号

        Args:
            driver_path: ChromeDriver文件路径

        Returns:
            int: 版本号，如果提取失败返回None
        """
        try:
            import re
            filename = os.path.basename(driver_path)
            match = re.search(r'chromedriver(\d+)', filename)
            return int(match.group(1)) if match else None
        except Exception as e:
            log_error(f"提取ChromeDriver版本号时出错: {str(e)}")
            return None

    def get_browser_list_with_socket(self):
        """
        通过Socket获取店铺列表（新功能）

        :return: 店铺列表或None
        """
        try:
            # 初始化Socket客户端
            if not self.initialize_socket_client():
                log_warning("Socket客户端初始化失败，无法获取店铺列表")
                return None

            # 获取店铺列表
            browser_list = self.socket_client.get_browser_list()

            if browser_list:
                log_success(f"✅ 通过Socket获取到 {len(browser_list)} 个店铺")
                return browser_list
            else:
                log_warning("Socket方式获取店铺列表失败")
                return None

        except Exception as e:
            log_warning(f"Socket获取店铺列表异常: {str(e)}")
            return None

    def check_login_status(self, driver):
        """
        检查登录状态（增强版，支持多种检测方式）
        特别针对无头模式进行优化
        """
        try:
            log_step("🔍 检查登录状态...")

            # 方法1: 检查当前URL
            current_url = driver.current_url
            log_info(f"📍 当前URL: {current_url}")

            # 如果URL包含登录相关关键词，说明未登录
            login_indicators = ['signin', 'login', 'ap/signin', 'ap/mfa', 'authentication']
            if any(indicator in current_url.lower() for indicator in login_indicators):
                log_info(f"❌ URL包含登录指示器，用户未登录: {current_url}")
                return False

            # 如果URL包含home或dashboard，可能已登录
            if 'home' in current_url or 'dashboard' in current_url:
                log_info("✅ URL显示可能已登录，进一步验证...")

            # 方法2: 尝试查找设置按钮元素
            settings_button_xpaths = [
                "//*[@id='navbar']/div[3]/div/div[2]/div/div[1]/div",  # 主要设置按钮xpath
                "//*[@id='navbar']//div[contains(@class, 'settings')]",  # 通用设置按钮
                "//div[contains(text(), '设置') or contains(text(), 'Settings')]",  # 包含设置文本的元素
                "//*[contains(@aria-label, '设置') or contains(@aria-label, 'settings')]",  # aria-label包含设置的元素
                "//*[@id='navbar']",  # 导航栏存在
                "//div[contains(@class, 'navbar')]",  # 导航栏class
            ]

            for xpath in settings_button_xpaths:
                try:
                    settings_element = WebDriverWait(driver, 3).until(
                        EC.presence_of_element_located((By.XPATH, xpath))
                    )
                    if settings_element.is_displayed():
                        log_success(f"✅ 检测到导航元素存在且可见，用户已登录。使用xpath: {xpath}")
                        return True
                except TimeoutException:
                    continue

            # 方法3: 检查页面标题
            try:
                page_title = driver.title
                if any(keyword in page_title.lower() for keyword in ['seller central', 'dashboard', 'home']):
                    log_success(f"✅ 页面标题显示已登录: {page_title}")
                    return True
                elif any(keyword in page_title.lower() for keyword in ['sign in', 'login', 'authentication']):
                    log_info(f"❌ 页面标题显示未登录: {page_title}")
                    return False
            except:
                pass

            log_info("❓ 无法确定登录状态，认为未登录")
            return False

        except Exception as e:
            log_error(f"检查登录状态时发生错误: {str(e)}")
            # 发生错误时，保守起见认为未登录
            return False

    def navigate_to_seller_central_home(self, driver):
        """
        导航到商铺主页并处理登录
        
        :param driver: WebDriver实例
        :return: 成功返回True，失败返回False
        """
        try:
            log_step("正在导航到Amazon Seller Central主页...")

            # 根据当前站点动态确定主页URL
            current_url = driver.current_url
            home_url = self._generate_home_url(current_url)

            log_info(f"导航到主页URL: {home_url}")
            driver.get(home_url)
            
            # 使用智能等待替代固定延迟
            log_step("等待商铺主页完全加载...")
            if not self.wait_for_page_ready(driver, timeout=15.0):
                log_warning("商铺主页可能未完全加载，但继续执行")
            
            # 先检查登录状态，如果未登录则处理登录
            if not self.check_login_status(driver):
                log_step("检测到用户未登录，开始处理登录...")
                if not self._handle_login_if_needed(driver):
                    log_error("登录处理失败，跳过此店铺")
                    return False
            else:
                log_success("用户已登录，跳过登录处理")
                
            # 判断当前url是否包含home（额外保险检查）
            if "home" not in driver.current_url:
                # 处理可能的登录页面
                if not self._handle_login_if_needed(driver):
                    log_error("登录处理失败，跳过此店铺")
                    return False

            # 判断是否在主页（使用正则表达式精确匹配）
            current_url = driver.current_url
            if not self.HOME_URL_PATTERN.match(current_url):
                log_error(f"导航到商铺主页失败，当前URL: {current_url}")
                return False
            log_success("成功进入Amazon Seller Central主页")
            return True
            
        except Exception as e:
            log_error(f"导航到商铺主页失败: {str(e)}")
            return False

    def _generate_home_url(self, current_url: str) -> str:
        """
        根据当前URL生成对应的主页URL

        Args:
            current_url: 当前页面的URL

        Returns:
            str: 对应的主页URL
        """
        try:
            if not current_url or not isinstance(current_url, str):
                raise ValueError("Invalid URL")

            import urllib.parse
            parsed = urllib.parse.urlparse(current_url)

            # 检查URL是否有效
            if not parsed.scheme or not parsed.netloc:
                raise ValueError("Invalid URL format")

            # 构建主页URL
            home_url = f"{parsed.scheme}://{parsed.netloc}/home"
            return home_url
        except Exception:
            # 默认返回日本站点主页
            return "https://sellercentral-japan.amazon.com/home"

    def is_home_page(self, url: str) -> bool:
        """
        检查URL是否为Seller Central主页

        支持的格式:
        - https://sellercentral.amazon.com/home
        - https://sellercentral-japan.amazon.com/home?params
        - https://sellercentral.amazon.co.jp/home#anchor
        - 等等

        Args:
            url: 要检查的URL

        Returns:
            bool: 是否为主页URL
        """
        if not url:
            return False

        return self.HOME_URL_PATTERN.match(url) is not None
    
    def _handle_login_if_needed(self, driver):
        """
        基于URL智能处理登录流程

        :param driver: WebDriver实例
        :return: 登录成功返回True，失败返回False
        """
        try:
            log_step("🔐 开始智能登录处理...")

            # 如果存在设置按钮则认为已登录，直接返回
            if self.check_login_status(driver):
                log_success("✅ 检测到已登录状态，跳过登录处理")
                return True

            # 获取当前页面信息用于调试
            current_url = driver.current_url
            page_title = driver.title
            log_info(f"📍 登录处理 - 当前URL: {current_url}")
            log_info(f"📄 登录处理 - 页面标题: {page_title}")

            # 基于URL智能判断登录阶段
            login_stage = self._determine_login_stage(current_url)
            log_info(f"🎯 检测到登录阶段: {login_stage}")

            # 根据登录阶段执行相应操作
            if login_stage == "continue_page":
                return self._handle_continue_page(driver)
            elif login_stage == "signin_page":
                return self._handle_signin_page(driver)
            elif login_stage == "mfa_page":
                return self._handle_mfa_page(driver)
            elif login_stage == "country_selection":
                # 检测到account-switcher页面，使用switch_country方法处理
                log_step("🌍 检测到account-switcher页面，使用switch_country方法处理")
                current_country = getattr(self, 'current_country', None)
                if not current_country:
                    current_country = "日本"  # 默认选择日本
                    log_info(f"📍 未指定目标国家，使用默认国家: {current_country}")
                else:
                    log_info(f"📍 目标国家: {current_country}")

                # 直接使用switch_country方法，它包含完整的选择和点击逻辑
                return self.switch_country(driver, current_country)
            elif login_stage == "logged_in":
                log_success("✅ 已在主页，无需登录")
                return True
            elif login_stage == "unknown":
                return self._handle_unknown_login_page(driver)
            else:
                log_error(f"❌ 未知的登录阶段: {login_stage}")
                return False

        except Exception as e:
            log_error(f"❌ 登录处理过程中发生异常: {str(e)}")
            import traceback
            log_error(f"详细错误: {traceback.format_exc()}")
            return False

    def _determine_url_stage(self, url):
        """
        基于正则表达式判断URL阶段，区分不同的登录页面类型
        """
        # 登录页面正则（支持各国站点）
        login_pattern = re.compile(r"^https?://sellercentral(-[a-z]+)?\.amazon\.[a-z.]+/ap/signin")
        # MFA页面正则
        mfa_pattern = re.compile(r"^https?://sellercentral(-[a-z]+)?\.amazon\.[a-z.]+/ap/mfa")
        # 国家切换正则
        switcher_pattern = re.compile(r"^https?://sellercentral(-[a-z]+)?\.amazon\.[a-z.]+/account-switcher")

        # 检查是否为未注册页面
        if "authorization/failed/continue-registration" in url:
            return "unregistered_site"
        elif mfa_pattern.match(url):
            return "mfa_page"
        elif login_pattern.match(url):
            # 进一步区分continue_page和signin_page
            if "?" in url:
                return "continue_page"  # 带查询参数，需要点击继续按钮
            else:
                return "signin_page"    # 不带查询参数，直接登录
        elif switcher_pattern.match(url):
            return "country_selection"
        else:
            return "unknown"  # 其他情况，直接进入主任务

    def _determine_login_stage(self, url):
        """
        基于URL判断登录阶段（增强版 - 支持国家选择页面检测）
        保持向后兼容性
        """
        # 移除协议前缀
        clean_url = url.replace("https://", "").replace("http://", "")

        # 优先检查登录相关页面（避免被其他条件误判）
        if "/ap/signin" in clean_url and "sellercentral" in clean_url:
            if "?" in clean_url:
                # 有查询参数，通常需要点击继续按钮
                return "continue_page"
            else:
                # 没有查询参数，通常是登录页面
                return "signin_page"
        elif "/ap/mfa" in clean_url and "sellercentral" in clean_url:
            # MFA页面，需要第二次登录
            return "mfa_page"
        elif "account-switcher" in clean_url:
            # 国家选择页面
            return "country_selection"
        elif ("sellercentral" in clean_url and "/home" in clean_url) or \
             ("sellercentral" in clean_url and "/dashboard" in clean_url):
            # 已经在主页（必须是路径中的home/dashboard，不是参数中的）
            return "logged_in"
        else:
            # 未知页面
            return "unknown"

    def _handle_continue_page(self, driver):
        """
        处理需要点击继续按钮的页面
        """
        log_step("🔄 处理继续页面...")

        # 继续按钮选择器（ID优先策略）
        continue_id_selectors = [
            # ID选择器（优先使用）
            ("continue", "继续按钮"),
            ("signInSubmit", "登录提交按钮"),
            ("auth-signin-button", "登录按钮"),
            ("auth-continue-button", "继续按钮"),
        ]

        continue_xpath_selectors = [
            # XPath选择器（备选）
            ("//input[@id='continue' and @type='submit']", "继续按钮(input)"),
            ("//input[@aria-labelledby='continue-announce' and @type='submit']", "继续按钮(aria)"),
            ("//span[contains(text(), '继续')]/../input[@type='submit']", "继续按钮(文本定位)"),
            ("//span[@id='continue']//input[@type='submit']", "继续按钮(span内input)"),
            ("//input[@id='continue']", "继续按钮(input ID)"),
            ("//input[@type='submit' and contains(@class, 'a-button-input')]", "继续按钮(通用)")
        ]

        # 优先尝试ID定位
        for selector, button_name in continue_id_selectors:
            if self._click_login_button_by_id(driver, selector, button_name, 3):
                log_success(f"✅ 使用ID成功点击 {button_name}")
                return self._handle_login_redirect(driver, button_name)

        # 备选：使用XPath定位
        for selector, button_name in continue_xpath_selectors:
            if self._click_login_button_by_xpath(driver, selector, button_name, 3):
                log_success(f"✅ 使用XPath成功点击 {button_name}")
                return self._handle_login_redirect(driver, button_name)

        # 如果没找到继续按钮，尝试查找登录按钮
        log_warning("⚠️ 未找到继续按钮，尝试查找登录按钮...")
        return self._handle_signin_page(driver)

    def _handle_signin_page(self, driver):
        """
        处理登录页面
        """
        log_step("🔄 处理登录页面...")

        # 登录按钮选择器（ID优先策略）
        signin_id_selectors = [
            # ID选择器（优先使用）
            ("signInSubmit", "登录提交按钮"),
            ("auth-signin-button", "登录按钮"),
            ("signin-button", "登录按钮"),
            ("login-button", "登录按钮"),
        ]

        signin_xpath_selectors = [
            # XPath选择器（备选）
            ("//input[@id='signInSubmit' and @type='submit']", "登录按钮(input)"),
            ("//input[@aria-labelledby='auth-signin-button-announce' and @type='submit']", "登录按钮(aria)"),
            ("//span[contains(text(), '登录')]/../input[@type='submit']", "登录按钮(文本定位)"),
            ("//span[@id='auth-signin-button']//input[@type='submit']", "登录按钮(span内input)"),
            ("//input[@id='signInSubmit']", "登录按钮(input ID)"),
            ("//input[@type='submit' and contains(@class, 'a-button-input')]", "登录按钮(通用)")
        ]

        # 优先尝试ID定位
        for selector, button_name in signin_id_selectors:
            if self._click_login_button_by_id(driver, selector, button_name, 3):
                log_success(f"✅ 使用ID成功点击 {button_name}")
                return self._handle_login_redirect(driver, button_name)

        # 备选：使用XPath定位
        for selector, button_name in signin_xpath_selectors:
            if self._click_login_button_by_xpath(driver, selector, button_name, 3):
                log_success(f"✅ 使用XPath成功点击 {button_name}")
                return self._handle_login_redirect(driver, button_name)

        log_error("❌ 未找到可用的登录按钮")
        return False

    def _handle_mfa_page(self, driver):
        """
        处理MFA页面（第二次登录）
        """
        log_step("🔄 处理MFA页面（第二次登录）...")

        # MFA页面的登录按钮选择器（ID优先策略）
        mfa_id_selectors = [
            # ID选择器（优先使用）
            ("auth-signin-button", "MFA登录按钮"),
            ("auth-signin-button-announce", "MFA登录按钮"),
            ("mfa-submit-button", "MFA提交按钮"),
            ("signin-mfa-submit-button", "MFA登录提交按钮"),
        ]

        mfa_xpath_selectors = [
            # XPath选择器（备选）
            ("//input[@id='auth-signin-button' and @type='submit']", "二次登录按钮(input)"),
            ("//input[@name='mfaSubmit' and @type='submit']", "二次登录按钮(name)"),
            ("//input[@aria-labelledby='a-autoid-0-announce' and @type='submit']", "二次登录按钮(aria)"),
            ("//span[contains(text(), '登录') and @id='a-autoid-0-announce']/../input[@type='submit']", "二次登录按钮(文本定位)"),
            ("//span[contains(@class, 'auth-disable-button-on-submit')]//input[@type='submit']", "二次登录按钮(class定位)"),
            ("//input[@id='auth-signin-button']", "二次登录按钮(input ID)"),
            ("//input[@type='submit' and @name='mfaSubmit']", "二次登录按钮(通用)")
        ]

        # 优先尝试ID定位
        for selector, button_name in mfa_id_selectors:
            if self._click_login_button_by_id(driver, selector, button_name, 3):
                log_success(f"✅ 使用ID成功点击 {button_name}")
                return self._handle_login_redirect(driver, button_name)

        # 备选：使用XPath定位
        for selector, button_name in mfa_xpath_selectors:
            if self._click_login_button_by_xpath(driver, selector, button_name, 3):
                log_success(f"✅ 使用XPath成功点击 {button_name}")
                return self._handle_login_redirect(driver, button_name)

        log_error("❌ 未找到可用的MFA登录按钮")
        return False

    def _handle_unknown_login_page(self, driver):
        """
        处理未知的登录页面
        """
        log_step("🔄 处理未知登录页面...")

        current_url = driver.current_url
        log_warning(f"⚠️ 未识别的页面类型: {current_url}")

        # 尝试导航到日本站点主页
        log_step("🔄 尝试导航到日本站点主页...")
        driver.get("https://sellercentral-japan.amazon.com/home")
        time.sleep(3)

        # 重新检查登录状态
        if self.check_login_status(driver):
            log_success("✅ 导航后确认已登录")
            return True

        # 重新分析页面
        current_url = driver.current_url
        new_stage = self._determine_login_stage(current_url)
        if new_stage != "unknown":
            log_info(f"🔄 重新识别页面类型: {new_stage}")
            return self._handle_login_if_needed(driver)  # 递归处理

        log_error("❌ 无法处理当前页面")
        return False

    def _handle_country_selection_page(self, driver, target_country=None):
        """
        处理MAF登录后的国家选择页面

        :param driver: WebDriver实例
        :param target_country: 目标国家名称，如果为None则尝试获取当前任务的国家
        """
        try:
            log_step("🌍 检测到国家选择页面，开始自动处理...")

            # 等待页面完全加载
            time.sleep(3)

            # 确定目标国家
            if not target_country:
                target_country = self._get_current_task_country()

            if target_country:
                log_info(f"🎯 目标国家: {target_country}")

                # 查找目标国家元素
                target_element = self._find_target_country_element(driver, target_country)

                if target_element:
                    country_name = self._extract_country_name(target_element)
                    log_step(f"🌍 选择目标国家: {country_name}")

                    # 使用增强的点击方法
                    if self._enhanced_click_element(driver, target_element, f"国家选择-{country_name}"):
                        log_success(f"✅ 成功选择国家: {country_name}")

                        # 等待页面跳转
                        time.sleep(5)

                        # 验证是否成功跳转到主页
                        current_url = driver.current_url
                        if "account-switcher" not in current_url:
                            log_success("✅ 国家选择完成，已进入主页")
                            return True
                        else:
                            log_warning("⚠️ 国家选择后仍在选择页面")
                            return False
                    else:
                        log_error("❌ 点击目标国家失败")
                        return False
                else:
                    log_warning(f"⚠️ 未找到目标国家 {target_country}，尝试选择默认国家（日本）")
                    return self._select_default_country(driver)
            else:
                log_warning("⚠️ 无法确定目标国家，选择默认国家（日本）")
                return self._select_default_country(driver)

        except Exception as e:
            log_error(f"❌ 处理国家选择页面时发生错误: {str(e)}")
            return False

    def _get_current_task_country(self):
        """
        获取当前任务正在使用的国家站点
        """
        try:
            # 尝试从任务上下文中获取当前国家
            if hasattr(self, 'current_country') and self.current_country:
                log_info(f"📍 从任务上下文获取国家: {self.current_country}")
                return self.current_country

            # 尝试从类属性中获取
            if hasattr(self, 'target_country') and self.target_country:
                log_info(f"📍 从目标国家属性获取: {self.target_country}")
                return self.target_country

            # 如果都没有，返回None，让调用方处理
            log_warning("⚠️ 无法从任务上下文获取当前国家")
            return None

        except Exception as e:
            log_error(f"❌ 获取当前任务国家时发生错误: {str(e)}")
            return None

    def _find_target_country_element(self, driver, target_country):
        """
        查找指定目标国家的元素

        :param driver: WebDriver实例
        :param target_country: 目标国家名称
        :return: 找到的国家元素或None
        """
        try:
            log_step(f"🔍 查找目标国家元素: {target_country}")

            # 获取所有国家元素
            all_country_elements = self._find_country_elements_with_id_priority(driver)

            if not all_country_elements:
                log_error("❌ 未找到任何国家元素")
                return None

            # 在所有国家元素中查找目标国家
            for element in all_country_elements:
                try:
                    country_name = self._extract_country_name(element)
                    log_info(f"📍 检查国家元素: {country_name}")

                    # 检查是否匹配目标国家（支持模糊匹配）
                    if self._is_country_match(country_name, target_country):
                        log_success(f"✅ 找到目标国家元素: {country_name}")
                        return element

                except Exception as e:
                    log_warning(f"⚠️ 检查国家元素时出错: {str(e)}")
                    continue

            log_warning(f"⚠️ 未找到匹配的目标国家: {target_country}")
            return None

        except Exception as e:
            log_error(f"❌ 查找目标国家元素时发生错误: {str(e)}")
            return None

    def _is_country_match(self, country_name, target_country):
        """
        判断国家名称是否匹配目标国家（支持模糊匹配）

        :param country_name: 页面上的国家名称
        :param target_country: 目标国家名称
        :return: 是否匹配
        """
        try:
            if not country_name or not target_country:
                return False

            # 转换为小写进行比较
            country_lower = country_name.lower().strip()
            target_lower = target_country.lower().strip()

            # 精确匹配
            if country_lower == target_lower:
                return True

            # 包含匹配
            if target_lower in country_lower or country_lower in target_lower:
                return True

            # 常见国家名称映射
            country_mappings = {
                'us': ['美国', 'united states', 'usa', 'america'],
                'uk': ['英国', 'united kingdom', 'britain', 'great britain'],
                'de': ['德国', 'germany', 'deutschland'],
                'fr': ['法国', 'france'],
                'it': ['意大利', 'italy'],
                'es': ['西班牙', 'spain'],
                'jp': ['日本', 'japan'],
                'ca': ['加拿大', 'canada'],
                'au': ['澳大利亚', 'australia'],
                'in': ['印度', 'india'],
                'mx': ['墨西哥', 'mexico'],
                'br': ['巴西', 'brazil'],
            }

            # 检查映射匹配
            for code, names in country_mappings.items():
                if (target_lower == code or target_lower in names) and \
                   (country_lower == code or country_lower in names):
                    return True

            return False

        except Exception as e:
            log_error(f"❌ 判断国家匹配时发生错误: {str(e)}")
            return False

    def _select_default_country(self, driver):
        """
        选择默认国家（优先选择日本）
        """
        try:
            log_step("🌍 选择默认国家（优先日本）")

            country_elements = self._find_country_elements_with_id_priority(driver)

            if country_elements:
                log_info(f"🎯 找到 {len(country_elements)} 个国家选项")

                # 优先查找日本
                japan_element = None
                for element in country_elements:
                    country_name = self._extract_country_name(element)
                    if self._is_country_match("日本", country_name):
                        japan_element = element
                        log_info(f"✅ 找到日本站点: {country_name}")
                        break

                # 选择日本或第一个可用国家
                if japan_element:
                    selected_element = japan_element
                    country_name = self._extract_country_name(selected_element)
                    log_step(f"🇯🇵 选择默认国家: {country_name}")
                else:
                    selected_element = country_elements[0]
                    country_name = self._extract_country_name(selected_element)
                    log_step(f"🌍 日本不可用，选择第一个国家: {country_name}")

                # 使用增强的点击方法
                if self._enhanced_click_element(driver, selected_element, f"国家选择-{country_name}"):
                    log_success(f"✅ 成功选择国家: {country_name}")

                    # 等待页面跳转
                    time.sleep(5)

                    # 验证是否成功跳转到主页
                    current_url = driver.current_url
                    if "account-switcher" not in current_url:
                        log_success("✅ 国家选择完成，已进入主页")
                        return True
                    else:
                        log_warning("⚠️ 国家选择后仍在选择页面")
                        return False
                else:
                    log_error("❌ 点击国家选择失败")
                    return False
            else:
                log_error("❌ 未找到可选择的国家元素")
                return False

        except Exception as e:
            log_error(f"❌ 选择默认国家时发生错误: {str(e)}")
            return False

    def _select_first_available_country(self, driver):
        """
        选择第一个可用的国家（备选方案）
        """
        try:
            log_step("🌍 选择第一个可用国家作为备选方案")

            country_elements = self._find_country_elements_with_id_priority(driver)

            if country_elements:
                log_info(f"🎯 找到 {len(country_elements)} 个国家选项")

                # 选择第一个可用的国家
                first_country = country_elements[0]
                country_name = self._extract_country_name(first_country)
                log_step(f"🌍 自动选择第一个国家: {country_name}")

                # 使用增强的点击方法
                if self._enhanced_click_element(driver, first_country, f"国家选择-{country_name}"):
                    log_success(f"✅ 成功选择国家: {country_name}")

                    # 等待页面跳转
                    time.sleep(5)

                    # 验证是否成功跳转到主页
                    current_url = driver.current_url
                    if "account-switcher" not in current_url:
                        log_success("✅ 国家选择完成，已进入主页")
                        return True
                    else:
                        log_warning("⚠️ 国家选择后仍在选择页面")
                        return False
                else:
                    log_error("❌ 点击国家选择失败")
                    return False
            else:
                log_error("❌ 未找到可选择的国家元素")
                return False

        except Exception as e:
            log_error(f"❌ 选择第一个可用国家时发生错误: {str(e)}")
            return False

    def _find_country_elements_with_id_priority(self, driver):
        """
        使用ID优先的策略查找国家元素
        """
        try:
            # 策略1: 优先使用ID定位
            id_selectors = [
                "country-selector-item-0",
                "country-selector-item-1",
                "country-selector-item-2",
                "account-switcher-country-0",
                "account-switcher-country-1"
            ]

            for selector in id_selectors:
                try:
                    elements = driver.find_elements(By.ID, selector)
                    if elements:
                        log_success(f"✅ 使用ID定位找到国家元素: {selector}")
                        return elements
                except:
                    continue

            # 策略2: 使用class定位（备选）
            class_selectors = [
                "full-page-account-switcher-account-label",
                "account-switcher-item",
                "country-selection-item"
            ]

            for selector in class_selectors:
                try:
                    elements = driver.find_elements(By.CLASS_NAME, selector)
                    if elements:
                        log_info(f"📍 使用CLASS定位找到国家元素: {selector}")
                        return elements
                except:
                    continue

            # 策略3: 使用XPath定位（最后备选）
            xpath_selectors = [
                "//span[contains(@class, 'full-page-account-switcher-account-label')]",
                "//div[contains(@class, 'account-switcher')]//span[contains(text(), '美国') or contains(text(), '英国') or contains(text(), '德国')]"
            ]

            for selector in xpath_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    if elements:
                        log_info(f"📍 使用XPATH定位找到国家元素")
                        return elements
                except:
                    continue

            return []

        except Exception as e:
            log_error(f"❌ 查找国家元素时发生错误: {str(e)}")
            return []

    def _extract_country_name(self, element):
        """
        从国家元素中提取国家名称
        """
        try:
            # 尝试多种方式获取国家名称
            text_methods = [
                lambda: element.text,
                lambda: element.get_attribute("textContent"),
                lambda: element.get_attribute("innerText"),
                lambda: element.get_attribute("title")
            ]

            for method in text_methods:
                try:
                    text = method()
                    if text and text.strip():
                        return text.strip()
                except:
                    continue

            return "未知国家"

        except Exception as e:
            log_error(f"❌ 提取国家名称时发生错误: {str(e)}")
            return "未知国家"

    def _enhanced_click_element(self, driver, element, element_name):
        """
        增强的元素点击方法，优先使用ID定位策略
        """
        try:
            log_step(f"🎯 尝试点击元素: {element_name}")

            # 策略1: 检查元素是否有ID，优先使用ID重新定位
            element_id = element.get_attribute("id")
            if element_id:
                try:
                    log_info(f"📍 使用ID重新定位元素: {element_id}")
                    fresh_element = driver.find_element(By.ID, element_id)
                    if fresh_element.is_displayed() and fresh_element.is_enabled():
                        fresh_element.click()
                        log_success(f"✅ 使用ID定位成功点击: {element_name}")
                        return True
                except Exception as e:
                    log_warning(f"⚠️ ID定位点击失败: {str(e)}")

            # 策略2: 使用原始元素直接点击
            try:
                if element.is_displayed() and element.is_enabled():
                    element.click()
                    log_success(f"✅ 直接点击成功: {element_name}")
                    return True
            except Exception as e:
                log_warning(f"⚠️ 直接点击失败: {str(e)}")

            # 策略3: 使用JavaScript点击
            try:
                driver.execute_script("arguments[0].click();", element)
                log_success(f"✅ JavaScript点击成功: {element_name}")
                return True
            except Exception as e:
                log_warning(f"⚠️ JavaScript点击失败: {str(e)}")

            # 策略4: 滚动到元素后点击
            try:
                driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element)
                time.sleep(1)
                element.click()
                log_success(f"✅ 滚动后点击成功: {element_name}")
                return True
            except Exception as e:
                log_warning(f"⚠️ 滚动后点击失败: {str(e)}")

            log_error(f"❌ 所有点击策略都失败了: {element_name}")
            return False

        except Exception as e:
            log_error(f"❌ 增强点击方法发生错误: {str(e)}")
            return False

    def _debug_page_elements(self, driver, page_name):
        """调试页面元素结构，特别用于检测无头模式下的差异"""
        try:
            log_info(f"🔍 调试 {page_name} 页面元素...")

            # 检查常见的登录相关元素
            login_elements = [
                ("signInSubmit", "登录提交按钮"),
                ("auth-signin-button-announce", "登录按钮"),
                ("auth-signin-button", "第二个登录按钮"),
                ("continue", "继续按钮"),
                ("ap_email", "邮箱输入框"),
                ("ap_password", "密码输入框")
            ]

            found_elements = []
            for element_id, element_name in login_elements:
                try:
                    element = driver.find_element(By.ID, element_id)
                    is_displayed = element.is_displayed()
                    is_enabled = element.is_enabled()
                    found_elements.append(f"{element_name}(显示:{is_displayed},启用:{is_enabled})")
                except:
                    found_elements.append(f"{element_name}(未找到)")

            log_info(f"🔍 页面元素状态: {', '.join(found_elements)}")

            # 检查页面源码中的关键信息
            try:
                page_source = driver.page_source
                # 检查是否包含MFA相关内容
                if 'mfa' in page_source.lower():
                    log_warning(f"⚠️ 页面源码包含MFA相关内容")
                if 'two-factor' in page_source.lower():
                    log_warning(f"⚠️ 页面源码包含二次验证相关内容")
                if 'captcha' in page_source.lower():
                    log_warning(f"⚠️ 页面源码包含验证码相关内容")

                # 检查用户代理检测
                if 'headless' in page_source.lower():
                    log_warning(f"⚠️ 页面可能检测到无头模式")

            except Exception as e:
                log_warning(f"⚠️ 无法检查页面源码: {str(e)}")

        except Exception as e:
            log_warning(f"⚠️ 页面元素调试失败: {str(e)}")


    def _click_button_if_exists(self, driver, element_id, button_name, timeout=3):
        """
        如果按钮存在则点击

        :param driver: WebDriver实例
        :param element_id: 元素ID
        :param button_name: 按钮名称（用于日志）
        :param timeout: 超时时间
        :return: 是否成功点击
        """
        try:
            # 先尝试常规点击
            element = WebDriverWait(driver, timeout).until(
                EC.element_to_be_clickable((By.ID, element_id))
            )
            element.click()
            log_step(f"点击了{button_name}")
            return True
        except TimeoutException:
            log_step(f"未找到{button_name}")
            return False
        except Exception as e:
            # 如果常规点击失败，尝试JavaScript点击
            try:
                element = driver.find_element(By.ID, element_id)
                driver.execute_script("arguments[0].click();", element)
                log_step(f"使用JavaScript点击了{button_name}")
                return True
            except Exception as js_e:
                log_step(f"点击{button_name}失败: {str(e)}")
                return False

    def _click_login_button_by_id(self, driver, element_id, button_name, timeout=3):
        """
        专门用于登录处理的ID优先按钮点击方法

        :param driver: WebDriver实例
        :param element_id: 元素ID
        :param button_name: 按钮名称（用于日志）
        :param timeout: 超时时间
        :return: 是否成功点击
        """
        try:
            log_info(f"🎯 使用ID定位登录按钮: {element_id}")

            # 策略1: 等待元素可点击并点击
            try:
                element = WebDriverWait(driver, timeout).until(
                    EC.element_to_be_clickable((By.ID, element_id))
                )
                element.click()
                log_success(f"✅ ID定位成功点击{button_name}")
                return True
            except TimeoutException:
                log_info(f"📍 ID定位超时: {element_id}")
                return False
            except Exception as e:
                log_warning(f"⚠️ ID定位点击失败: {str(e)}")

                # 策略2: 查找元素并使用JavaScript点击
                try:
                    element = driver.find_element(By.ID, element_id)
                    if element:
                        driver.execute_script("arguments[0].click();", element)
                        log_success(f"✅ ID定位JavaScript点击成功{button_name}")
                        return True
                except Exception as js_e:
                    log_warning(f"⚠️ ID定位JavaScript点击失败: {str(js_e)}")
                    return False

        except Exception as e:
            log_error(f"❌ ID定位点击{button_name}时发生错误: {str(e)}")
            return False

    def _click_login_button_by_xpath(self, driver, xpath, button_name, timeout=3):
        """
        专门用于登录处理的XPath按钮点击方法（备选）

        :param driver: WebDriver实例
        :param xpath: XPath选择器
        :param button_name: 按钮名称（用于日志）
        :param timeout: 超时时间
        :return: 是否成功点击
        """
        try:
            log_info(f"📍 使用XPath定位登录按钮: {xpath}")

            # 策略1: 等待元素可点击并点击
            try:
                element = WebDriverWait(driver, timeout).until(
                    EC.element_to_be_clickable((By.XPATH, xpath))
                )
                element.click()
                log_success(f"✅ XPath定位成功点击{button_name}")
                return True
            except TimeoutException:
                log_info(f"📍 XPath定位超时")
                return False
            except Exception as e:
                log_warning(f"⚠️ XPath定位点击失败: {str(e)}")

                # 策略2: 查找元素并使用JavaScript点击
                try:
                    element = driver.find_element(By.XPATH, xpath)
                    if element:
                        driver.execute_script("arguments[0].click();", element)
                        log_success(f"✅ XPath定位JavaScript点击成功{button_name}")
                        return True
                except Exception as js_e:
                    log_warning(f"⚠️ XPath定位JavaScript点击失败: {str(js_e)}")
                    return False

        except Exception as e:
            log_error(f"❌ XPath定位点击{button_name}时发生错误: {str(e)}")
            return False

    def _handle_login_redirect(self, driver, button_name):
        """
        处理登录按钮点击后的页面跳转

        :param driver: WebDriver实例
        :param button_name: 按钮名称
        :return: 是否成功处理
        """
        try:
            # 等待页面跳转
            time.sleep(5)
            current_url = driver.current_url
            log_info(f"📍 点击{button_name}后URL: {current_url}")

            # 检查跳转后的页面类型
            new_stage = self._determine_login_stage(current_url)
            if new_stage == "logged_in":
                log_success("✅ 登录成功，已进入主页")
                return True
            elif new_stage == "country_selection":
                log_info("🌍 进入国家选择页面，继续处理...")
                current_country = getattr(self, 'current_country', None)
                return self._handle_country_selection_page(driver, current_country)
            elif new_stage == "mfa_page":
                log_info("🔄 进入MFA页面，继续处理...")
                return self._handle_mfa_page(driver)
            elif new_stage == "signin_page":
                log_info("🔄 仍在登录页面，继续处理...")
                return self._handle_signin_page(driver)
            else:
                # 等待更长时间，可能页面还在跳转
                log_info("⏳ 等待页面完全跳转...")
                for wait_count in range(10):
                    time.sleep(1)
                    current_url = driver.current_url
                    new_stage = self._determine_login_stage(current_url)
                    if new_stage == "logged_in":
                        log_success("✅ 登录跳转完成，已进入主页")
                        return True
                    elif new_stage == "country_selection":
                        current_country = getattr(self, 'current_country', None)
                        return self._handle_country_selection_page(driver, current_country)
                    elif new_stage == "mfa_page":
                        return self._handle_mfa_page(driver)

                log_warning(f"⚠️ {button_name}点击后页面未按预期跳转: {current_url}")
                return False

        except Exception as e:
            log_error(f"❌ 处理{button_name}点击后跳转时发生错误: {str(e)}")
            return False

    def check_webdriver_health(self, driver) -> bool:
        """
        检查WebDriver健康状态
        
        Returns:
            bool: True表示健康，False表示需要重建连接
        """
        try:
            # 检查会话是否有效
            _ = driver.current_url
            
            # 检查浏览器进程是否响应
            _ = driver.title
            
            # 尝试执行简单的JavaScript来验证页面交互能力
            driver.execute_script("return document.readyState;")
            
            return True
            
        except InvalidSessionIdException:
            log_warning("WebDriver会话已失效")
            return False
        except WebDriverException as e:
            error_str = str(e).lower()
            if any(keyword in error_str for keyword in ["chrome not reachable", "session deleted", "tab crashed", "browser crashed"]):
                log_warning(f"Chrome浏览器崩溃或不可达: {str(e)}")
                return False
            log_warning(f"WebDriver健康检查异常: {str(e)}")
            return False
        except Exception as e:
            log_warning(f"WebDriver健康检查未知错误: {str(e)}")
            return False

    def recover_from_webdriver_crash(self, crashed_driver, store_info, country="", recovery_context=None, isHeadless=0):
        """
        从WebDriver崩溃中恢复，支持状态恢复
        
        Args:
            crashed_driver: 崩溃的WebDriver实例
            store_info: 店铺信息
            country: 当前处理的国家
            recovery_context: 恢复上下文，包含之前的状态信息
            
        Returns:
            WebDriver: 恢复后的WebDriver实例，失败返回None
        """
        try:
            log_step(f"{country}: 开始WebDriver崩溃恢复流程...")
            
            # 1. 清理崩溃的WebDriver
            try:
                if crashed_driver:
                    crashed_driver.quit()
                    log_info(f"{country}: 已清理崩溃的WebDriver实例")
            except:
                log_info(f"{country}: 崩溃的WebDriver实例已无法访问")
            
            # 2. 等待一段时间让系统资源释放
            time.sleep(3)
            
            # 3. 重新启动浏览器实例
            log_step(f"{country}: 重新启动浏览器实例...")
            
            # 获取店铺的browserOauth
            browser_oauth = store_info.get('browserOauth') if isinstance(store_info, dict) else store_info
            
            # 关闭可能存在的旧浏览器实例
            try:
                self.close_store(browser_oauth)
                time.sleep(2)
            except:
                pass
            
            # 重新打开浏览器
            open_ret = self.open_store(browser_oauth)
            if not open_ret:
                log_error(f"{country}: 重新启动浏览器失败")
                return None
            
            # 4. 获取新的WebDriver实例
            log_step(f"{country}: 获取新的WebDriver实例...")
            new_driver = self.get_driver(open_ret, isHeadless=isHeadless)
            if not new_driver:
                log_error(f"{country}: 获取新WebDriver实例失败")
                return None
            
            # 5. 设置基本配置
            new_driver.implicitly_wait(5)
            
            # 6. 验证新WebDriver的健康状态
            if not self.check_webdriver_health(new_driver):
                log_error(f"{country}: 新WebDriver实例健康检查失败")
                try:
                    new_driver.quit()
                except:
                    pass
                return None
            
            # 7. 根据恢复上下文导航到相应页面
            if recovery_context and recovery_context.get('current_page'):
                log_step(f"{country}: 恢复页面状态到: {recovery_context['current_page']}")
                success = self._restore_page_state(new_driver, recovery_context, country)
                if not success:
                    log_warning(f"{country}: 页面状态恢复失败，导航到主页")
                    self.navigate_to_seller_central_home(new_driver)
            else:
                # 默认导航到主页
                log_step(f"{country}: 导航到商铺主页...")
                if not self.navigate_to_seller_central_home(new_driver):
                    log_warning(f"{country}: 导航到主页失败，但WebDriver可用")
            
            log_success(f"{country}: WebDriver崩溃恢复成功")
            return new_driver
            
        except Exception as e:
            log_error(f"{country}: WebDriver崩溃恢复过程中发生错误: {str(e)}")
            return None

    def _restore_page_state(self, driver, recovery_context, country):
        """
        恢复页面状态
        
        Args:
            driver: WebDriver实例
            recovery_context: 恢复上下文
            country: 国家名称
            
        Returns:
            bool: 恢复是否成功
        """
        try:
            current_page = recovery_context.get('current_page', '')
            
            if current_page == 'shipping_settings':
                log_step(f"{country}: 恢复运输设置页面状态...")
                # 导航到运输设置页面
                return self._navigate_to_shipping_settings_page(driver, country)
            elif current_page == 'fba_settings':
                log_step(f"{country}: 恢复FBA设置页面状态...")
                # 导航到FBA设置页面
                return self._navigate_to_fba_settings_page(driver, country)
            elif current_page == 'return_settings':
                log_step(f"{country}: 恢复退货设置页面状态...")
                # 导航到退货设置页面
                return self._navigate_to_return_settings_page(driver, country)
            else:
                log_warning(f"{country}: 未知的页面状态: {current_page}")
                return False
                
        except Exception as e:
            log_error(f"{country}: 恢复页面状态时发生错误: {str(e)}")
            return False

    def _navigate_to_shipping_settings_page(self, driver, country):
        """导航到运输设置页面"""
        try:
            # 这里需要实现具体的导航逻辑
            # 暂时返回True，具体实现由子类重写
            log_info(f"{country}: 运输设置页面导航逻辑需要子类实现")
            return True
        except Exception as e:
            log_error(f"{country}: 导航到运输设置页面失败: {str(e)}")
            return False

    def _navigate_to_fba_settings_page(self, driver, country):
        """导航到FBA设置页面"""
        try:
            # 这里需要实现具体的导航逻辑
            # 暂时返回True，具体实现由子类重写
            log_info(f"{country}: FBA设置页面导航逻辑需要子类实现")
            return True
        except Exception as e:
            log_error(f"{country}: 导航到FBA设置页面失败: {str(e)}")
            return False

    def _navigate_to_return_settings_page(self, driver, country):
        """导航到退货设置页面"""
        try:
            # 这里需要实现具体的导航逻辑
            # 暂时返回True，具体实现由子类重写
            log_info(f"{country}: 退货设置页面导航逻辑需要子类实现")
            return True
        except Exception as e:
            log_error(f"{country}: 导航到退货设置页面失败: {str(e)}")
            return False

    def execute_with_crash_recovery(self, operation_func, driver, store_info, country="", max_recovery_attempts=2, recovery_context=None):
        """
        执行操作并在崩溃时自动恢复，支持状态恢复
        
        Args:
            operation_func: 要执行的操作函数，接收driver作为第一个参数
            driver: WebDriver实例
            store_info: 店铺信息
            country: 国家名称
            max_recovery_attempts: 最大恢复尝试次数
            recovery_context: 恢复上下文，包含页面状态信息
            
        Returns:
            tuple: (success: bool, result: any, recovered_driver: WebDriver)
        """
        recovery_attempts = 0
        current_driver = driver
        
        while recovery_attempts <= max_recovery_attempts:
            try:
                # 执行操作
                result = operation_func(current_driver)
                return True, result, current_driver
                
            except Exception as e:
                error_str = str(e).lower()
                
                # 检查是否是崩溃相关错误
                if any(keyword in error_str for keyword in ["tab crashed", "browser crashed", "chrome not reachable", "session deleted"]):
                    if recovery_attempts < max_recovery_attempts:
                        log_warning(f"{country}: 检测到WebDriver崩溃，尝试第{recovery_attempts + 1}次恢复...")
                        
                        # 尝试恢复
                        recovered_driver = self.recover_from_webdriver_crash(current_driver, store_info, country, recovery_context)
                        if recovered_driver:
                            current_driver = recovered_driver
                            recovery_attempts += 1
                            log_info(f"{country}: WebDriver恢复成功，重新尝试操作...")
                            continue
                        else:
                            log_error(f"{country}: WebDriver恢复失败")
                            return False, None, current_driver
                    else:
                        log_error(f"{country}: 已达到最大恢复尝试次数({max_recovery_attempts})，操作失败")
                        return False, None, current_driver
                else:
                    # 非崩溃错误，直接抛出
                    raise e
        
        return False, None, current_driver

    def wait_for_page_ready(self, driver, timeout: float = 10.0) -> bool:
        """
        等待页面就绪，增加崩溃检测和容错处理
        """
        try:
            log_info("🔄 等待页面就绪...")
            
            # 首先检查WebDriver是否还活着
            try:
                # 使用一个轻量级的JavaScript检查
                driver.execute_script("return document.readyState;")
            except Exception as e:
                if "tab crashed" in str(e).lower():
                    log_error("检测到标签页崩溃，页面状态检查终止")
                    return False
                else:
                    log_warning(f"WebDriver检查失败: {str(e)}")
                    return False
            
            # 使用更轻量级的页面就绪检查
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    # 检查页面基本状态
                    ready_state = driver.execute_script("return document.readyState;")
                    if ready_state == "complete":
                        log_success("✅ 页面已完全加载")
                        return True
                    
                    # 检查是否有错误页面
                    page_title = driver.title
                    if "error" in page_title.lower() or "crash" in page_title.lower():
                        log_error(f"检测到错误页面: {page_title}")
                        return False
                    
                    time.sleep(0.5)
                    
                except Exception as e:
                    if "tab crashed" in str(e).lower():
                        log_error("检测到标签页崩溃，页面状态检查终止")
                        return False
                    else:
                        log_warning(f"页面状态检查出错: {str(e)}")
                        time.sleep(0.5)
                        continue
            
            log_warning(f"等待页面就绪失败: 超时 {timeout}秒")
            return False
            
        except Exception as e:
            log_error(f"页面就绪检查异常: {str(e)}")
            return False

    def crash_recovery_decorator(self, operation_name=""):
        """
        通用崩溃恢复装饰器
        用于包装任何可能发生崩溃的操作
        
        Args:
            operation_name: 操作名称，用于日志记录
            
        Returns:
            decorator: 装饰器函数
        """
        def decorator(func):
            def wrapper(*args, **kwargs):
                # 提取driver参数（通常是第一个参数）
                driver = None
                country = ""
                store_info = None
                
                # 从参数中提取关键信息
                if len(args) > 1:
                    driver = args[1]  # 通常是第二个参数
                if len(args) > 2:
                    country = args[2]  # 通常是第三个参数
                if len(args) > 3:
                    store_info = args[3]  # 通常是第四个参数
                
                # 如果没有从位置参数获取，尝试从kwargs获取
                if not driver and 'driver' in kwargs:
                    driver = kwargs['driver']
                if not country and 'country' in kwargs:
                    country = kwargs['country']
                if not store_info and 'store_info' in kwargs:
                    store_info = kwargs['store_info']
                
                # 使用现有的崩溃恢复机制
                def operation_func(current_driver):
                    # 创建新的参数列表，正确更新driver参数
                    new_args = list(args)
                    new_kwargs = kwargs.copy()
                    
                    # 更新driver参数
                    if len(new_args) > 1:
                        new_args[1] = current_driver
                    elif 'driver' in new_kwargs:
                        new_kwargs['driver'] = current_driver
                    else:
                        # 如果没有driver参数，添加一个
                        new_kwargs['driver'] = current_driver
                    
                    return func(*new_args, **new_kwargs)
                
                try:
                    success, result, recovered_driver = self.execute_with_crash_recovery(
                        operation_func,
                        driver,
                        store_info,
                        country,
                        recovery_context=kwargs.get('recovery_context', None)
                    )
                    
                    if success:
                        log_success(f"{country}: {operation_name} 执行成功")
                        return result
                    else:
                        log_error(f"{country}: {operation_name} 在崩溃恢复后仍然失败")
                        return False
                        
                except Exception as e:
                    log_error(f"{country}: {operation_name} 执行异常: {str(e)}")
                    return False
                    
            return wrapper
        return decorator

    def execute_store_task(self, driver, selected_countries, upload_file_path, store_info):
        """
        在指定店铺执行任务（优化流程：智能URL判断 + 主任务 + 可选的国家遍历）

        :param driver: WebDriver实例
        :param selected_countries: 选择的国家列表
        :param upload_file_path: 上传文件路径
        :param store_info: 店铺信息
        """
        log_step(f"开始在店铺 {store_info['browserName']} 执行任务")

        # 执行前置钩子（不再强制导航）
        self.pre_execute_hook(driver, store_info)

        # 1. 打开店铺后先判断URL状态
        current_url = driver.current_url
        url_stage = self._determine_url_stage(current_url)
        log_info(f"当前URL阶段: {url_stage}, URL: {current_url}")

        # 2. 根据URL阶段执行相应的登录处理
        login_success = True
        if url_stage == "unregistered_site":
            log_warning("⚠️ 检测到站点未注册，导航到日本站点首页...")
            driver.get("https://sellercentral-japan.amazon.com/home")
            time.sleep(3)
        elif url_stage == "continue_page":
            log_step("检测到continue页面，执行继续按钮点击...")
            login_success = self._handle_continue_page(driver)
        elif url_stage == "signin_page":
            log_step("检测到signin页面，执行登录处理...")
            login_success = self._handle_signin_page(driver)
        elif url_stage == "mfa_page":
            log_step("检测到MFA页面，执行MFA验证...")
            login_success = self._handle_mfa_page(driver)
        elif url_stage == "country_selection":
            log_step("检测到国家切换页面，执行国家切换...")
            current_country = getattr(self, 'current_country', "日本")
            login_success = self.switch_country(driver, current_country)
        else:
            log_step("URL状态正常，直接进入主任务")

        # 3. 检查登录/切换是否成功
        if not login_success:
            log_error("登录或国家切换失败，跳过此店铺")
            self.record_country_result(store_info['browserName'], selected_countries, False,f"登录或国家切换失败，跳过此店铺")
            return

        # 4. 执行子类自定义的主要任务
        log_step("开始执行主任务...")
        main_task_success = self.execute_main_task(driver, upload_file_path, store_info)

        # 5. 如果主任务失败，尝试兜底方案
        if not main_task_success:
            log_warning("主任务执行失败，尝试兜底方案...")
            log_step("导航到日本站点首页...")
            driver.get("https://sellercentral-japan.amazon.com/home")
            time.sleep(5)

            # 重新尝试主任务
            log_step("重新执行主任务...")
            main_task_success = self.execute_main_task(driver, upload_file_path, store_info)

        if main_task_success:
            log_success("主任务执行成功")
            self.record_country_result(store_info['browserName'], selected_countries, True,f"主任务（退货设置）执行成功")
        else:
            log_error("主任务执行失败")
            self.record_country_result(store_info['browserName'], selected_countries, False,f"主任务（退货设置）执行失败")

        # 6. 根据子类设定决定是否进入国家遍历逻辑
        if self.requires_country_iteration():
            log_step("根据任务配置，开始执行国家遍历任务...")
            self._execute_countries_iteration(driver, selected_countries, upload_file_path, store_info)
        else:
            log_info("根据任务配置，跳过国家遍历")

        # 执行后置钩子
        self.post_execute_hook(driver, store_info)

        log_step(f"店铺 {store_info['browserName']} 所有任务执行完成")

    def _execute_countries_iteration(self, driver, selected_countries, upload_file_path, store_info):
        """
        执行国家遍历任务（原有逻辑）
        
        :param driver: WebDriver实例
        :param selected_countries: 选择的国家列表
        :param upload_file_path: 上传文件路径
        :param store_info: 店铺信息
        """
        log_step(f"开始在店铺 {store_info['browserName']} 执行 {len(selected_countries)} 个国家的任务")
        
        success_count = 0
        for i, country in enumerate(selected_countries, 1):
            
            log_step(f"处理国家 {i}/{len(selected_countries)}: {country}")

            # 设置当前国家，确保在遇到account-switcher页面时能正确处理
            self.current_country = country
            log_info(f"📍 设置当前目标国家: {country}")

            # 导航到账户切换页面（使用日本站点）
            log_step("导航到账户切换页面...")
            driver.get("https://sellercentral-japan.amazon.com/account-switcher/default/merchantMarketplace")
            
            # 使用智能等待替代固定延迟
            log_step("等待账户切换页面完全加载...")
            if not self.wait_for_page_ready(driver, timeout=15.0):
                log_warning("账户切换页面加载可能未完成，但继续执行")
            try:
                # 判断当前url是否包含account-switcher（额外保险检查）
                current_url = driver.current_url
                if "account-switcher" not in current_url:
                    # 可能跳到登录页或其他页面，处理登录
                    log_warning(f"当前URL不包含account-switcher: {current_url}")
                    if not self._handle_login_if_needed(driver):
                        log_error(f"国家 {country} 登录失败，跳过此国家")
                        continue
                else:
                    log_info(f"确认在account-switcher页面: {current_url}")
                # 切换到指定国家
                if self.switch_country(driver, country):
                    # 执行具体任务
                    if self.execute_country_task(driver, country, upload_file_path, store_info):
                        success_count += 1
                        log_success(f"国家 {country} 任务执行成功")
                    else:
                        log_error(f"国家 {country} 任务执行失败")
                else:
                    self.record_country_result(store_info['browserName'], country, False, "切换国家失败", "切换国家失败跳过该国家")
                    log_error(f"切换到国家 {country} 失败")
                    
            except Exception as e:
                error_msg = str(e)
                log_error(f"处理国家 {country} 时发生异常: {error_msg}")
                
                # 检查是否是tab crashed错误，如果是则标记后续国家为失败
                if "tab crashed" in error_msg.lower():
                    log_error(f"检测到标签页崩溃，将剩余未完成的国家标记为失败")
                    remaining_countries = selected_countries[i:]  # 获取剩余未处理的国家
                    for remaining_country in remaining_countries:
                        log_error(f"国家 {remaining_country} 因崩溃未完成，标记为失败")
                    self.record_store_level_failure(store_info['browserName'], remaining_countries, False, f"因页面崩溃未完成")
                    break  # 跳出循环，不再处理剩余国家
                else:
                    self.record_country_result(store_info['browserName'], country, False, "其他错误", error_msg)
                    log_error(f"国家 {country} 因其他错误失败")
        
        log_step(f"店铺 {store_info['browserName']} 国家遍历任务完成: {success_count}/{len(selected_countries)} 个国家成功")

    def use_one_browser_run_task(self, browser, selected_countries, upload_file_path):
        """
        使用单个浏览器执行任务
        
        :param browser: 浏览器配置信息
        :param selected_countries: 选择的国家列表
        :param upload_file_path: 上传文件路径
        """
        browser_oauth = browser['browserOauth']
        browser_name = browser['browserName']
        
        log_task_start(f"店铺 {browser_name} 任务")
        
        try:
            # 检查任务控制状态
            if not self.check_task_control():
                log_warning("任务被停止，跳过店铺处理")
                return
            
            # 打开浏览器
            log_step(f"正在启动店铺 {browser_name} 的浏览器...")
            open_ret = self.open_store(browser_oauth)
            
            if not open_ret:
                log_error(f"启动店铺 {browser_name} 失败")
                return
            
            log_success(f"店铺 {browser_name} 浏览器启动成功")
            
            # 再次检查任务控制状态
            if not self.check_task_control():
                log_warning("任务被停止，关闭浏览器")
                self.close_store(browser_oauth)
                return
            
            # 获取WebDriver
            log_step("正在初始化WebDriver...")
            driver = self.get_driver(open_ret)
            
            if not driver:
                log_error("WebDriver初始化失败")
                self.close_store(browser_oauth)
                return
                
            log_success("WebDriver初始化成功")
            
            try:
                # 设置元素查找等待时间-全局隐式等待
                driver.implicitly_wait(5)
                
                # 执行店铺任务（新流程）
                self.execute_store_task(driver, selected_countries, upload_file_path, browser)
                
                log_success(f"店铺 {browser_name} 所有任务执行完成")
                
            except Exception as e:
                log_error(f"店铺 {browser_name} 任务执行异常: {str(e)}")
                import traceback
                traceback.print_exc()
                
            finally:
                # 关闭WebDriver
                try:
                    driver.quit()
                    log_step("WebDriver已关闭")
                except:
                    pass
                
                # 关闭店铺
                log_step(f"正在关闭店铺 {browser_name}...")
                self.close_store(browser_oauth)
                log_success(f"店铺 {browser_name} 已关闭")
                
        except Exception as e:
            log_error(f"店铺 {browser_name} 处理异常: {str(e)}")
            import traceback
            traceback.print_exc()
            
        finally:
            log_task_end(f"店铺 {browser_name} 任务", True)

    def find_element_with_multiple_strategies(self, driver, country: str, timeout: float = 10.0):
        """
        使用多种策略查找国家元素
        
        Args:
            driver: WebDriver实例
            country: 国家名称
            timeout: 查找超时时间
            
        Returns:
            WebElement or None: 找到的元素或None
        """
        strategies = [
            # 策略1: 原始XPath
            (By.XPATH, f"//span[contains(@class, 'full-page-account-switcher-account-label') and contains(text(), '{country}')]"),
            # # 策略2: 更宽松的XPath
            # (By.XPATH, f"//span[contains(text(), '{country}')]"),
            # # 策略3: 包含国家名称的任何元素
            # (By.XPATH, f"//*[contains(text(), '{country}')]"),
            # # 策略4: 使用部分文本匹配
            # (By.PARTIAL_LINK_TEXT, country),
        ]
        
        for i, (by, locator) in enumerate(strategies, 1):
            try:
                log_step(f"尝试查找策略 {i}: {by}")
                element = WebDriverWait(driver, timeout / len(strategies)).until(
                    EC.presence_of_element_located((by, locator))
                )
                log_success(f"策略 {i} 成功找到国家元素")
                return element
            except TimeoutException:
                log_warning(f"策略 {i} 未找到元素，尝试下一个策略...")
                continue
            except Exception as e:
                log_warning(f"策略 {i} 出现错误: {str(e)}")
                continue
        
        log_error("所有查找策略都失败了")
        return None

    @retry_with_exponential_backoff(max_retries=3, base_delay=2.0, max_delay=8.0)
    def switch_country(self, driver, country):
        """
        切换到指定国家 (增强版 - 具备重试机制和WebDriver健康检查)
        
        Args:
            driver: WebDriver实例
            country: 目标国家名称
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        log_step(f"开始切换到国家: {country}")
        
        try:
            # 1. 预检查：WebDriver健康状态
            if not self.check_webdriver_health(driver):
                raise BrowserCrashError("WebDriver健康检查失败，浏览器可能已崩溃")
            
            # 2. 等待页面完全加载
            log_step("等待账户切换页面完全加载...")
            if not self.wait_for_page_ready(driver, timeout=15.0):
                log_warning("页面可能未完全加载，但继续尝试操作")
            
            # 3. 使用多策略查找国家元素
            log_step(f"查找国家元素: {country}")
            country_element = self.find_element_with_multiple_strategies(driver, country, timeout=12.0)
            
            if not country_element:
                raise ElementNotFoundError(f"无法找到国家 '{country}' 的元素")
            
            # 4. 智能点击国家元素
            log_step(f"点击国家 '{country}' 元素")
            try:
                # 确保元素可点击
                WebDriverWait(driver, 8).until(EC.element_to_be_clickable(country_element))
                country_element.click()
                log_success(f"成功点击国家 '{country}' 元素")
            except Exception as click_e:
                # 如果普通点击失败，尝试JavaScript点击
                log_warning(f"普通点击失败，尝试JavaScript点击: {str(click_e)}")
                driver.execute_script("arguments[0].click();", country_element)
                log_success("JavaScript点击成功")

            # 5. 查找并点击"选择账户"按钮
            select_button_locators = [
                (By.XPATH, '//*[@id="sc-content-container"]/div/div[3]/div/kat-button//button'),
                (By.XPATH, '//*[@id="sc-content-container"]/div/div[3]/div'),
                (By.XPATH, '//*[@id="sc-content-container"]/div/div[3]/div/kat-button'),
                (By.XPATH, '//*[@id="sc-content-container"]/div/div[3]/div/kat-button//button/div[2]/slot/span'),
            ]

            select_button = None
            for i, (by, locator) in enumerate(select_button_locators, 1):
                try:
                    log_step(f"尝试定位策略 {i} 查找选择按钮...")
                    select_button = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((by, locator))
                    )
                    log_success(f"策略 {i} 成功找到选择按钮")
                    break
                except TimeoutException:
                    log_warning(f"策略 {i} 未找到选择按钮")
                    continue

            if not select_button:
                raise ElementNotFoundError("无法找到'选择账户'按钮")

            # 6. 点击选择按钮
            log_step("点击选择账户按钮")
            try:
                select_button.click()
                log_success("成功点击选择账户按钮")
            except Exception as click_e:
                log_warning(f"普通点击选择按钮失败，尝试JavaScript点击: {str(click_e)}")
                driver.execute_script("arguments[0].click();", select_button)
                log_success("JavaScript点击选择按钮成功")

            # 7. 等待页面跳转并处理可能的登录
            time.sleep(3)  # 给页面跳转一些时间

            log_step("处理可能出现的登录页面...")
            if not self._handle_login_if_needed(driver):
                log_error(f"切换国家 {country} 后登录处理失败")
                return False

            # 8. 最终验证：确保切换成功
            log_step("验证国家切换是否成功...")
            current_url = driver.current_url
            log_info(f"当前页面URL: {current_url}")

            # 简单验证：URL不再是账户切换页面
            if "account-switcher" not in current_url:
                log_success(f"国家 '{country}' 切换成功")
                return True
            else:
                log_warning("似乎仍在账户切换页面，可能切换未成功")
                return False
                
        except BrowserCrashError as e:
            log_error(f"浏览器崩溃错误: {str(e)}")
            raise e  # 重新抛出以触发重试
        except ElementNotFoundError as e:
            log_error(f"元素未找到: {str(e)}")
            raise WebDriverException(str(e))  # 转换为WebDriverException以触发重试
        except NetworkTimeoutError as e:
            log_error(f"网络超时: {str(e)}")
            raise WebDriverException(str(e))  # 转换为WebDriverException以触发重试
        except TimeoutException as e:
            log_error(f"操作超时: {str(e)}")
            raise WebDriverException(str(e))  # 转换为WebDriverException以触发重试
        except Exception as e:
            error_msg = f"切换国家 '{country}' 时发生未知错误: {str(e)}"
            log_error(error_msg)
            
            # 记录额外的调试信息
            try:
                log_info(f"当前URL: {driver.current_url}")
                log_info(f"页面标题: {driver.title}")
            except:
                log_error("无法获取页面调试信息，WebDriver可能已断开")
            
            # 对于未知错误，也转换为WebDriverException以尝试重试
            raise WebDriverException(error_msg)

    def memory_optimization(self, driver, country=""):
        """
        内存优化方法
        定期清理WebDriver缓存和内存
        """
        try:
            log_info(f"{country}: 开始激进内存优化...")
            
            # 清理浏览器缓存
            driver.delete_all_cookies()
            log_info(f"{country}: 已清理浏览器缓存")
            
            # 执行更激进的JavaScript清理内存
            driver.execute_script("""
                // 激进的内存清理
                if (window.gc) {
                    window.gc();
                    window.gc(); // 执行两次确保清理
                }
                
                // 清理所有事件监听器
                var elements = document.querySelectorAll('*');
                for (var i = 0; i < elements.length; i++) {
                    var element = elements[i];
                    var clone = element.cloneNode(true);
                    if (element.parentNode) {
                        element.parentNode.replaceChild(clone, element);
                    }
                }
                
                // 清理定时器
                var highestTimeoutId = setTimeout(";");
                for (var i = 0; i < highestTimeoutId; i++) {
                    clearTimeout(i);
                }
                
                // 清理间隔器
                var highestIntervalId = setInterval(";");
                for (var i = 0; i < highestIntervalId; i++) {
                    clearInterval(i);
                }
                
                // 清理localStorage和sessionStorage
                try {
                    localStorage.clear();
                    sessionStorage.clear();
                } catch(e) {}
                
                // 强制垃圾回收
                if (window.gc) {
                    window.gc();
                }
            """)
            log_info(f"{country}: 已执行激进JavaScript内存清理")
            
            # 强制垃圾回收（多次执行）
            import gc
            for i in range(3):
                gc.collect()
            log_info(f"{country}: 已执行多次Python垃圾回收")
            
            log_success(f"{country}: 激进内存优化完成")
            return True
            
        except Exception as e:
            log_error(f"{country}: 内存优化失败: {str(e)}")
            return False
    
    def check_memory_usage(self, country=""):
        """
        检查内存使用情况
        """
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_percent = process.memory_percent()
            
            log_info(f"{country}: 内存使用情况 - RSS: {memory_info.rss / 1024 / 1024:.2f}MB, 占用率: {memory_percent:.2f}%")
            
            # 如果内存使用率超过80%，建议进行内存优化
            if memory_percent > 80:
                log_warning(f"{country}: 内存使用率过高({memory_percent:.2f}%)，建议进行内存优化")
                return False
            return True
            
        except Exception as e:
            log_error(f"{country}: 内存检查失败: {str(e)}")
            return True  # 如果检查失败，默认继续执行

    def check_page_state(self, driver, country="", expected_elements=None, timeout=10):
        """
        检查页面状态，确保页面已正确加载
        
        Args:
            driver: WebDriver实例
            country: 国家名称
            expected_elements: 期望存在的元素列表
            timeout: 超时时间
            
        Returns:
            bool: 页面状态是否正常
        """
        try:
            log_info(f"{country}: 开始检查页面状态...")
            
            # 检查页面是否完全加载
            if not self.wait_for_page_ready(driver, timeout):
                log_error(f"{country}: 页面加载超时")
                return False
            
            # 检查页面标题
            try:
                page_title = driver.title
                log_info(f"{country}: 页面标题: {page_title}")
            except Exception as e:
                log_warning(f"{country}: 获取页面标题失败: {str(e)}")
            
            # 检查页面URL
            try:
                current_url = driver.current_url
                log_info(f"{country}: 当前URL: {current_url}")
            except Exception as e:
                log_warning(f"{country}: 获取当前URL失败: {str(e)}")
            
            # 检查期望的元素
            if expected_elements:
                for element_desc, xpath in expected_elements.items():
                    try:
                        element = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.XPATH, xpath))
                        )
                        log_info(f"{country}: 找到期望元素: {element_desc}")
                    except TimeoutException:
                        log_error(f"{country}: 未找到期望元素: {element_desc}")
                        return False
            
            # 检查页面是否有错误
            try:
                error_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '错误') or contains(text(), 'Error') or contains(text(), '失败') or contains(text(), 'Failed')]")
                if error_elements:
                    for error_element in error_elements:
                        error_text = error_element.text
                        if error_text and len(error_text.strip()) > 0:
                            log_warning(f"{country}: 页面发现错误信息: {error_text}")
            except Exception as e:
                log_warning(f"{country}: 检查页面错误信息失败: {str(e)}")
            
            # 检查页面是否响应
            try:
                # 尝试执行一个简单的JavaScript
                driver.execute_script("return document.readyState;")
                log_info(f"{country}: 页面响应正常")
            except Exception as e:
                log_error(f"{country}: 页面无响应: {str(e)}")
                return False
            
            log_success(f"{country}: 页面状态检查完成")
            return True
            
        except Exception as e:
            log_error(f"{country}: 页面状态检查失败: {str(e)}")
            return False
    
    def wait_for_element_stable(self, driver, element_xpath, country="", timeout=10, check_interval=0.5):
        """
        等待元素稳定，确保元素可以安全操作
        
        Args:
            driver: WebDriver实例
            element_xpath: 元素xpath
            country: 国家名称
            timeout: 超时时间
            check_interval: 检查间隔
            
        Returns:
            WebElement or None: 稳定的元素或None
        """
        try:
            log_info(f"{country}: 等待元素稳定: {element_xpath}")
            
            start_time = time.time()
            last_position = None
            stable_count = 0
            required_stable_count = 3  # 需要连续3次位置相同才算稳定
            
            while time.time() - start_time < timeout:
                try:
                    element = driver.find_element(By.XPATH, element_xpath)
                    
                    # 检查元素是否可见和可点击
                    if not element.is_displayed() or not element.is_enabled():
                        log_info(f"{country}: 元素不可见或不可点击，等待...")
                        time.sleep(check_interval)
                        continue
                    
                    # 获取元素位置
                    current_position = element.location
                    
                    if last_position is None:
                        last_position = current_position
                        stable_count = 1
                    elif current_position == last_position:
                        stable_count += 1
                        if stable_count >= required_stable_count:
                            log_success(f"{country}: 元素已稳定")
                            return element
                    else:
                        last_position = current_position
                        stable_count = 1
                    
                    time.sleep(check_interval)
                    
                except Exception as e:
                    log_warning(f"{country}: 等待元素稳定时出错: {str(e)}")
                    time.sleep(check_interval)
            
            log_error(f"{country}: 元素稳定等待超时")
            return None
            
        except Exception as e:
            log_error(f"{country}: 等待元素稳定失败: {str(e)}")
            return None

    def get_optimized_chrome_options(self, is_headless=False):
        """
        获取优化的Chrome浏览器配置
        增加内存限制和稳定性设置
        """
        chrome_options = Options()
        
        # 基础配置
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-plugins")
        chrome_options.add_argument("--disable-images")  # 禁用图片加载，减少内存使用
        # 移除JavaScript禁用，保持点击策略的灵活性
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        
        # 内存优化配置 - 更激进的内存限制
        chrome_options.add_argument("--memory-pressure-off")
        chrome_options.add_argument("--max_old_space_size=256")  # 进一步限制JavaScript堆内存
        chrome_options.add_argument("--js-flags=--max-old-space-size=256")
        chrome_options.add_argument("--disable-background-timer-throttling")
        chrome_options.add_argument("--disable-backgrounding-occluded-windows")
        chrome_options.add_argument("--disable-renderer-backgrounding")
        
        # 进程配置 - 更严格的进程控制
        chrome_options.add_argument("--single-process")  # 单进程模式，减少内存使用
        chrome_options.add_argument("--disable-ipc-flooding-protection")
        chrome_options.add_argument("--disable-background-networking")
        chrome_options.add_argument("--disable-background-mode")
        chrome_options.add_argument("--disable-background-timer-throttling")
        
        # 缓存配置 - 更激进的缓存清理
        chrome_options.add_argument("--disable-application-cache")
        chrome_options.add_argument("--disable-offline-load-stale-cache")
        chrome_options.add_argument("--disk-cache-size=1")
        chrome_options.add_argument("--media-cache-size=1")
        chrome_options.add_argument("--aggressive-cache-discard")
        
        # 网络配置
        chrome_options.add_argument("--disable-background-networking")
        chrome_options.add_argument("--disable-default-apps")
        # 注意：不禁用sync以保持登录状态
        chrome_options.add_argument("--disable-component-update")
        
        # 性能配置 - 更激进的性能优化
        chrome_options.add_argument("--disable-logging")
        chrome_options.add_argument("--disable-in-process-stack-traces")
        chrome_options.add_argument("--disable-histogram-customizer")
        chrome_options.add_argument("--disable-glsl-translator")
        chrome_options.add_argument("--disable-gpu-rasterization")
        chrome_options.add_argument("--disable-gpu-sandbox")
        chrome_options.add_argument("--disable-software-rasterizer")
        chrome_options.add_argument("--disable-threaded-animation")
        chrome_options.add_argument("--disable-threaded-scrolling")
        
        # 内存管理配置
        chrome_options.add_argument("--enable-precise-memory-info")
        chrome_options.add_argument("--enable-logging")
        chrome_options.add_argument("--v=1")
        
        # 紫鸟浏览器兼容配置（移除无头模式支持）
        log_info("🔧 配置紫鸟浏览器兼容选项")

        # 注意：紫鸟浏览器不支持标准Chrome的无头模式
        # 所有模式都使用紫鸟浏览器的默认配置
        if is_headless:
            log_warning("⚠️ 紫鸟浏览器不支持无头模式，将使用普通模式")
            log_warning("⚠️ 如需后台运行，请最小化浏览器窗口")

        log_info("✅ 紫鸟浏览器配置完成")
        
        # 用户数据目录配置（跨平台兼容，保持登录状态）
        import os
        import tempfile
        import platform

        # 根据操作系统选择合适的用户数据目录
        system = platform.system()
        if system == "Windows":
            # Windows: 使用AppData目录
            base_dir = os.path.expanduser("~\\AppData\\Local")
        elif system == "Darwin":  # macOS
            # macOS: 使用Application Support目录
            base_dir = os.path.expanduser("~/Library/Application Support")
        else:  # Linux和其他Unix系统
            # Linux: 使用.local目录
            base_dir = os.path.expanduser("~/.local/share")

        # 创建紫鸟专用的Chrome配置目录
        fixed_user_data_dir = os.path.join(base_dir, "ZiniaoRPA", "chrome_profile")

        # 确保目录存在，使用递归创建
        try:
            os.makedirs(fixed_user_data_dir, exist_ok=True)
            log_info(f"✅ 用户数据目录创建成功: {fixed_user_data_dir}")
        except Exception as e:
            # 如果创建失败，回退到临时目录
            log_warning(f"⚠️ 无法创建用户数据目录，回退到临时目录: {str(e)}")
            fixed_user_data_dir = os.path.join(tempfile.gettempdir(), "ziniao_chrome_profile")
            os.makedirs(fixed_user_data_dir, exist_ok=True)

        chrome_options.add_argument(f"--user-data-dir={fixed_user_data_dir}")
        chrome_options.add_argument("--no-first-run")
        chrome_options.add_argument("--no-default-browser-check")

        log_info(f"🔧 Chrome用户数据目录 ({system}): {fixed_user_data_dir}")
        log_info(f"🔄 登录状态将在有头/无头模式间共享")

        return chrome_options

    def record_store_failure(self, store_name, countries, failure_reason):
        """记录店铺失败信息"""
        failure_info = {
            'store_name': store_name,
            'countries': countries,
            'failure_reason': failure_reason,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        self.task_statistics['failed_stores'].append(failure_info)

        # 记录详细的失败日志
        log_error(f"🚫 店铺失败记录")
        log_error(f"   店铺: {store_name}")
        log_error(f"   计划处理站点: {', '.join(countries) if countries else '未知'}")
        log_error(f"   失败原因: {failure_reason}")
        log_error(f"   结果: 该店铺的所有站点设置失败")


class ThrottledOperationManager:
    """
    操作节奏控制管理器
    用于控制WebDriver操作的执行频率，避免页面不稳定
    增加单元素操作锁机制，确保同一时间只有一个元素被操作
    """
    
    def __init__(self, min_operation_interval=2.0, dom_settle_time=1.5, batch_break_size=3, batch_break_time=4.0):
        """
        初始化节奏控制管理器
        
        Args:
            min_operation_interval: 最小操作间隔(秒) - 增加到2.0秒
            dom_settle_time: DOM稳定等待时间(秒) - 增加到1.5秒
            batch_break_size: 批量操作休息间隔(操作数) - 减少到3个操作
            batch_break_time: 批量操作休息时间(秒) - 增加到4.0秒
        """
        self.min_operation_interval = min_operation_interval
        self.dom_settle_time = dom_settle_time
        self.batch_break_size = batch_break_size
        self.batch_break_time = batch_break_time
        self.last_operation_time = 0
        self.operation_count = 0
        self.element_lock = threading.Lock()  # 元素操作锁
        self.current_operation = None  # 当前正在执行的操作
        
    def throttled_click(self, driver, element, element_name, country=""):
        """
        节流控制的点击操作
        
        Args:
            driver: WebDriver实例
            element: 要点击的元素
            element_name: 元素名称(用于日志)
            country: 国家名称(用于日志)
            
        Returns:
            bool: 点击是否成功
        """
        return self.safe_element_operation(f"点击_{element_name}", self._perform_click, driver, element, element_name, country)
    
    def _perform_click(self, driver, element, element_name, country=""):
        """执行实际的点击操作"""
        # 确保操作间隔
        self._ensure_operation_interval(country)
        
        try:
            # 执行点击操作
            element.click()
            log_success(f"{country}: 节流点击成功 - {element_name}")
            
            # DOM稳定等待
            self._wait_dom_settle(country, element_name)
            
            # 更新操作计数和时间
            self._update_operation_state()
            
            return True
            
        except Exception as e:
            log_error(f"{country}: 节流点击失败 - {element_name}: {str(e)}")
            return False
    
    def throttled_send_keys(self, driver, element, text, element_name, country=""):
        """
        节流控制的文本输入操作
        
        Args:
            driver: WebDriver实例
            element: 输入元素
            text: 要输入的文本
            element_name: 元素名称(用于日志)
            country: 国家名称(用于日志)
            
        Returns:
            bool: 输入是否成功
        """
        return self.safe_element_operation(f"输入_{element_name}", self._perform_send_keys, driver, element, text, element_name, country)
    
    def _perform_send_keys(self, driver, element, text, element_name, country=""):
        """执行实际的文本输入操作"""
        # 确保操作间隔
        self._ensure_operation_interval(country)
        
        try:
            # 清空并输入文本
            element.clear()
            time.sleep(0.3)  # 清空后短暂等待
            element.send_keys(text)
            time.sleep(0.3)  # 输入后短暂等待
            
            log_success(f"{country}: 节流输入成功 - {element_name}: '{text}'")
            
            # DOM稳定等待
            self._wait_dom_settle(country, element_name)
            
            # 更新操作计数和时间
            self._update_operation_state()
            
            return True
            
        except Exception as e:
            log_error(f"{country}: 节流输入失败 - {element_name}: {str(e)}")
            return False
    
    def safe_element_operation(self, operation_name, operation_func, *args, **kwargs):
        """
        安全的单元素操作，确保同一时间只有一个元素被操作
        
        Args:
            operation_name: 操作名称
            operation_func: 要执行的操作函数
            *args, **kwargs: 传递给操作函数的参数
            
        Returns:
            操作函数的返回值
        """
        with self.element_lock:
            if self.current_operation:
                log_warning(f"等待前一个操作完成: {self.current_operation}")
                return False
                
            self.current_operation = operation_name
            try:
                log_info(f"开始单元素操作: {operation_name}")
                result = operation_func(*args, **kwargs)
                log_success(f"单元素操作完成: {operation_name}")
                return result
            except Exception as e:
                log_error(f"单元素操作失败: {operation_name} - {str(e)}")
                return False
            finally:
                self.current_operation = None
    
    def batch_operation_break_check(self, country=""):
        """
        批量操作休息检查
        每执行指定数量的操作后，自动插入休息时间
        
        Args:
            country: 国家名称(用于日志)
        """
        if self.operation_count > 0 and self.operation_count % self.batch_break_size == 0:
            log_info(f"{country}: 批量操作休息 {self.batch_break_time}s (已完成 {self.operation_count} 个操作)")
            time.sleep(self.batch_break_time)
    
    def _ensure_operation_interval(self, country=""):
        """确保操作间隔"""
        elapsed = time.time() - self.last_operation_time
        if elapsed < self.min_operation_interval:
            sleep_time = self.min_operation_interval - elapsed
            log_info(f"{country}: 操作节流等待 {sleep_time:.1f}s")
            time.sleep(sleep_time)
    
    def _wait_dom_settle(self, country="", element_name=""):
        """等待DOM稳定"""
        time.sleep(self.dom_settle_time)
        log_info(f"{country}: DOM稳定等待完成 - {element_name}")
    
    def _update_operation_state(self):
        """更新操作状态"""
        self.last_operation_time = time.time()
        self.operation_count += 1
    
    def reset_counter(self):
        """重置操作计数器(用于新的批量操作开始时)"""
        self.operation_count = 0
        log_info("操作计数器已重置")


class ChromeDriverManager:
    """
    ChromeDriver版本智能管理器
    自动检测系统Chrome版本并下载匹配的ChromeDriver
    """
    
    def __init__(self, driver_folder_path="./drivers"):
        """
        初始化ChromeDriver管理器
        
        Args:
            driver_folder_path: ChromeDriver存储路径
        """
        self.driver_folder_path = driver_folder_path
        # 确保目录存在
        os.makedirs(driver_folder_path, exist_ok=True)
        self.version_mapping = {
            # Chrome主版本 -> ChromeDriver版本映射（使用最新的正确版本）
            "138": "138.0.7204.183",  # 修正为正确的Chrome 138版本
            "137": "137.0.6847.0",    # 添加137版本支持
            "136": "136.0.6776.0",    # 添加136版本支持
            "135": "135.0.6698.0",    # 添加135版本支持
            "134": "134.0.6991.18",
            "133": "133.0.6835.16",
            "132": "132.0.6834.83",
            "131": "131.0.6778.87",   # 紫鸟浏览器实际版本
            "130": "130.0.6723.116",
            "129": "129.0.6668.89",
            "128": "128.0.6613.137",
            "127": "127.0.6533.119"
        }
        
        # 确保drivers文件夹存在
        os.makedirs(driver_folder_path, exist_ok=True)
    
    def get_system_chrome_version(self):
        """
        获取系统Chrome版本
        
        Returns:
            str: Chrome主版本号，如"134"，失败返回None
        """
        try:
            if platform.system() == "Windows":
                import winreg
                # 尝试多个注册表位置
                registry_paths = [
                    (winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon"),
                    (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Google\Chrome\BLBeacon"),
                    (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Google\Chrome\BLBeacon")
                ]
                
                for hkey, path in registry_paths:
                    try:
                        key = winreg.OpenKey(hkey, path)
                        version = winreg.QueryValueEx(key, "version")[0]
                        major_version = version.split('.')[0]
                        log_success(f"检测到Chrome版本: {version} (主版本: {major_version})")
                        return major_version
                    except FileNotFoundError:
                        continue
                    except Exception as e:
                        log_warning(f"读取注册表失败: {str(e)}")
                        continue
                        
            elif platform.system() == "Darwin":  # macOS
                try:
                    import subprocess
                    result = subprocess.run([
                        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "--version"
                    ], capture_output=True, text=True, timeout=5)
                    
                    if result.returncode == 0:
                        version_str = result.stdout.strip()
                        # 解析版本号，格式通常是 "Google Chrome 134.0.6991.112"
                        version_parts = version_str.split()
                        if len(version_parts) >= 3:
                            version = version_parts[2]
                            major_version = version.split('.')[0]
                            log_success(f"检测到Chrome版本: {version} (主版本: {major_version})")
                            return major_version
                except Exception as e:
                    log_warning(f"macOS Chrome版本检测失败: {str(e)}")
                    
            log_warning("无法检测Chrome版本，将使用默认ChromeDriver")
            return None
            
        except Exception as e:
            log_error(f"Chrome版本检测异常: {str(e)}")
            return None
    
    def get_latest_chromedriver_version(self, chrome_version):
        """
        动态获取ChromeDriver版本映射
        
        Args:
            chrome_version: Chrome主版本号
            
        Returns:
            str: ChromeDriver版本号，失败返回None
        """
        try:
            import requests
            
            # 获取ChromeDriver版本列表
            url = "https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            versions = data.get("versions", [])
            
            # 查找匹配的ChromeDriver版本
            for version_info in versions:
                version = version_info.get("version", "")
                if version.startswith(f"{chrome_version}."):
                    log_success(f"找到ChromeDriver版本映射: Chrome {chrome_version} -> {version}")
                    return version
            
            log_warning(f"未找到Chrome {chrome_version} 对应的ChromeDriver版本")
            return None
            
        except Exception as e:
            log_error(f"获取ChromeDriver版本映射失败: {str(e)}")
            return None
    
    def get_driver_path(self, chrome_version):
        """
        获取对应版本的ChromeDriver路径
        
        Args:
            chrome_version: Chrome主版本号
            
        Returns:
            str: ChromeDriver可执行文件路径
        """
        if platform.system() == "Windows":
            driver_name = f"chromedriver{chrome_version}.exe"
        else:
            driver_name = f"chromedriver{chrome_version}"
            
        return os.path.join(self.driver_folder_path, driver_name)
    
    def is_driver_available(self, chrome_version):
        """
        检查指定版本的ChromeDriver是否已存在
        
        Args:
            chrome_version: Chrome主版本号
            
        Returns:
            bool: Driver是否存在且可执行
        """
        driver_path = self.get_driver_path(chrome_version)
        
        if not os.path.exists(driver_path):
            return False
            
        # 检查是否可执行
        if not os.access(driver_path, os.X_OK):
            # 在Windows上，exe文件可能没有执行权限标记，但仍可执行
            if platform.system() == "Windows" and driver_path.endswith('.exe'):
                return True
            else:
                return False
                
        return True
    
    def download_matching_driver(self, chrome_version):
        """
        下载匹配的ChromeDriver
        
        Args:
            chrome_version: Chrome主版本号
            
        Returns:
            bool: 下载是否成功
        """
        # 首先尝试从本地映射获取
        if chrome_version in self.version_mapping:
            driver_version = self.version_mapping[chrome_version]
            log_info(f"使用本地版本映射: Chrome {chrome_version} -> {driver_version}")
        else:
            # 尝试动态获取版本映射
            log_info(f"Chrome版本 {chrome_version} 不在本地映射中，尝试动态获取...")
            driver_version = self.get_latest_chromedriver_version(chrome_version)
            
            if not driver_version:
                log_error(f"Chrome版本 {chrome_version} 没有对应的ChromeDriver映射")
                return False
            
            # 将新版本添加到本地映射
            self.version_mapping[chrome_version] = driver_version
            log_success(f"已添加版本映射: Chrome {chrome_version} -> {driver_version}")
        driver_path = self.get_driver_path(chrome_version)
        
        try:
            log_step(f"开始下载ChromeDriver {driver_version}...")
            
            # 构建下载URL
            if platform.system() == "Windows":
                platform_suffix = "win32"
                file_extension = ".exe"
            elif platform.system() == "Darwin":
                platform_suffix = "mac-x64"  # 或 mac-arm64，根据需要调整
                file_extension = ""
            else:
                platform_suffix = "linux64"
                file_extension = ""
            
            # ChromeDriver下载URL格式
            download_url = f"https://storage.googleapis.com/chrome-for-testing-public/{driver_version}/{platform_suffix}/chromedriver-{platform_suffix}.zip"
            
            log_info(f"下载URL: {download_url}")
            
            # 下载文件
            import requests
            import zipfile
            import tempfile
            
            response = requests.get(download_url, timeout=60)
            response.raise_for_status()
            
            # 使用临时文件处理zip
            with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_file:
                temp_file.write(response.content)
                temp_zip_path = temp_file.name
            
            try:
                # 解压zip文件
                with zipfile.ZipFile(temp_zip_path, 'r') as zip_ref:
                    # 查找chromedriver可执行文件
                    for file_info in zip_ref.filelist:
                        if file_info.filename.endswith(f'chromedriver{file_extension}'):
                            # 确保目标目录存在
                            os.makedirs(os.path.dirname(driver_path), exist_ok=True)
                            
                            # 提取到目标位置
                            with zip_ref.open(file_info.filename) as source:
                                with open(driver_path, 'wb') as target:
                                    target.write(source.read())
                            
                            # 设置执行权限（Unix系统）
                            if platform.system() != "Windows":
                                os.chmod(driver_path, 0o755)
                            
                            log_success(f"ChromeDriver {driver_version} 下载成功: {driver_path}")
                            
                            return True
                
                log_error("zip文件中未找到chromedriver可执行文件")
                return False
                
            finally:
                # 清理临时文件
                try:
                    if os.path.exists(temp_zip_path):
                        os.unlink(temp_zip_path)
                except Exception as e:
                    log_warning(f"清理临时文件失败: {str(e)}")
            
        except requests.RequestException as e:
            log_error(f"下载ChromeDriver失败: {str(e)}")
            return False
        except Exception as e:
            log_error(f"处理ChromeDriver下载时发生错误: {str(e)}")
            return False
    
    def download_driver_for_version(self, version):
        """
        为指定版本下载ChromeDriver
        
        Args:
            version: Chrome主版本号（来自紫鸟客户端）
            
        Returns:
            bool: 下载是否成功
        """
        try:
            log_info(f"开始为Chrome版本 {version} 下载ChromeDriver...")
            
            # 检查是否已有对应版本的ChromeDriver
            if self.is_driver_available(version):
                log_success(f"ChromeDriver {version} 已存在")
                return True
            
            # 下载匹配的ChromeDriver
            if self.download_matching_driver(version):
                log_success(f"ChromeDriver {version} 下载成功")
                return True
            else:
                log_error(f"ChromeDriver {version} 下载失败")
                return False
                
        except Exception as e:
            log_error(f"下载ChromeDriver {version} 时发生错误: {str(e)}")
            return False
    
    def ensure_compatible_driver(self):
        """
        确保有兼容的ChromeDriver可用
        
        Returns:
            str: 可用的ChromeDriver路径，失败返回None
        """
        # 1. 获取系统Chrome版本
        chrome_version = self.get_system_chrome_version()
        
        if not chrome_version:
            log_warning("无法检测Chrome版本，使用现有ChromeDriver")
            # 返回默认的chromedriver路径
            default_paths = [
                os.path.join(self.driver_folder_path, "chromedriver.exe"),
                os.path.join(self.driver_folder_path, "chromedriver")
            ]
            for path in default_paths:
                if os.path.exists(path):
                    return path
            return None
        
        # 2. 检查是否已有对应版本的ChromeDriver
        if self.is_driver_available(chrome_version):
            driver_path = self.get_driver_path(chrome_version)
            log_success(f"找到匹配的ChromeDriver: {driver_path}")
            return driver_path
        
        # 3. 下载匹配的ChromeDriver
        log_info(f"Chrome版本 {chrome_version} 的ChromeDriver不存在，开始下载...")
        
        if self.download_matching_driver(chrome_version):
            return self.get_driver_path(chrome_version)
        else:
            log_error(f"下载Chrome {chrome_version}对应的ChromeDriver失败")
            return None

    def set_task_control(self, task_control):
        """
        设置任务控制对象（由GUI调用）
        
        :param task_control: GUI的任务控制字典
        """
        self.task_control = task_control
    
    def check_task_control(self):
        """
        检查任务控制状态
        子类在执行长时间操作时应调用此方法
        
        :return: True继续执行，False应该停止
        """
        if not self.task_control:
            return True
        
        # 检查暂停状态
        if self.task_control.get('paused', False):
            log_warning("任务被暂停，等待恢复...")
            self.wait_if_paused()
            return True
        
        # 检查停止状态
        if self.task_control.get('stopped', False):
            log_warning("任务被停止")
            return False
        
        return True
    
    def wait_if_paused(self):
        """
        等待任务恢复（当任务被暂停时）
        """
        if not self.task_control:
            return
        
        while self.task_control.get('paused', False):
            time.sleep(1)
            if self.task_control.get('stopped', False):
                break




class ConfigGUI:
    def __init__(self, rpa_instance):
        self.rpa = rpa_instance
        self.window = tk.Tk()
        task_name = self.rpa.get_task_name()
        self.window.title(f"紫鸟浏览器配置 - {task_name}")
        self.window.geometry("800x600")
        
        # 设置样式
        style = ttk.Style()
        style.configure("TLabel", padding=5)
        style.configure("TEntry", padding=5)
        style.configure("TButton", padding=5)
        
        # 创建主框架
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置主窗口的网格权重
        self.window.grid_rowconfigure(0, weight=1)
        self.window.grid_columnconfigure(0, weight=1)
        main_frame.grid_columnconfigure(1, weight=1)
        
        # 客户端路径选择
        ttk.Label(main_frame, text="紫鸟浏览器路径:").grid(row=0, column=0, sticky=tk.W)
        self.client_path_var = tk.StringVar()
        self.client_path_var.trace_add("write", self.auto_save_config)
        ttk.Entry(main_frame, textvariable=self.client_path_var, width=50).grid(row=0, column=1, padx=5)
        ttk.Button(main_frame, text="浏览", command=self.select_client_path).grid(row=0, column=2)
        
        # WebDriver路径选择
        ttk.Label(main_frame, text="WebDriver路径:").grid(row=1, column=0, sticky=tk.W)
        self.driver_path_var = tk.StringVar()
        self.driver_path_var.trace_add("write", self.auto_save_config)
        ttk.Entry(main_frame, textvariable=self.driver_path_var, width=50).grid(row=1, column=1, padx=5)
        ttk.Button(main_frame, text="浏览", command=self.select_driver_path).grid(row=1, column=2)
        
        # 用户信息输入
        ttk.Label(main_frame, text="公司名称:").grid(row=2, column=0, sticky=tk.W)
        self.company_var = tk.StringVar()
        self.company_var.trace_add("write", self.auto_save_config)
        ttk.Entry(main_frame, textvariable=self.company_var, width=50).grid(row=2, column=1, padx=5)
        
        ttk.Label(main_frame, text="用户名:").grid(row=3, column=0, sticky=tk.W)
        self.username_var = tk.StringVar()
        self.username_var.trace_add("write", self.auto_save_config)
        ttk.Entry(main_frame, textvariable=self.username_var, width=50).grid(row=3, column=1, padx=5)
        
        ttk.Label(main_frame, text="密码:").grid(row=4, column=0, sticky=tk.W)
        self.password_var = tk.StringVar()
        self.password_var.trace_add("write", self.auto_save_config)
        ttk.Entry(main_frame, textvariable=self.password_var, width=50, show="*").grid(row=4, column=1, padx=5)
        
        # 文件上传路径选择
        ttk.Label(main_frame, text="上传文件文件夹:").grid(row=5, column=0, sticky=tk.W)
        self.upload_folder_var = tk.StringVar()
        self.upload_folder_var.trace_add("write", self.auto_save_config)
        ttk.Entry(main_frame, textvariable=self.upload_folder_var, width=50).grid(row=5, column=1, padx=5)
        ttk.Button(main_frame, text="浏览", command=self.select_upload_folder).grid(row=5, column=2)
        
        # 国家选择
        ttk.Label(main_frame, text="选择国家:").grid(row=6, column=0, sticky=tk.W)
        self.country_frame = ttk.Frame(main_frame)
        self.country_frame.grid(row=6, column=1, columnspan=2, sticky=tk.W)
        
        self.country_vars = {}
        for i, country in enumerate(COUNTRIES):
            var = tk.BooleanVar()
            self.country_vars[country] = var
            ttk.Checkbutton(self.country_frame, text=country, variable=var).grid(
                row=i//3, column=i%3, sticky=tk.W, padx=5, pady=2
            )
            
        # 店铺选择
        ttk.Label(main_frame, text="选择店铺:").grid(row=7, column=0, sticky=tk.W)
        
        # 创建一个带滚动条的框架
        store_container = ttk.Frame(main_frame)
        store_container.grid(row=7, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 创建Canvas和Scrollbar
        canvas = tk.Canvas(store_container, height=150)  # 设置固定高度
        scrollbar = ttk.Scrollbar(store_container, orient="vertical", command=canvas.yview)
        
        # 创建店铺框架
        self.store_frame = ttk.Frame(canvas)
        
        # 配置Canvas
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 放置Canvas和Scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 创建窗口
        canvas_frame = canvas.create_window((0, 0), window=self.store_frame, anchor="nw")
        
        # 绑定事件
        def configure_scroll_region(event):
            canvas.configure(scrollregion=canvas.bbox("all"))
        
        def configure_canvas_width(event):
            canvas.itemconfig(canvas_frame, width=event.width)
        
        self.store_frame.bind("<Configure>", configure_scroll_region)
        canvas.bind("<Configure>", configure_canvas_width)
        
        self.store_vars = {}
        self.store_list = []
        
        # 创建一个按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=8, column=1, columnspan=2, pady=10)
        
        # 刷新店铺列表按钮
        ttk.Button(button_frame, text="刷新店铺列表", command=self.refresh_store_list_async).pack(side=tk.LEFT, padx=5)
        
        # 开始按钮
        ttk.Button(button_frame, text="开始", command=self.start_program).pack(side=tk.LEFT, padx=5)
        
        # 加载已保存的配置（如果有）
        self.load_config()
    
    def select_client_path(self):
        path = filedialog.askopenfilename(
            title="选择紫鸟浏览器启动程序",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        if path:
            self.client_path_var.set(path)
            
    def select_driver_path(self):
        path = filedialog.askdirectory(title="选择WebDriver目录")
        if path:
            self.driver_path_var.set(path)
            
    def select_upload_folder(self):
        path = filedialog.askdirectory(title="选择上传文件文件夹")
        if path:
            self.upload_folder_var.set(path)
            
    def auto_save_config(self, *args):
        """自动保存配置"""
        self.save_config(silent=True)
            
    def save_config(self, silent=False):
        """保存配置到文件"""
        selected_countries = [country for country, var in self.country_vars.items() if var.get()]
        config = {
            "client_path": self.client_path_var.get(),
            "driver_folder_path": self.driver_path_var.get(),
            "user_info": {
                "company": self.company_var.get(),
                "username": self.username_var.get(),
                "password": self.password_var.get()
            },
            "selected_countries": selected_countries,
            "upload_folder": self.upload_folder_var.get()
        }
        
        try:
            config_path = get_config_path()
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            if not silent:
                messagebox.showinfo("成功", "配置已保存")
        except Exception as e:
            if not silent:
                messagebox.showerror("错误", f"保存配置失败: {str(e)}")
            
    def load_config(self):
        try:
            config_path = get_config_path()
            if os.path.exists(config_path):
                with open(config_path, "r", encoding="utf-8") as f:
                    config = json.load(f)
                    
                self.client_path_var.set(config.get("client_path", ""))
                self.driver_path_var.set(config.get("driver_folder_path", ""))
                
                user_info = config.get("user_info", {})
                self.company_var.set(user_info.get("company", ""))
                self.username_var.set(user_info.get("username", ""))
                self.password_var.set(user_info.get("password", ""))
                
                self.upload_folder_var.set(config.get("upload_folder", ""))
                
                # 加载已选择的国家
                selected_countries = config.get("selected_countries", [])
                for country in selected_countries:
                    if country in self.country_vars:
                        self.country_vars[country].set(True)
        except Exception as e:
            # 配置文件可能损坏或不完整，不显示错误信息
            pass
    def refresh_store_list_async(self):
        """异步刷新店铺列表"""
        import threading
        
        # 禁用刷新按钮，避免重复点击
        refresh_button = None
        for widget in self.window.winfo_children():
            if hasattr(widget, 'winfo_children'):
                for child in widget.winfo_children():
                    if hasattr(child, 'winfo_children'):
                        for grandchild in child.winfo_children():
                            if isinstance(grandchild, ttk.Button) and grandchild.cget('text') == '刷新店铺列表':
                                refresh_button = grandchild
                                break
        
        if refresh_button:
            refresh_button.config(state='disabled', text='刷新中...')
        
        # 在后台线程执行刷新
        def refresh_task():
            try:
                self.refresh_store_list()
            except Exception as e:
                # 处理异常，记录错误但不关闭界面
                print(f"刷新店铺列表时发生异常: {str(e)}")
                # 在主线程显示错误消息
                self.window.after(0, lambda: messagebox.showerror("刷新失败", f"刷新店铺列表失败: {str(e)}"))
            finally:
                # 无论成功还是失败，都重新启用按钮
                if refresh_button:
                    self.window.after(0, lambda: refresh_button.config(state='normal', text='刷新店铺列表'))
        
        thread = threading.Thread(target=refresh_task)
        thread.daemon = True
        thread.start()
            
    def refresh_store_list(self):
        """刷新店铺列表"""
        # 检查必要配置是否完整
        if not self.client_path_var.get():
            messagebox.showerror("错误", "请先设置紫鸟浏览器路径")
            return
            
        if not self.company_var.get() or not self.username_var.get() or not self.password_var.get():
            messagebox.showerror("错误", "请填写完整的用户信息")
            return
            
        # 清除现有的店铺选择框
        for widget in self.store_frame.winfo_children():
            widget.destroy()
        self.store_vars.clear()
        
        # 获取店铺列表
        try:
            # 设置RPA实例的参数
            self.rpa.client_path = self.client_path_var.get()
            self.rpa.driver_folder_path = self.driver_path_var.get()
            if not self.rpa.driver_folder_path:
                self.rpa.driver_folder_path = get_driver_folder_path()
                if not os.path.exists(self.rpa.driver_folder_path):
                    os.makedirs(self.rpa.driver_folder_path)
            self.rpa.user_info = {
                "company": self.company_var.get(),
                "username": self.username_var.get(),
                "password": self.password_var.get()
            }
            
            # 终止紫鸟客户端已启动的进程
            self.rpa.kill_process()
            
            print("=====启动客户端=====")
            self.rpa.start_browser()
            print("=====更新内核=====")
            self.rpa.update_core()
            
            print("=====获取店铺列表=====")
            browser_list = self.rpa.get_browser_list()
            if browser_list is None:
                messagebox.showerror("错误", "获取店铺列表失败")
                return
                
            self.store_list = browser_list
            
            # 创建店铺选择框
            for i, store in enumerate(browser_list):
                var = tk.BooleanVar()
                self.store_vars[store['browserName']] = var
                ttk.Checkbutton(self.store_frame, text=store['browserName'], variable=var).grid(
                    row=i//3, column=i%3, sticky=tk.W, padx=5, pady=2
                )
                
        except Exception as e:
            messagebox.showerror("错误", f"刷新店铺列表失败: {str(e)}")
            
    def start_program(self):
        """启动主程序"""
        # 保存当前配置
        self.save_config(silent=True)
        
        # 检查必要配置是否完整
        if not self.client_path_var.get():
            messagebox.showerror("错误", "请设置紫鸟浏览器路径")
            return
            
        if not self.company_var.get() or not self.username_var.get() or not self.password_var.get():
            messagebox.showerror("错误", "请填写完整的用户信息")
            return
            
        selected_countries = [country for country, var in self.country_vars.items() if var.get()]
        if not selected_countries:
            messagebox.showerror("错误", "请至少选择一个国家")
            return
            
        if not hasattr(self, 'store_list') or not self.store_list:
            messagebox.showerror("错误", "请先刷新店铺列表")
            return
            
        selected_stores = []
        for store in self.store_list:
            store_name = store['browserName']
            if store_name in self.store_vars and self.store_vars[store_name].get():
                selected_stores.append(store)
                
        if not selected_stores:
            messagebox.showerror("错误", "请至少选择一个店铺")
            return
            
        # 检查上传文件夹是否存在
        upload_folder = self.upload_folder_var.get()
        if not upload_folder or not os.path.exists(upload_folder):
            messagebox.showerror("错误", "请选择有效的上传文件文件夹")
            return
            
        # 检查每个选中的店铺是否有对应的上传文件
        missing_files = []
        for store in selected_stores:
            store_name = store['browserName']
            # 查找文件夹中与店铺名称匹配的文件
            matching_files = [f for f in os.listdir(upload_folder) if f.startswith(store_name)]
            if not matching_files:
                missing_files.append(store_name)
                
        if missing_files:
            messagebox.showerror("错误", f"以下店铺没有找到对应的上传文件：\n{', '.join(missing_files)}")
            return
            
        # 关闭配置窗口
        self.window.destroy()
        
        # 启动主程序逻辑
        self.run_main_program(selected_countries, selected_stores)
        
    def run_main_program(self, selected_countries, selected_stores):
        """运行主程序逻辑"""
        # 设置RPA实例的参数
        self.rpa.client_path = self.client_path_var.get()
        self.rpa.driver_folder_path = self.driver_path_var.get()
        if not self.rpa.driver_folder_path:
            self.rpa.driver_folder_path = get_driver_folder_path()
            if not os.path.exists(self.rpa.driver_folder_path):
                os.makedirs(self.rpa.driver_folder_path)
        self.rpa.user_info = {
            "company": self.company_var.get(),
            "username": self.username_var.get(),
            "password": self.password_var.get()
        }
        
        # 终止紫鸟客户端已启动的进程
        self.rpa.kill_process()

        print("=====启动客户端=====")
        self.rpa.start_browser()
        print("=====更新内核=====")
        self.rpa.update_core()

        # 遍历选中的店铺
        for store in selected_stores:
            # 查找店铺对应的上传文件
            store_name = store['browserName']
            upload_folder = self.upload_folder_var.get()
            matching_files = [f for f in os.listdir(upload_folder) if f.startswith(store_name)]
            if matching_files:
                upload_file_path = os.path.join(upload_folder, matching_files[0])
                # 验证上传文件
                if self.rpa.validate_upload_file(upload_file_path, store_name):
                    self.rpa.use_one_browser_run_task(store, selected_countries, upload_file_path)
                else:
                    print(f"店铺 {store_name} 的上传文件验证失败")
            
    def run(self):
        """运行GUI程序"""
        self.window.mainloop()


def encrypt_sha1(fpath: str) -> str:
    with open(fpath, 'rb') as f:
        return hashlib.new('sha1', f.read()).hexdigest()


def download_file(url, save_path):
    # 发送GET请求获取文件内容
    response = requests.get(url, stream=True)
    # 检查请求是否成功
    if response.status_code == 200:
        # 创建一个本地文件并写入下载的内容（如果文件已存在，将被覆盖）
        with open(save_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    f.write(chunk)
        print(f"文件已成功下载并保存到：{save_path}")
    else:
        print(f"下载失败，响应状态码为：{response.status_code}")


def download_driver(driver_folder_path):
    if platform.system() == 'Windows':
        config_url = "https://cdn-superbrowser-attachment.ziniao.com/webdriver/exe_32/config.json"
    elif platform.system() == 'Darwin':
        arch = platform.machine()
        if arch == 'x86_64':
            config_url = "https://cdn-superbrowser-attachment.ziniao.com/webdriver/mac/x64/config.json"
        elif arch == 'arm64':
            config_url = "https://cdn-superbrowser-attachment.ziniao.com/webdriver/mac/arm64/config.json"
        else:
            return
    else:
        return
    response = requests.get(config_url)
    # 检查请求是否成功
    if response.status_code == 200:
        # 获取文本内容
        txt_content = response.text
        config = json.loads(txt_content)
    else:
        print(f"下载驱动失败，状态码：{response.status_code}")
        exit()
    if not os.path.exists(driver_folder_path):
        os.makedirs(driver_folder_path)

    # 获取文件夹中所有chromedriver文件
    driver_list = [filename for filename in os.listdir(driver_folder_path) if filename.startswith('chromedriver')]

    for item in config:
        filename = item['name']
        if platform.system() == 'Windows':
            filename = filename + ".exe"
        local_file_path = os.path.join(driver_folder_path, filename)
        if filename in driver_list:
            # 判断sha1是否一致
            file_sha1 = encrypt_sha1(local_file_path)
            if file_sha1 == item['sha1']:
                print(f"驱动{filename}已存在，sha1校验通过...")
            else:
                print(f"驱动{filename}的sha1不一致，重新下载...")
                download_file(item['url'], local_file_path)
                # mac首次下载修改文件权限
                if platform.system() == 'Darwin':
                    cmd = ['chmod', '+x', local_file_path]
                    subprocess.Popen(cmd)
        else:
            print(f"驱动{filename}不存在，开始下载...")
            download_file(item['url'], local_file_path)
            # mac首次下载修改文件权限
            if platform.system() == 'Darwin':
                cmd = ['chmod', '+x', local_file_path]
                subprocess.Popen(cmd)


def find_file_dialog_input():
    """查找文件选择对话框的文件名输入框"""
    def callback(hwnd, extra):
        if win32gui.IsWindowVisible(hwnd):
            # 获取窗口类名
            class_name = win32gui.GetClassName(hwnd)
            # 获取窗口标题
            title = win32gui.GetWindowText(hwnd)
            
            # 检查是否是文件选择对话框
            if "打开" in title or "Open" in title:
                # 查找编辑框控件
                def find_edit(hwnd, extra):
                    if win32gui.IsWindowVisible(hwnd):
                        class_name = win32gui.GetClassName(hwnd)
                        if class_name == "Edit":
                            # 获取控件位置
                            rect = win32gui.GetWindowRect(hwnd)
                            # 计算中心点
                            x = (rect[0] + rect[2]) // 2
                            y = (rect[1] + rect[3]) // 2
                            extra.append((x, y))
                    return True
                
                edit_positions = []
                win32gui.EnumChildWindows(hwnd, find_edit, edit_positions)
                
                # 通常文件名输入框是最后一个Edit控件
                if edit_positions:
                    extra.append(edit_positions[-1])
        return True
    
    positions = []
    win32gui.EnumWindows(callback, positions)
    return positions[0] if positions else None 

class MemoryMonitor:
    """
    实时内存监控器
    监控系统内存使用情况，在内存使用率过高时主动进行优化
    """
    
    def __init__(self, threshold_percent=65, check_interval=3):
        """
        初始化内存监控器
        
        Args:
            threshold_percent: 内存使用率阈值，超过此值将触发优化
            check_interval: 检查间隔（秒）
        """
        self.threshold_percent = threshold_percent
        self.check_interval = check_interval
        self.last_check_time = 0
        self.optimization_count = 0
        self.max_optimizations_per_session = 10  # 每个会话最多优化次数
        
    def check_memory_usage(self, country=""):
        """
        检查内存使用情况
        
        Returns:
            bool: 是否需要优化
        """
        try:
            import psutil
            
            # 获取系统内存信息
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_gb = memory.used / (1024**3)
            memory_total_gb = memory.total / (1024**3)
            
            log_info(f"{country}: 内存使用情况 - 使用率: {memory_percent:.1f}%, 已用: {memory_used_gb:.1f}GB, 总计: {memory_total_gb:.1f}GB")
            
            # 检查是否超过阈值
            if memory_percent > self.threshold_percent:
                log_warning(f"{country}: 内存使用率过高({memory_percent:.1f}%)，建议进行内存优化")
                return True
            
            return False
            
        except Exception as e:
            log_error(f"{country}: 内存检查失败: {str(e)}")
            return False
    
    def should_optimize(self, country=""):
        """
        判断是否应该进行内存优化
        
        Returns:
            bool: 是否应该优化
        """
        current_time = time.time()
        
        # 检查时间间隔
        if current_time - self.last_check_time < self.check_interval:
            return False
        
        # 检查优化次数限制
        if self.optimization_count >= self.max_optimizations_per_session:
            log_warning(f"{country}: 已达到最大内存优化次数({self.max_optimizations_per_session})")
            return False
        
        # 检查内存使用情况
        if self.check_memory_usage(country):
            self.optimization_count += 1
            self.last_check_time = current_time
            return True
        
        self.last_check_time = current_time
        return False
    
    def get_memory_stats(self, country=""):
        """
        获取详细的内存统计信息
        """
        try:
            import psutil
            
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            stats = {
                "memory_percent": memory.percent,
                "memory_used_gb": memory.used / (1024**3),
                "memory_total_gb": memory.total / (1024**3),
                "swap_percent": swap.percent,
                "swap_used_gb": swap.used / (1024**3),
                "swap_total_gb": swap.total / (1024**3)
            }
            
            log_info(f"{country}: 内存统计 - 内存: {stats['memory_percent']:.1f}% ({stats['memory_used_gb']:.1f}GB/{stats['memory_total_gb']:.1f}GB), "
                    f"交换: {stats['swap_percent']:.1f}% ({stats['swap_used_gb']:.1f}GB/{stats['swap_total_gb']:.1f}GB)")
            
            return stats
            
        except Exception as e:
            log_error(f"{country}: 获取内存统计失败: {str(e)}")
            return None