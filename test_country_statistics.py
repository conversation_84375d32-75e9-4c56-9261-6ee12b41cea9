#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的国家统计功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from script.shopSetup import ShopSetup

def test_country_statistics():
    """测试国家统计功能"""
    print("=" * 60)
    print("🧪 测试新的国家统计功能")
    print("=" * 60)
    
    # 创建ShopSetup实例
    shop_setup = ShopSetup()
    
    # 模拟李四店铺的统计数据
    print("\n📝 模拟李四店铺的统计数据...")
    
    # 李四店铺成功的站点
    shop_setup.record_country_result("李四店铺", "加拿大", True)
    shop_setup.record_country_result("李四店铺", "日本", True)
    shop_setup.record_country_result("李四店铺", "新加坡", True)
    
    # 李四店铺失败的站点
    shop_setup.record_country_result("李四店铺", "瑞典", False, "登录步骤", "密码错误")
    shop_setup.record_country_result("李四店铺", "俄罗斯", False, "设置保存步骤", "网络超时")
    shop_setup.record_country_result("李四店铺", "美国", False, "页面导航步骤", "元素未找到")
    
    # 模拟张三店铺的统计数据
    print("📝 模拟张三店铺的统计数据...")
    
    # 张三店铺失败的站点
    shop_setup.record_country_result("张三店铺", "新加坡", False, "模块执行步骤", "配置错误")
    
    # 模拟王五店铺的统计数据（全部成功）
    print("📝 模拟王五店铺的统计数据...")
    
    # 王五店铺成功的站点
    shop_setup.record_country_result("王五店铺", "英国", True)
    shop_setup.record_country_result("王五店铺", "德国", True)
    
    # 测试店铺级别失败
    print("📝 模拟赵六店铺的店铺级别失败...")
    shop_setup.record_store_level_failure("赵六店铺", ["法国", "意大利", "西班牙"], "浏览器启动", "浏览器启动失败")
    
    print("\n" + "=" * 60)
    print("📊 打印统计结果")
    print("=" * 60)
    
    # 打印新的国家统计
    shop_setup.print_country_statistics()
    
    print("\n✅ 测试完成！")
    print("=" * 60)

if __name__ == "__main__":
    test_country_statistics()
