#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的Socket方式启动和关闭浏览器
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from script.shopSetup import ShopSetup

class MockSocketClient:
    """模拟Socket客户端"""
    def __init__(self, scenario="success"):
        self.scenario = scenario
        self.is_connected = False
    
    def get_browser_list(self):
        """模拟获取店铺列表"""
        if self.scenario == "success":
            return [
                {"browserOauth": "test_store_1", "browserName": "测试店铺1"},
                {"browserOauth": "test_store_2", "browserName": "测试店铺2"}
            ]
        return None
    
    def start_browser(self, browser_oauth, is_headless):
        """模拟启动浏览器"""
        if self.scenario == "success":
            return {
                "launcherPage": "https://test.com",
                "debuggingPort": 9222,
                "browserOauth": browser_oauth
            }
        return None
    
    def stop_browser(self, browser_oauth):
        """模拟关闭浏览器"""
        return self.scenario == "success"

class TestableShopSetup(ShopSetup):
    """可测试的ShopSetup类"""
    
    def __init__(self, scenario="success"):
        # 只初始化必要的属性
        self.country_statistics = {}
        self.socket_initialized = True
        self.socket_client = MockSocketClient(scenario)
        
    def initialize_socket_client(self):
        """重写Socket客户端初始化"""
        return self.socket_client.scenario == "success"

def test_socket_optimization():
    """测试Socket优化"""
    print("=" * 60)
    print("🧪 测试Socket方式优化")
    print("=" * 60)
    
    # 测试场景1：成功启动和关闭
    print("\n📝 测试场景1：Socket方式成功启动和关闭")
    try:
        shop_setup = TestableShopSetup("success")
        
        # 测试获取店铺列表
        print("  测试获取店铺列表...")
        browser_list = shop_setup.get_browser_list_smart()
        if browser_list:
            print(f"  ✅ 成功获取到 {len(browser_list)} 个店铺")
        else:
            print("  ❌ 获取店铺列表失败")
        
        # 测试启动浏览器
        print("  测试启动浏览器...")
        store_info = {"browserOauth": "test_store_1"}
        result = shop_setup.start_browser_with_config(store_info)
        if result:
            print("  ✅ 浏览器启动成功")
            print(f"    调试端口: {result.get('debuggingPort')}")
        else:
            print("  ❌ 浏览器启动失败")
        
        # 测试关闭浏览器
        print("  测试关闭浏览器...")
        close_result = shop_setup.close_store_smart("test_store_1")
        if close_result:
            print("  ✅ 浏览器关闭成功")
        else:
            print("  ❌ 浏览器关闭失败")
            
    except Exception as e:
        print(f"  ❌ 测试异常: {str(e)}")
    
    # 测试场景2：Socket失败
    print("\n📝 测试场景2：Socket方式失败")
    try:
        shop_setup = TestableShopSetup("fail")
        
        # 测试获取店铺列表失败
        print("  测试获取店铺列表失败...")
        browser_list = shop_setup.get_browser_list_smart()
        if browser_list is None:
            print("  ✅ 正确处理获取店铺列表失败")
        else:
            print("  ❌ 应该失败但成功了")
        
        # 测试启动浏览器失败
        print("  测试启动浏览器失败...")
        store_info = {"browserOauth": "test_store_1"}
        result = shop_setup.start_browser_with_config(store_info)
        if result is None:
            print("  ✅ 正确处理浏览器启动失败")
        else:
            print("  ❌ 应该失败但成功了")
        
        # 测试关闭浏览器失败
        print("  测试关闭浏览器失败...")
        close_result = shop_setup.close_store_smart("test_store_1")
        if close_result is False:
            print("  ✅ 正确处理浏览器关闭失败")
        else:
            print("  ❌ 应该失败但成功了")
            
    except Exception as e:
        print(f"  ❌ 测试异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print("📊 优化总结")
    print("=" * 60)
    print("✅ 删除了HTTP回退机制")
    print("✅ 只使用Socket方式进行通信")
    print("✅ 简化了启动和关闭逻辑")
    print("✅ 提高了代码可维护性")
    print("✅ 减少了不必要的复杂性")
    print("=" * 60)

if __name__ == "__main__":
    test_socket_optimization()
