import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from global_logger import log_task_start, log_step, log_success, log_error, log_info, log_warning, log_task_end
from gui_framework import ConfigurableGUI, ComponentConfig
from ziniao_rpa_base import BaseZiniaoRPA, MemoryMonitor, ThrottledOperationManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.common import TimeoutException, NoSuchElementException
import time
import traceback
from selenium.webdriver.support.ui import Select


class SmartWaitManager:
    """智能等待管理器，根据上下文动态调整等待时间"""

    def __init__(self, driver):
        self.driver = driver
        self.original_wait = None
        self.context_waits = {
            "page_loading": 8,      # 页面加载时需要较长等待
            "popup_detection": 2,   # 弹窗检测时短等待
            "element_interaction": 4,  # 元素交互时中等等待
            "normal": 3            # 正常情况
        }

    def set_context_wait(self, context):
        """根据上下文设置合适的等待时间"""
        new_wait = self.context_waits.get(context, 3)
        self.driver.implicitly_wait(new_wait)
        log_info(f"设置隐式等待: {new_wait}秒 (上下文: {context})")

    def __enter__(self):
        # 保存原始等待时间
        try:
            self.original_wait = self.driver.timeouts.implicit_wait
        except:
            self.original_wait = 5  # 默认值
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # 恢复原始等待时间
        if self.original_wait is not None:
            self.driver.implicitly_wait(self.original_wait)


class ShopSetup(BaseZiniaoRPA):
    def __init__(self):
        super().__init__()
        # 用于跟踪已记录的错误，避免重复记录
        # 格式: {(店铺名, 国家, 流程类型): True}
        self.logged_errors = set()
        
        # 模块配置系统
        self.module_config = self.get_default_module_config()
        
        # 浏览器配置已移除无头模式（紫鸟浏览器不支持）
        self.browser_config = {}
        
        # 内存管理配置
        self.memory_check_interval = 5  # 每5个操作检查一次内存
        self.operation_count = 0
        
        # 初始化内存监控器
        self.memory_monitor = MemoryMonitor(threshold_percent=75, check_interval=5)
        
        # 初始化节流管理器
        self.throttle_manager = ThrottledOperationManager()
        
        # 崩溃恢复机制
        self.breakpoint_manager = {
            'url': None,
            'country': None,
            'store_info': None,
            'edit_page_loaded': False,
            'execution_point': None
        }
        
        # 店铺重试计数器
        self.store_recovery_attempts = {}

        # 初始化任务统计
        self.task_statistics = {
            'total_stores': 0,
            'successful_stores': 0,
            'failed_stores': [],
            'total_countries': 0,
            'successful_countries': 0,
            'failed_countries': [],
            'login_failures': [],
            'setting_failures': [],
            'module_failures': [],      # 模块执行失败
            'save_failures': [],        # 保存设置失败
            'navigation_failures': [], # 页面导航失败
            'exception_failures': []   # 异常导致的失败
        }

        # ChromeDriver管理器初始化完成
        log_info("✅ ChromeDriver管理器初始化完成，将按需下载驱动")
    
    @staticmethod
    def crash_recovery_decorator(func):
        """
        崩溃恢复装饰器，统一处理配送设置相关的崩溃恢复
        
        Args:
            func: 被装饰的函数
            
        Returns:
            装饰后的函数
        """
        def wrapper(self, driver, country, store_info, *args, **kwargs):
            try:
                return func(self, driver, country, store_info, *args, **kwargs)
            except Exception as e:
                error_msg = str(e).lower()
                if "tab crashed" in error_msg or "chrome not reachable" in error_msg:
                    log_error(f"{country}: 检测到崩溃错误: {error_msg}")
                    log_info(f"{country}: 尝试使用新的崩溃恢复机制...")
                    
                    # 使用新的崩溃恢复方法
                    if self.recover_from_crash_with_refresh(driver, country, store_info):
                        log_success(f"{country}: 崩溃恢复成功，重新尝试执行操作")
                        # 重新尝试执行原函数
                        return func(self, driver, country, store_info, *args, **kwargs)
                    else:
                        log_error(f"{country}: 崩溃恢复失败，终止操作")
                        raise e
                else:
                    # 非崩溃错误，直接抛出
                    raise e
        return wrapper
    
    def start_browser_with_config(self, store_info):
        """使用配置启动浏览器（优先Socket，失败后HTTP兜底）"""
        # 紫鸟浏览器统一使用普通模式（不支持无头模式）
        is_headless = 0

        log_info(f"🖥️ 使用普通模式启动浏览器（紫鸟浏览器不支持无头模式）")

        # 优先尝试Socket方式启动
        log_step("🚀 优先使用Socket通信方式启动浏览器...")
        socket_result = self.open_store_with_socket(store_info, isHeadless=is_headless)

        if socket_result:
            log_success("✅ Socket方式启动成功")
            return socket_result

        # Socket方式失败，检查是否已经通过回退机制启动了HTTP
        log_warning("⚠️ Socket方式启动失败")

        # 如果Socket方式内部已经处理了回退，socket_result可能是None但HTTP已启动
        # 这种情况下我们需要重新尝试获取启动结果
        log_step("🔄 检查HTTP回退结果...")

        # 等待一下，让HTTP服务完全启动
        time.sleep(2)

        # 直接尝试HTTP方式（此时应该已经通过回退机制启动了HTTP服务）
        log_step("🔄 使用HTTP方式启动浏览器...")

        # 如果是无头模式，提醒用户HTTP方式不支持
        if is_headless:
            log_warning(f"⚠️ HTTP方式不支持无头模式，将使用普通模式启动")
            log_info(f"💡 建议：如需后台运行，请手动最小化浏览器窗口")

        # 调用原有的HTTP方式
        http_result = self.open_store(store_info, isHeadless=is_headless)

        if http_result:
            log_success("✅ HTTP方式启动成功")
            return http_result
        else:
            log_error("❌ 所有启动方式都失败")
            return None

    def use_one_browser_run_task(self, browser, selected_countries, upload_file_path):
        """
        重写基类方法，使用配置启动浏览器（支持无头模式）

        :param browser: 浏览器配置信息
        :param selected_countries: 选择的国家列表
        :param upload_file_path: 上传文件路径
        """
        browser_oauth = browser['browserOauth']
        browser_name = browser['browserName']

        # 设置当前店铺名称用于错误记录
        self.current_store_name = browser_name

        # 更新统计
        self.task_statistics['total_stores'] += 1
        self.task_statistics['total_countries'] += len(selected_countries)

        log_task_start(f"店铺 {browser_name} 任务")
        log_info(f"计划处理站点: {', '.join(selected_countries)}")

        try:
            # 检查任务控制状态
            if not self.check_task_control():
                log_warning("任务被停止，跳过店铺处理")
                self.record_store_failure(browser_name, selected_countries, "任务被用户停止")
                return

            # 使用配置启动浏览器（支持无头模式）
            log_step(f"正在启动店铺 {browser_name} 的浏览器...")
            open_ret = self.start_browser_with_config(browser_oauth)

            if not open_ret:
                log_error(f"启动店铺 {browser_name} 失败")
                self.record_store_failure(browser_name, selected_countries, "浏览器启动失败")
                return

            log_success(f"店铺 {browser_name} 浏览器启动成功")

            # 再次检查任务控制状态
            if not self.check_task_control():
                log_warning("任务被停止，关闭浏览器")
                self.close_store_smart(browser_oauth)
                self.record_store_failure(browser_name, selected_countries, "任务被用户停止")
                return

            # 获取WebDriver（优先Socket，失败后HTTP兜底）
            log_step("正在初始化WebDriver...")
            # 紫鸟浏览器统一使用普通模式
            is_headless = 0

            # 优先尝试Socket版本的WebDriver连接
            log_step("🚀 优先使用Socket方式连接WebDriver...")
            driver = self.get_driver_with_socket(open_ret, isHeadless=is_headless)

            if not driver:
                # Socket方式失败，使用HTTP方式兜底
                log_warning("⚠️ Socket方式WebDriver连接失败，使用HTTP方式兜底")
                log_step("🔄 回退到HTTP方式连接WebDriver...")
                driver = self.get_driver(open_ret, isHeadless=is_headless)

            if not driver:
                log_error("❌ 所有方式的WebDriver初始化都失败")
                self.close_store_smart(browser_oauth)
                self.record_store_failure(browser_name, selected_countries, "WebDriver初始化失败")
                return

            log_success("✅ WebDriver初始化成功")

            try:
                # 设置元素查找等待时间-全局隐式等待
                driver.implicitly_wait(5)

                # 执行店铺任务（调用现有的任务执行方法）
                task_result = self.execute_store_task(driver, selected_countries, upload_file_path, browser)

                # 根据任务结果更新统计
                if task_result:
                    self.task_statistics['successful_stores'] += 1
                    log_success(f"店铺 {browser_name} 任务执行成功")
                else:
                    self.record_store_failure(browser_name, selected_countries, "任务执行失败")

            except Exception as e:
                log_error(f"店铺 {browser_name} 任务执行异常: {str(e)}")
                import traceback
                log_error(f"详细错误信息: {traceback.format_exc()}")
                self.record_store_failure(browser_name, selected_countries, f"任务执行异常: {str(e)}")
            finally:
                # 确保关闭浏览器
                try:
                    if driver:
                        driver.quit()
                except:
                    pass
                try:
                    self.close_store_smart(browser_oauth)
                except:
                    pass

        except Exception as e:
            log_error(f"店铺 {browser_name} 处理异常: {str(e)}")
            self.record_store_failure(browser_name, selected_countries, f"店铺处理异常: {str(e)}")
        finally:
            log_task_end(f"店铺 {browser_name} 任务")

    def close_store_smart(self, browser_oauth):
        """智能关闭店铺（优先Socket，失败后HTTP兜底）"""
        try:
            log_step(f"🚀 优先使用Socket方式关闭店铺: {browser_oauth}")

            # 优先尝试Socket方式
            if self.close_store_with_socket(browser_oauth):
                log_success("✅ Socket方式关闭店铺成功")
                return True

            # Socket方式失败，使用HTTP方式兜底
            log_warning("⚠️ Socket方式关闭失败，使用HTTP方式兜底")
            log_step(f"🔄 回退到HTTP方式关闭店铺: {browser_oauth}")

            result = self.close_store(browser_oauth)
            if result:
                log_success("✅ HTTP方式关闭店铺成功")
                return True
            else:
                log_error("❌ HTTP方式关闭店铺也失败")
                return False

        except Exception as e:
            log_error(f"关闭店铺异常: {str(e)}")
            return False

    def get_browser_list_smart(self):
        """智能获取店铺列表（优先Socket，失败后HTTP兜底）"""
        try:
            log_step("🚀 优先使用Socket方式获取店铺列表...")

            # 优先尝试Socket方式
            browser_list = self.get_browser_list_with_socket()
            if browser_list:
                log_success(f"✅ Socket方式获取到 {len(browser_list)} 个店铺")
                return browser_list

            # Socket方式失败，使用HTTP方式兜底
            log_warning("⚠️ Socket方式获取店铺列表失败，使用HTTP方式兜底")
            log_step("🔄 回退到HTTP方式获取店铺列表...")

            # 调用原有的HTTP方式
            http_result = self.browser_list()
            if http_result:
                log_success("✅ HTTP方式获取店铺列表成功")
                return http_result
            else:
                log_error("❌ HTTP方式获取店铺列表也失败")
                return None

        except Exception as e:
            log_error(f"获取店铺列表异常: {str(e)}")
            return None

    def record_login_failure(self, store_name, error_reason, current_url=""):
        """记录登录失败信息"""
        failure_info = {
            'store_name': store_name,
            'error_reason': error_reason,
            'current_url': current_url,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        self.task_statistics['login_failures'].append(failure_info)

        # 记录详细的失败日志
        log_error(f"🚫 登录失败记录")
        log_error(f"   店铺: {store_name}")
        log_error(f"   失败原因: {error_reason}")
        log_error(f"   当前URL: {current_url}")
        log_error(f"   影响: 该店铺的所有站点设置将被跳过")

    def record_store_failure(self, store_name, countries, failure_reason):
        """记录店铺失败信息"""
        failure_info = {
            'store_name': store_name,
            'countries': countries,
            'failure_reason': failure_reason,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        self.task_statistics['failed_stores'].append(failure_info)

        # 记录详细的失败日志
        log_error(f"🚫 店铺失败记录")
        log_error(f"   店铺: {store_name}")
        log_error(f"   计划处理站点: {', '.join(countries) if countries else '未知'}")
        log_error(f"   失败原因: {failure_reason}")
        log_error(f"   结果: 该店铺的所有站点设置失败")

    def record_country_failure(self, store_name, country, failure_reason, failure_type="setting"):
        """记录单个国家/站点的失败信息"""
        failure_info = {
            'store_name': store_name,
            'country': country,
            'failure_reason': failure_reason,
            'failure_type': failure_type,  # 'setting', 'navigation', 'save', 'exception'
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }

        # 根据失败类型分类记录
        if failure_type == "setting":
            self.task_statistics['setting_failures'].append(failure_info)
        elif failure_type == "navigation":
            self.task_statistics['navigation_failures'].append(failure_info)
        elif failure_type == "save":
            self.task_statistics['save_failures'].append(failure_info)
        elif failure_type == "exception":
            self.task_statistics['exception_failures'].append(failure_info)

        # 同时记录到失败国家列表
        self.task_statistics['failed_countries'].append(failure_info)

        # 记录详细的失败日志
        log_error(f"🚫 {country}站点失败记录")
        log_error(f"   店铺: {store_name}")
        log_error(f"   站点: {country}")
        log_error(f"   失败类型: {failure_type}")
        log_error(f"   失败原因: {failure_reason}")
        log_error(f"   结果: 该站点的设置未能成功保存")

    def record_module_failure(self, store_name, country, module_name, failure_reason):
        """记录模块执行失败信息"""
        failure_info = {
            'store_name': store_name,
            'country': country,
            'module_name': module_name,
            'failure_reason': failure_reason,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        self.task_statistics['module_failures'].append(failure_info)

        # 记录详细的失败日志
        log_error(f"🚫 模块执行失败记录")
        log_error(f"   店铺: {store_name}")
        log_error(f"   站点: {country}")
        log_error(f"   模块: {module_name}")
        log_error(f"   失败原因: {failure_reason}")
        log_error(f"   结果: 该模块的设置未能完成")

    def record_country_success(self, store_name, country):
        """记录单个国家/站点的成功信息"""
        self.task_statistics['successful_countries'] += 1
        log_success(f"✅ {country}站点设置成功 - {store_name}")

    def print_task_summary(self):
        """打印任务执行总结"""
        stats = self.task_statistics

        log_info("=" * 60)
        log_info("📊 任务执行总结报告")
        log_info("=" * 60)

        # 店铺统计
        log_info(f"🏪 店铺处理统计:")
        log_info(f"   总店铺数: {stats['total_stores']}")
        log_info(f"   成功店铺数: {stats['successful_stores']}")
        log_info(f"   失败店铺数: {len(stats['failed_stores'])}")

        # 站点统计
        log_info(f"🌍 站点处理统计:")
        log_info(f"   总站点数: {stats['total_countries']}")
        log_info(f"   成功站点数: {stats['successful_countries']}")
        log_info(f"   失败站点数: {len(stats['failed_countries'])}")

        # 登录失败详情
        if stats['login_failures']:
            log_error(f"🚫 登录失败详情 ({len(stats['login_failures'])}个):")
            for failure in stats['login_failures']:
                log_error(f"   • {failure['store_name']}: {failure['error_reason']}")

        # 店铺失败详情
        if stats['failed_stores']:
            log_error(f"🚫 店铺失败详情 ({len(stats['failed_stores'])}个):")
            for failure in stats['failed_stores']:
                log_error(f"   • {failure['store_name']}: {failure['failure_reason']}")
                if failure['countries']:
                    log_error(f"     影响站点: {', '.join(failure['countries'])}")

        # 站点失败详情（按类型分组）
        if stats['failed_countries']:
            log_error(f"🚫 站点失败详情 ({len(stats['failed_countries'])}个):")

            # 按失败类型分组显示
            failure_types = {
                'setting': '设置失败',
                'navigation': '页面导航失败',
                'save': '保存设置失败',
                'exception': '异常导致失败'
            }

            for failure_type, type_name in failure_types.items():
                type_failures = [f for f in stats['failed_countries'] if f.get('failure_type') == failure_type]
                if type_failures:
                    log_error(f"   📋 {type_name} ({len(type_failures)}个):")
                    for failure in type_failures:
                        log_error(f"      • {failure.get('store_name', '未知店铺')} - {failure.get('country', '未知站点')}: {failure.get('failure_reason', '未知原因')}")

        # 模块失败详情
        if stats['module_failures']:
            log_error(f"🚫 模块失败详情 ({len(stats['module_failures'])}个):")
            for failure in stats['module_failures']:
                log_error(f"   • {failure['store_name']} - {failure['country']} - {failure['module_name']}: {failure['failure_reason']}")

        # 保存失败详情
        if stats['save_failures']:
            log_error(f"🚫 保存失败详情 ({len(stats['save_failures'])}个):")
            for failure in stats['save_failures']:
                log_error(f"   • {failure['store_name']} - {failure['country']}: {failure['failure_reason']}")

        # 成功率计算
        store_success_rate = (stats['successful_stores'] / max(stats['total_stores'], 1)) * 100
        country_success_rate = (stats['successful_countries'] / max(stats['total_countries'], 1)) * 100

        log_info(f"📈 成功率统计:")
        log_info(f"   店铺成功率: {store_success_rate:.1f}%")
        log_info(f"   站点成功率: {country_success_rate:.1f}%")

        # 失败类型统计
        total_failures = len(stats['login_failures']) + len(stats['failed_countries']) + len(stats['module_failures'])
        if total_failures > 0:
            log_info(f"📊 失败类型统计:")
            log_info(f"   登录失败: {len(stats['login_failures'])}个")
            log_info(f"   设置失败: {len([f for f in stats['failed_countries'] if f.get('failure_type') == 'setting'])}个")
            log_info(f"   导航失败: {len([f for f in stats['failed_countries'] if f.get('failure_type') == 'navigation'])}个")
            log_info(f"   保存失败: {len([f for f in stats['failed_countries'] if f.get('failure_type') == 'save'])}个")
            log_info(f"   异常失败: {len([f for f in stats['failed_countries'] if f.get('failure_type') == 'exception'])}个")
            log_info(f"   模块失败: {len(stats['module_failures'])}个")

        # 建议
        if stats['login_failures']:
            log_warning(f"💡 建议: 检查登录凭据或处理MFA认证问题")
        if len(stats['failed_stores']) > 0:
            log_warning(f"💡 建议: 检查失败店铺的网络连接和页面状态")
        if len([f for f in stats['failed_countries'] if f.get('failure_type') == 'save']) > 0:
            log_warning(f"💡 建议: 检查保存按钮是否可点击，网络是否稳定")
        if len([f for f in stats['failed_countries'] if f.get('failure_type') == 'navigation']) > 0:
            log_warning(f"💡 建议: 检查页面加载是否完整，元素定位是否正确")
        if len(stats['module_failures']) > 0:
            log_warning(f"💡 建议: 检查模块配置和页面结构是否发生变化")

        log_info("=" * 60)

    def recover_from_webdriver_crash(self, crashed_driver, store_info, country="", recovery_context=None, isHeadless=0):
        """
        重写基类方法，紫鸟浏览器统一使用普通模式进行崩溃恢复
        """
        # 紫鸟浏览器统一使用普通模式
        is_headless = 0

        # 调用父类方法，传递普通模式参数
        return super().recover_from_webdriver_crash(crashed_driver, store_info, country, recovery_context, isHeadless=is_headless)

    def _handle_login_if_needed(self, driver):
        """
        重写登录处理方法，使用父类的智能登录处理并添加详细的失败记录
        """
        try:
            # 调用父类的智能登录处理方法
            login_result = super()._handle_login_if_needed(driver)

            if login_result:
                log_success("✅ 智能登录处理成功")
                return True
            else:
                # 登录失败，记录详细信息
                current_url = ""
                try:
                    current_url = driver.current_url
                except:
                    current_url = "无法获取URL"

                error_reason = f"智能登录处理失败 (URL: {current_url})"
                log_error(f"登录处理失败: {error_reason}")

                # 记录登录失败
                store_name = getattr(self, 'current_store_name', '未知店铺')
                self.record_login_failure(store_name, error_reason, current_url)
                return False

        except Exception as e:
            error_reason = f"登录处理过程中发生异常: {str(e)}"
            log_error(error_reason)

            # 记录登录失败
            store_name = getattr(self, 'current_store_name', '未知店铺')
            try:
                current_url = driver.current_url
            except:
                current_url = "无法获取URL"
            self.record_login_failure(store_name, error_reason, current_url)
            return False

    def set_task_control(self, task_control):
        """
        设置任务控制对象（由GUI调用）
        
        :param task_control: GUI的任务控制字典
        """
        self.task_control = task_control
    
    def check_task_control(self):
        """
        检查任务控制状态
        子类在执行长时间操作时应调用此方法
        
        :return: True继续执行，False应该停止
        """
        if not self.task_control:
            return True
        
        # 检查停止标志
        if self.task_control['stop_event'].is_set():
            log_warning("检测到停止信号，任务将终止")
            return False
        
        # 等待暂停状态结束
        self.task_control['pause_event'].wait()
        
        # 暂停后再次检查停止标志
        if self.task_control['stop_event'].is_set():
            log_warning("检测到停止信号，任务将终止")
            return False
        
        return True
    
    def wait_if_paused(self):
        """
        如果任务暂停则等待
        子类可以在合适的位置调用此方法来响应暂停
        """
        if self.task_control and self.task_control['is_paused']:
            log_info("任务已暂停，等待继续...")
            self.task_control['pause_event'].wait()
            log_info("任务已恢复")
    

        
    def get_default_module_config(self):
        """获取默认的模块配置"""
        return {
            "return_settings": {
                "name": "退货设置模块",
                "enabled": True,
                "description": "处理店铺退货政策和地址设置",
                "is_main_task": True,
                "sub_modules": {}
            },
            "shipping_settings": {
                "name": "配送设置模块", 
                "enabled": True,
                "description": "处理配送模板和运费设置",
                "is_main_task": False,
                "sub_modules": {
                    "cancel_non_standard_shipping": {
                        "name": "取消非标准配送勾选",
                        "enabled": True,
                        "description": "取消勾选非标准配送服务选项"
                    },
                    "special_region_shipping": {
                        "name": "特殊地区运费设置",
                        "enabled": True,
                        "description": "设置特殊地区(如夏威夷、阿拉斯加、德国配送)的运费"
                    }
                }
            },
            "fba_settings": {
                "name": "亚马逊物流(FBA)设置模块",
                "enabled": True,
                "description": "处理FBA相关设置(仅美国)",
                "is_main_task": False,
                "country_filter": ["美国"],  # 仅对特定国家生效
                "sub_modules": {
                    "inbound_settings": {
                        "name": "入库设置",
                        "enabled": True,
                        "description": "配置商品入库相关设置"
                    },
                    "unfulfillable_settings": {
                        "name": "不可售商品设置",
                        "enabled": True,
                        "description": "配置不可售商品处理方式"
                    },
                    "barcode_preferences": {
                        "name": "条形码首选项",
                        "enabled": True,
                        "description": "设置商品条形码偏好选项"
                    },
                    "sellable_settings": {
                        "name": "可售商品设置",
                        "enabled": True,
                        "description": "配置可售商品自动化设置"
                    }
                }
            }
        }

    def update_module_config(self, config):
        """更新模块配置"""
        self.module_config = config
        log_info("模块配置已更新")

    def get_enabled_modules(self):
        """获取启用的模块列表"""
        enabled_modules = {}
        for module_id, module_info in self.module_config.items():
            if module_info.get("enabled", False):
                enabled_modules[module_id] = module_info
        return enabled_modules

    def is_module_enabled(self, module_id, sub_module_id=None):
        """检查模块是否启用"""
        if module_id not in self.module_config:
            return False

        main_module = self.module_config[module_id]
        if not main_module.get("enabled", False):
            return False

        if sub_module_id:
            sub_modules = main_module.get("sub_modules", {})
            if sub_module_id not in sub_modules:
                return False
            return sub_modules[sub_module_id].get("enabled", False)

        return True

    def get_task_name(self):
        return "店铺设置"

    def requires_upload_folder(self):
        """
        重写此方法，指定此任务不需要上传文件夹
        """
        return False

    def requires_country_iteration(self):
        """
        店铺设置任务是否需要国家遍历
        """
        return True

    def execute_country_task(self, driver, country, upload_file_path, store_info):
        """
        执行每个国家的店铺设置任务（模块化版本）：
        根据用户选择的模块配置执行对应任务
        """
        store_name = store_info.get('browserName', '未知店铺')

        try:
            # 保存store_info到实例属性，确保后续方法能获取到店铺名
            self.current_store_info = store_info

            log_task_start(f"开始处理 {country} 的模块化任务")
            enabled_modules = self.get_enabled_modules()

            # 检查该国家需要执行的模块
            country_modules = self.get_modules_for_country(country)
            log_info(f"{country}: 需执行模块: {', '.join(country_modules)}")

            success_count = 0
            total_modules = len(country_modules)

            # 如果没有需要执行的模块，记录为成功
            if total_modules == 0:
                log_info(f"{country}: 该站点无需执行任何模块")
                self.record_country_success(store_name, country)
                return True

            # 执行配送设置模块
            if "shipping_settings" in country_modules:
                log_step(f"=== {country}: 开始配送设置模块 ===")
                try:
                    if self.execute_shipping_settings_module(driver, country, store_info):
                        success_count += 1
                        log_success(f"{country}: 配送设置模块完成")
                    else:
                        log_error(f"{country}: 配送设置模块失败")
                        self.log_setting_exception(store_info, country, "配送设置模块", "配送设置模块执行失败")
                        self.record_module_failure(store_name, country, "配送设置", "模块执行返回失败")
                except Exception as e:
                    log_error(f"{country}: 配送设置模块执行异常: {str(e)}")
                    self.log_setting_exception(store_info, country, "配送设置模块", f"配送设置模块执行异常: {str(e)}")
                    self.record_module_failure(store_name, country, "配送设置", f"模块执行异常: {str(e)}")

            # 执行亚马逊物流(FBA)设置模块
            if "fba_settings" in country_modules:
                log_step(f"=== {country}: 开始亚马逊物流(FBA)设置模块 ===")
                try:
                    if self.execute_fba_settings_module(driver, country, store_info):
                        success_count += 1
                        log_success(f"{country}: 亚马逊物流(FBA)设置模块完成")
                    else:
                        log_error(f"{country}: 亚马逊物流(FBA)设置模块失败")
                        self.log_setting_exception(store_info, country, "亚马逊物流(FBA)设置模块", "亚马逊物流(FBA)设置模块执行失败")
                        self.record_module_failure(store_name, country, "FBA设置", "模块执行返回失败")
                except Exception as e:
                    log_error(f"{country}: 亚马逊物流(FBA)设置模块执行异常: {str(e)}")
                    self.log_setting_exception(store_info, country, "亚马逊物流(FBA)设置模块", f"亚马逊物流(FBA)设置模块执行异常: {str(e)}")
                    self.record_module_failure(store_name, country, "FBA设置", f"模块执行异常: {str(e)}")

            # 汇总结果
            if total_modules == 0:
                log_info(f"{country}: 没有启用的模块需要执行")
                self.record_country_success(store_name, country)
                return True
            elif success_count == total_modules:
                log_success(f"{country}: 所有模块执行成功 ({success_count}/{total_modules})")
                self.record_country_success(store_name, country)
                return True
            else:
                log_warning(f"{country}: 部分模块执行成功 ({success_count}/{total_modules})")
                failed_modules = total_modules - success_count
                if success_count > 0:
                    # 部分成功，记录为成功但也记录失败的模块
                    self.record_country_success(store_name, country)
                    log_warning(f"{country}: {failed_modules}个模块执行失败，但整体任务部分成功")
                else:
                    # 全部失败
                    self.record_country_failure(store_name, country, f"所有模块执行失败 ({total_modules}个模块全部失败)", "setting")
                return success_count > 0  # 只要有模块成功就算成功

        except Exception as e:
            log_error(f"执行{country}模块化任务时发生错误: {str(e)}")
            self.record_country_failure(store_name, country, f"任务执行异常: {str(e)}", "exception")
            return False

    def get_modules_for_country(self, country):
        """获取指定国家需要执行的模块列表"""
        enabled_modules = []

        for module_id, module_info in self.module_config.items():
            if not module_info.get("enabled", False):
                continue

            # 检查国家过滤器
            country_filter = module_info.get("country_filter", [])
            if country_filter and country not in country_filter:
                log_info(f"{country}: 跳过模块 {module_info['name']} (国家过滤)")
                continue

            # 跳过主任务模块（在execute_main_task中处理）
            if module_info.get("is_main_task", False):
                continue

            enabled_modules.append(module_id)

        return enabled_modules

    def execute_shipping_settings_module(self, driver, country, store_info):
        """执行配送设置模块"""
        try:
            log_step(f"{country}: 配送设置模块开始执行")

            # 设置断点，记录当前执行位置
            current_url = driver.current_url
            self.set_breakpoint(current_url, country, store_info, "shipping_settings_module_start")

            # 检查子模块配置
            sub_modules = self.module_config["shipping_settings"]["sub_modules"]

            # 执行主要的配送设置流程（导航到配送设置页面等）
            if not self.navigate_to_shipping_settings(driver, country, store_info):
                return False

            # 根据子模块配置执行相应操作
            sub_module_success_count = 0
            total_sub_modules = 0

            if self.is_module_enabled("shipping_settings", "cancel_non_standard_shipping"):
                total_sub_modules += 1
                log_step(f"{country}: 执行子模块 - 取消非标准配送勾选")
                try:
                    self.handle_shipping_services(driver, country)
                    sub_module_success_count += 1
                    log_success(f"{country}: 取消非标准配送勾选子模块执行成功")
                except Exception as e:
                    log_error(f"{country}: 取消非标准配送勾选子模块执行失败: {str(e)}")
                    self.log_setting_exception(store_info, country, "取消非标准配送勾选子模块", f"取消非标准配送勾选子模块执行失败: {str(e)}")
            else:
                log_info(f"{country}: 跳过子模块 - 取消非标准配送勾选")

            if self.is_module_enabled("shipping_settings", "special_region_shipping"):
                total_sub_modules += 1
                log_step(f"{country}: 执行子模块 - 特殊地区运费设置")
                try:
                    self.handle_special_regions(driver, country)
                    sub_module_success_count += 1
                    log_success(f"{country}: 特殊地区运费设置子模块执行成功")
                except Exception as e:
                    log_error(f"{country}: 特殊地区运费设置子模块执行失败: {str(e)}")
                    self.log_setting_exception(store_info, country, "特殊地区运费设置子模块", f"特殊地区运费设置子模块执行失败: {str(e)}")
            else:
                log_info(f"{country}: 跳过子模块 - 特殊地区运费设置")

            # 记录配送设置子模块执行情况
            if total_sub_modules > 0:
                log_info(f"{country}: 配送设置子模块执行情况: {sub_module_success_count}/{total_sub_modules}")

            # 保存配送设置
            log_step(f"{country}: 保存配送设置")
            result = self.save_shipping_settings_with_validation(driver, country, store_info)
            
            # 保存成功后清除断点
            if result:
                self.clear_breakpoint()
                log_success(f"{country}: 配送设置模块执行完成，断点已清除")
            
            return result

        except Exception as e:
            self.log_setting_exception(store_info, country, "配送设置", f"配送设置模块执行错误: {str(e)}")
            return False

    def execute_fba_settings_module(self, driver, country, store_info):
        """执行亚马逊物流(FBA)设置模块"""
        try:
            log_step(f"{country}: 亚马逊物流(FBA)设置模块开始执行")

            # 检查子模块配置
            sub_modules = self.module_config["fba_settings"]["sub_modules"]

            # 导航到FBA设置页面
            if not self.navigate_to_fba_settings(driver, country, store_info):
                return False

            # 首次页面健康检查
            initial_page_status = self.check_fba_page_health(driver, country)
            if initial_page_status != "正常":
                log_warning(f"{country}: FBA设置页面初始状态异常: {initial_page_status}")
                if not self.recover_fba_settings_page(driver, country, store_info):
                    log_error(f"{country}: FBA设置页面恢复失败，跳过FBA模块")
                    return False

            success_count = 0
            total_sub_modules = 0

            # 添加编辑页面状态跟踪
            self.in_edit_mode = False

            # 根据子模块配置执行相应操作
            if self.is_module_enabled("fba_settings", "inbound_settings"):
                total_sub_modules += 1
                log_step(f"{country}: 执行子模块 - 入库设置")
                try:
                    # 执行前检查页面健康状态（仅在非编辑模式下）
                    if not getattr(self, 'in_edit_mode', False):
                        page_status = self.check_fba_page_health(driver, country)
                        if page_status != "正常":
                            log_warning(f"{country}: 入库设置执行前页面状态异常: {page_status}")
                            if not self.recover_fba_settings_page(driver, country, store_info):
                                raise Exception("页面恢复失败")

                    self.handle_new_inbound_settings(driver, country, store_info)
                    success_count += 1
                    log_success(f"{country}: 入库设置子模块执行成功")
                except Exception as e:
                    log_error(f"{country}: 入库设置子模块执行失败: {str(e)}")
                    self.log_setting_exception(store_info, country, "入库设置子模块", f"入库设置子模块执行失败: {str(e)}")
                finally:
                    # 确保退出编辑模式
                    self.in_edit_mode = False
            else:
                log_info(f"{country}: 跳过子模块 - 入库设置")

            if self.is_module_enabled("fba_settings", "unfulfillable_settings"):
                total_sub_modules += 1
                log_step(f"{country}: 执行子模块 - 不可售商品设置")
                try:
                    # 执行前检查页面健康状态（仅在非编辑模式下）
                    if not getattr(self, 'in_edit_mode', False):
                        page_status = self.check_fba_page_health(driver, country)
                        if page_status != "正常":
                            log_warning(f"{country}: 不可售商品设置执行前页面状态异常: {page_status}")
                            if not self.recover_fba_settings_page(driver, country, store_info):
                                raise Exception("页面恢复失败")

                    self.handle_new_unfulfillable_settings(driver, country, store_info)
                    success_count += 1
                    log_success(f"{country}: 不可售商品设置子模块执行成功")
                except Exception as e:
                    log_error(f"{country}: 不可售商品设置子模块执行失败: {str(e)}")
                    self.log_setting_exception(store_info, country, "不可售商品设置子模块", f"不可售商品设置子模块执行失败: {str(e)}")
                    # 不增加success_count，保持失败状态
                finally:
                    # 确保退出编辑模式
                    self.in_edit_mode = False
            else:
                log_info(f"{country}: 跳过子模块 - 不可售商品设置")

            if self.is_module_enabled("fba_settings", "barcode_preferences"):
                total_sub_modules += 1
                log_step(f"{country}: 执行子模块 - 条形码首选项")
                try:
                    # 执行前检查页面健康状态（仅在非编辑模式下）
                    if not getattr(self, 'in_edit_mode', False):
                        page_status = self.check_fba_page_health(driver, country)
                        if page_status != "正常":
                            log_warning(f"{country}: 条形码首选项执行前页面状态异常: {page_status}")
                            if not self.recover_fba_settings_page(driver, country, store_info):
                                raise Exception("页面恢复失败")

                    self.handle_barcode_preferences(driver, country, store_info)
                    success_count += 1
                    log_success(f"{country}: 条形码首选项子模块执行成功")
                except Exception as e:
                    log_error(f"{country}: 条形码首选项子模块执行失败: {str(e)}")
                    self.log_setting_exception(store_info, country, "条形码首选项子模块", f"条形码首选项子模块执行失败: {str(e)}")
                finally:
                    # 确保退出编辑模式
                    self.in_edit_mode = False
            else:
                log_info(f"{country}: 跳过子模块 - 条形码首选项")

            if self.is_module_enabled("fba_settings", "sellable_settings"):
                total_sub_modules += 1
                log_step(f"{country}: 执行子模块 - 可售商品设置")
                try:
                    # 执行前检查页面健康状态（仅在非编辑模式下）
                    if not getattr(self, 'in_edit_mode', False):
                        page_status = self.check_fba_page_health(driver, country)
                        if page_status != "正常":
                            log_warning(f"{country}: 可售商品设置执行前页面状态异常: {page_status}")
                            if not self.recover_fba_settings_page(driver, country, store_info):
                                raise Exception("页面恢复失败")

                    self.handle_sellable_settings(driver, country, store_info)
                    success_count += 1
                    log_success(f"{country}: 可售商品设置子模块执行成功")
                except Exception as e:
                    log_error(f"{country}: 可售商品设置子模块执行失败: {str(e)}")
                    self.log_setting_exception(store_info, country, "可售商品设置子模块", f"可售商品设置子模块执行失败: {str(e)}")
                finally:
                    # 确保退出编辑模式
                    self.in_edit_mode = False
            else:
                log_info(f"{country}: 跳过子模块 - 可售商品设置")

            # 汇总FBA模块结果
            if total_sub_modules == 0:
                log_info(f"{country}: FBA模块没有启用的子模块")
                return True
            elif success_count >= total_sub_modules * 0.7:  # 70%成功率即算成功
                log_success(f"{country}: FBA模块执行成功 ({success_count}/{total_sub_modules})")
                return True
            else:
                log_warning(f"{country}: FBA模块部分成功 ({success_count}/{total_sub_modules})")
                return False

        except Exception as e:
            self.log_setting_exception(store_info, country, "亚马逊物流", f"FBA设置模块执行错误: {str(e)}")
            return False

    def navigate_to_shipping_settings(self, driver, country, store_info):
        """导航到配送设置页面的通用方法"""
        try:
            # 步骤1: 点击设置按钮
            log_step(f"{country}: 点击设置按钮")
            if not self.click_settings_button(driver, country):
                self.log_setting_exception(store_info, country, "配送设置", "点击设置按钮失败")
                return False

            # 设置按钮点击后检查弹窗
            self.handle_edit_page_popup(driver, country)

            # 步骤2: 点击配送设置（增加菜单检查和悬停）
            log_step(f"{country}: 点击配送设置")
            try:
                # 先检查菜单是否展开，如果没有则悬停设置按钮
                shipping_settings_link = None
                for attempt in range(3):
                    try:
                        shipping_settings_link = driver.find_element(By.XPATH, "//*[@id='navbar']/div[3]/div/div[2]/div/div[2]/div[1]/div/div[1]/a[7]")
                        if shipping_settings_link.is_displayed():
                            log_success(f"{country}: 配送设置链接已可见")
                            break
                    except:
                        pass

                    log_step(f"{country}: 配送设置链接不可见，尝试悬停设置按钮 (第{attempt+1}次)")
                    try:
                        settings_button = driver.find_element(By.XPATH, "//*[@id='navbar']/div[3]/div/div[2]/div/div[1]/div")
                        from selenium.webdriver.common.action_chains import ActionChains
                        actions = ActionChains(driver)
                        actions.move_to_element(settings_button).perform()
                        time.sleep(2)  # 等待菜单展开
                    except:
                        log_warning(f"{country}: 无法悬停设置按钮")

                # 确保元素可点击
                if not shipping_settings_link:
                    shipping_settings_link = WebDriverWait(driver, 15).until(
                        EC.element_to_be_clickable((By.XPATH, "//*[@id='navbar']/div[3]/div/div[2]/div/div[2]/div[1]/div/div[1]/a[7]"))
                    )

                if self.safe_click(driver, shipping_settings_link, f"{country}配送设置链接"):
                    log_success(f"{country}: 已点击配送设置")
                    time.sleep(2)  # 从5秒减少到2秒
                    # 配送设置页面加载后检查弹窗
                    self.handle_edit_page_popup(driver, country)
                else:
                    self.log_setting_exception(store_info, country, "配送设置", "点击配送设置链接失败")
                    return False
            except TimeoutException:
                self.log_setting_exception(store_info, country, "配送设置", "未找到配送设置链接")
                return False

            # 步骤3: 点击配送模板
            log_step(f"{country}: 点击配送模板")
            try:
                shipping_template_tab = WebDriverWait(driver, 15).until(
                    EC.element_to_be_clickable((By.XPATH, "//*[@id='shipping_templates_tab']/a"))
                )
                if self.safe_click(driver, shipping_template_tab, f"{country}配送模板标签"):
                    log_success(f"{country}: 已点击配送模板")
                    time.sleep(3)
                    # 配送模板页面加载后检查弹窗
                    self.handle_edit_page_popup(driver, country)
                else:
                    self.log_setting_exception(store_info, country, "配送设置", "点击配送模板标签失败")
                    return False
            except TimeoutException:
                self.log_setting_exception(store_info, country, "配送设置", "未找到配送模板标签")
                return False

            # 步骤4: 点击编辑模板
            log_step(f"{country}: 点击编辑模板")
            try:
                edit_template_button = WebDriverWait(driver, 15).until(
                    EC.element_to_be_clickable((By.XPATH, "//*[@id='template_actions_split_dropdown']/span[1]/span/button/span"))
                )
                if self.safe_click(driver, edit_template_button, f"{country}编辑模板按钮"):
                    log_success(f"{country}: 已点击编辑模板")
                    time.sleep(5)
                    # 编辑模板页面加载后检查弹窗
                    self.handle_edit_page_popup(driver, country)
                else:
                    self.log_setting_exception(store_info, country, "配送设置", "点击编辑模板按钮失败")
                    return False
            except TimeoutException:
                self.log_setting_exception(store_info, country, "配送设置", "未找到编辑模板按钮，无法进入编辑页面")
                return False

            # 步骤5: 处理编辑页面弹窗
            log_step(f"{country}: 检查并关闭编辑页面弹窗")
            self.handle_edit_page_popup(driver, country)

            # 步骤6: 处理配送设置自动化弹窗
            log_step(f"{country}: 处理配送设置自动化弹窗")
            self.handle_shipping_automation_popup(driver, country)

            return True

        except Exception as e:
            self.log_setting_exception(store_info, country, "配送设置", f"导航到配送设置失败: {str(e)}")
            return False

    def navigate_to_fba_settings(self, driver, country, store_info):
        """导航到FBA设置页面的通用方法"""
        try:
            # 步骤0: 先导航到商铺首页
            log_step(f"{country}: 导航到商铺首页")
            if not self.navigate_to_seller_central_home(driver):
                log_error(f"{country}: 导航到商铺首页失败")
                return False

            # 等待页面加载
            time.sleep(3)
            self.handle_edit_page_popup(driver, country)

            # 步骤1: 点击设置按钮
            log_step(f"{country}: 点击设置按钮")
            if not self.click_settings_button(driver, country):
                self.log_setting_exception(store_info, country, "亚马逊物流", "点击设置按钮失败")
                return False

            # 设置按钮点击后检查弹窗
            self.handle_edit_page_popup(driver, country)

            # 步骤2: 点击亚马逊物流链接
            log_step(f"{country}: 点击亚马逊物流链接")
            try:
                fba_link = WebDriverWait(driver, 15).until(
                    EC.element_to_be_clickable((By.XPATH, "//*[@id='navbar']/div[3]/div/div[2]/div/div[2]/div[1]/div/div[1]/a[11]"))
                )
                if self.safe_click(driver, fba_link, f"{country}亚马逊物流链接"):
                    log_success(f"{country}: 已点击亚马逊物流链接")
                    time.sleep(5)  # 等待页面加载
                    self.handle_edit_page_popup(driver, country)
                    return True
                else:
                    self.log_setting_exception(store_info, country, "亚马逊物流", "点击亚马逊物流链接失败")
                    return False
            except TimeoutException:
                self.log_setting_exception(store_info, country, "亚马逊物流", "未找到亚马逊物流链接")
                return False

        except Exception as e:
            self.log_setting_exception(store_info, country, "亚马逊物流", f"导航到FBA设置失败: {str(e)}")
            return False

    def check_fba_page_health(self, driver, country):
        """检查FBA设置页面健康状态"""
        try:
            log_step(f"{country}: 检查FBA页面健康状态")

            # 检查当前URL是否是FBA设置页面
            current_url = driver.current_url
            log_info(f"{country}: 当前页面URL: {current_url}")

            # 检查是否在登录页面
            if self.is_login_page(driver, country):
                log_warning(f"{country}: 检测到登录页面，需要重新登录")
                return "需要登录"

            # 检查是否包含FBA设置页面的关键标识
            if "fba/settings" in current_url or "fulfillment" in current_url.lower():
                log_info(f"{country}: URL显示在FBA设置相关页面")

                # 检查页面是否正常加载了FBA设置内容
                try:
                    # 尝试找到FBA设置页面的关键元素
                    WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, "//*[@id='root']/div/div"))
                    )
                    log_success(f"{country}: FBA设置页面主要内容已加载")
                    return "正常"
                except TimeoutException:
                    log_warning(f"{country}: FBA设置页面内容加载异常")
                    return "页面内容异常"
            else:
                log_warning(f"{country}: 当前页面不是FBA设置页面")
                return "页面位置错误"

        except Exception as e:
            log_error(f"{country}: 检查FBA页面健康状态时发生错误: {str(e)}")
            return "检查异常"

    def is_login_page(self, driver, country):
        """检查当前页面是否是登录页面"""
        try:
            current_url = driver.current_url
            page_title = driver.title

            # 检查URL和标题中的登录标识
            login_indicators = [
                "signin", "login", "ap/signin", "authentication",
                "登录", "ログイン", "sign-in"
            ]

            url_has_login = any(indicator in current_url.lower() for indicator in login_indicators)
            title_has_login = any(indicator in page_title.lower() for indicator in login_indicators)

            # 检查页面中是否有登录表单元素
            has_login_form = False
            try:
                login_elements = driver.find_elements(By.XPATH, "//input[@type='email' or @type='password']")
                if len(login_elements) > 0:
                    has_login_form = True
            except:
                pass

            is_login = url_has_login or title_has_login or has_login_form

            if is_login:
                log_info(f"{country}: 检测到登录页面 - URL标识: {url_has_login}, 标题标识: {title_has_login}, 表单标识: {has_login_form}")

            return is_login

        except Exception as e:
            log_error(f"{country}: 检查登录页面状态时发生错误: {str(e)}")
            return False

    def recover_fba_settings_page(self, driver, country, store_info, max_attempts=2):
        """恢复到FBA设置页面"""
        try:
            log_step(f"{country}: 尝试恢复到FBA设置页面")

            for attempt in range(max_attempts):
                log_info(f"{country}: 第 {attempt + 1} 次恢复尝试")

                # 方法1: 直接导航到FBA设置页面URL
                try:
                    log_step(f"{country}: 尝试直接导航到FBA设置页面")

                    # 获取当前域名
                    current_url = driver.current_url
                    base_domain = None

                    if "sellercentral" in current_url:
                        # 提取域名部分
                        import urllib.parse
                        parsed = urllib.parse.urlparse(current_url)
                        base_domain = f"{parsed.scheme}://{parsed.netloc}"

                    if base_domain:
                        fba_settings_url = f"{base_domain}/fba/settings/index.html"
                        log_info(f"{country}: 导航到FBA设置页面: {fba_settings_url}")
                        driver.get(fba_settings_url)
                        time.sleep(5)

                        # 检查页面状态
                        page_status = self.check_fba_page_health(driver, country)

                        if page_status == "正常":
                            log_success(f"{country}: 成功恢复到FBA设置页面")
                            return True
                        elif page_status == "需要登录":
                            log_warning(f"{country}: 需要重新登录，尝试自动登录")
                            if self.handle_automatic_login(driver, country):
                                # 登录成功后重新导航到FBA设置页面
                                driver.get(fba_settings_url)
                                time.sleep(5)
                                if self.check_fba_page_health(driver, country) == "正常":
                                    log_success(f"{country}: 登录后成功恢复到FBA设置页面")
                                    return True

                except Exception as e:
                    log_error(f"{country}: 直接导航恢复失败: {str(e)}")

                # 方法2: 通过传统导航方式
                try:
                    log_step(f"{country}: 尝试通过传统导航方式恢复")
                    if self.navigate_to_fba_settings(driver, country, store_info):
                        page_status = self.check_fba_page_health(driver, country)
                        if page_status == "正常":
                            log_success(f"{country}: 通过传统导航成功恢复到FBA设置页面")
                            return True
                except Exception as e:
                    log_error(f"{country}: 传统导航恢复失败: {str(e)}")

                # 等待后重试
                if attempt < max_attempts - 1:
                    log_info(f"{country}: 等待3秒后重试")
                    time.sleep(3)

            log_error(f"{country}: FBA设置页面恢复失败，已尝试 {max_attempts} 次")
            return False

        except Exception as e:
            log_error(f"{country}: 恢复FBA设置页面时发生错误: {str(e)}")
            return False

    def handle_automatic_login(self, driver, country):
        """处理自动登录（简化版本）"""
        try:
            log_step(f"{country}: 尝试处理自动登录")

            # 这里可以添加自动登录逻辑
            # 由于登录涉及敏感信息，暂时返回False，让用户手动处理
            log_warning(f"{country}: 检测到需要登录，请手动登录后继续")

            # 等待用户可能的手动登录操作
            time.sleep(10)

            # 检查是否已经登录成功
            if not self.is_login_page(driver, country):
                log_success(f"{country}: 检测到已登录")
                return True
            else:
                log_error(f"{country}: 仍在登录页面")
                return False

        except Exception as e:
            log_error(f"{country}: 处理自动登录时发生错误: {str(e)}")
            return False

    def check_edit_page_loaded(self, driver, country, edit_type="unfulfillable", timeout=10):
        """检查编辑页面是否真正加载成功"""
        try:
            log_step(f"{country}: 检查{edit_type}编辑页面是否真正加载")

            # 根据编辑类型检查不同的关键元素
            key_elements = {
                "unfulfillable": [
                    "//*[@id='sc-content-container']/section/app-root/recovery-rsu-automated-unfulfillable",
                    "//*[@id='sc-content-container']/section/app-root/recovery-rsu-automated-unfulfillable/div[1]/div[2]/recovery-rsu-value-recovery-options"
                ],
                "sellable": [
                    "//*[@id='sc-content-container']/section/app-root/recovery-automated-sellable-x-month",
                    "//*[@id='sc-content-container']/section/app-root/recovery-automated-sellable-x-month/div[1]"
                ],
                "inbound": [
                    "//*[@id='root']/div/div/kat-table/kat-table-body",
                    "//*[@id='root']/div/div/kat-table"
                ]
            }

            # 交互元素检查 - 确保关键操作元素可点击
            interactive_elements = {
                "unfulfillable": [
                    "//*[@id='sc-content-container']/section/app-root/recovery-rsu-automated-unfulfillable/div[1]/div[2]/recovery-rsu-value-recovery-options/kat-table/kat-table-body/kat-table-row[2]/kat-table-cell/div/div[2]/recovery-setting-liquidation/div/div[1]/recovery-checkbox/kat-checkbox"
                ],
                "sellable": [
                    "//*[@id='sc-content-container']/section/app-root/recovery-automated-sellable-x-month/div[1]/kat-table/kat-table-body/kat-table-row/kat-table-cell/recovery-setting-row/div/div[2]/kat-radiobutton-group/div/kat-radiobutton[2]/input[@type='radio']"
                ],
                "inbound": [
                    "//*[@id='root']/div/div/div/a[1]/kat-button"  # 更新按钮
                ]
            }

            if edit_type not in key_elements:
                log_error(f"{country}: 不支持的编辑类型: {edit_type}")
                return False

            # 阶段1: 检查DOM元素是否存在
            elements_found = 0
            for xpath in key_elements[edit_type]:
                try:
                    WebDriverWait(driver, timeout).until(
                        EC.presence_of_element_located((By.XPATH, xpath))
                    )
                    elements_found += 1
                    log_info(f"{country}: 找到{edit_type}编辑页面关键元素: {xpath}")
                except TimeoutException:
                    log_warning(f"{country}: 未找到{edit_type}编辑页面关键元素: {xpath}")

            if elements_found == 0:
                log_error(f"{country}: {edit_type}编辑页面DOM元素检查失败，未找到任何关键元素")
                return False

            log_info(f"{country}: {edit_type}编辑页面DOM检查通过，找到{elements_found}个关键元素")

            # 阶段2: 简化等待策略，去掉复杂的交互检测
            log_step(f"{country}: 等待{edit_type}编辑页面JavaScript初始化")
            time.sleep(8)  # 给JavaScript充足的加载时间

            log_success(f"{country}: {edit_type}编辑页面基础检查通过，DOM元素已找到")
            return True

        except Exception as e:
            log_error(f"{country}: 检查{edit_type}编辑页面加载状态时发生错误: {str(e)}")
            return False

    def recover_to_fba_page_from_edit(self, driver, country, store_info):
        """从编辑页面返回到FBA设置页面"""
        try:
            log_step(f"{country}: 尝试从编辑页面返回到FBA设置页面")

            # 方法1: 尝试点击取消按钮返回
            try:
                cancel_button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, "//kat-button[contains(text(), '取消') or contains(@aria-label, 'Cancel') or contains(@class, 'cancel')]"))
                )
                if self.safe_click(driver, cancel_button, f"{country}取消按钮"):
                    log_success(f"{country}: 点击取消按钮成功")
                    time.sleep(3)
                    # 检查是否回到FBA设置页面
                    if self.check_fba_page_health(driver, country) == "正常":
                        log_success(f"{country}: 通过取消按钮成功返回FBA设置页面")
                        return True
            except TimeoutException:
                log_info(f"{country}: 未找到取消按钮")
            except Exception as e:
                log_warning(f"{country}: 点击取消按钮失败: {str(e)}")

            # 方法2: 直接导航到FBA设置页面
            try:
                log_step(f"{country}: 尝试直接导航回FBA设置页面")
                current_url = driver.current_url
                if "sellercentral" in current_url:
                    import urllib.parse
                    parsed = urllib.parse.urlparse(current_url)
                    base_domain = f"{parsed.scheme}://{parsed.netloc}"
                    fba_settings_url = f"{base_domain}/fba/settings/index.html"

                    log_info(f"{country}: 导航到FBA设置页面: {fba_settings_url}")
                    driver.get(fba_settings_url)
                    time.sleep(5)

                    if self.check_fba_page_health(driver, country) == "正常":
                        log_success(f"{country}: 直接导航成功返回FBA设置页面")
                        return True
            except Exception as e:
                log_error(f"{country}: 直接导航返回FBA设置页面失败: {str(e)}")

            # 方法3: 通过传统导航方式
            try:
                log_step(f"{country}: 尝试通过传统导航方式返回FBA设置页面")
                if self.navigate_to_fba_settings(driver, country, store_info):
                    if self.check_fba_page_health(driver, country) == "正常":
                        log_success(f"{country}: 通过传统导航成功返回FBA设置页面")
                        return True
            except Exception as e:
                log_error(f"{country}: 传统导航返回FBA设置页面失败: {str(e)}")

            log_error(f"{country}: 所有返回FBA设置页面的方法都失败了")
            return False

        except Exception as e:
            log_error(f"{country}: 从编辑页面返回FBA设置页面时发生错误: {str(e)}")
            return False


    def click_settings_button(self, driver, country):
        """统一的点击设置按钮方法（增加鼠标悬停确保菜单显示）"""
        try:
            settings_button = WebDriverWait(driver, 15).until(
                EC.element_to_be_clickable((By.XPATH, "//*[@id='navbar']/div[3]/div/div[2]/div/div[1]/div"))
            )

            # 先鼠标悬停到设置按钮，确保菜单能够显示
            log_step(f"{country}: 鼠标悬停到设置按钮")
            from selenium.webdriver.common.action_chains import ActionChains
            actions = ActionChains(driver)
            actions.move_to_element(settings_button).perform()
            time.sleep(1)  # 等待悬停效果

            # 第一次点击（可能消除弹窗或引导）
            log_step(f"{country}: 第一次点击设置按钮（防止弹窗干扰）")
            if self.safe_click(driver, settings_button, f"{country}设置按钮(第1次)"):
                log_success(f"{country}: 第一次点击设置按钮成功")
                time.sleep(2)  # 等待可能的弹窗处理
            else:
                log_info(f"{country}: 第一次点击设置按钮失败，继续第二次点击")

            # 重新获取设置按钮元素，防止stale element
            try:
                settings_button = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//*[@id='navbar']/div[3]/div/div[2]/div/div[1]/div"))
                )
            except TimeoutException:
                log_error(f"{country}: 重新获取设置按钮失败")
                return False

            # 再次鼠标悬停，确保菜单能够显示
            log_step(f"{country}: 再次鼠标悬停到设置按钮")
            actions = ActionChains(driver)
            actions.move_to_element(settings_button).perform()
            time.sleep(1)  # 等待悬停效果

            # 第二次点击（真正的有效点击）
            log_step(f"{country}: 第二次点击设置按钮（实际操作）")
            if self.safe_click(driver, settings_button, f"{country}设置按钮(第2次)"):
                log_success(f"{country}: 已点击设置按钮")
                time.sleep(3)  # 增加等待时间

                # 验证菜单是否展开
                log_step(f"{country}: 验证设置菜单是否展开")
                try:
                    # 检查配送设置链接是否可见
                    shipping_link = driver.find_element(By.XPATH, "//*[@id='navbar']/div[3]/div/div[2]/div/div[2]/div[1]/div/div[1]/a[7]")
                    if shipping_link.is_displayed():
                        log_success(f"{country}: 设置菜单已成功展开")
                        return True
                    else:
                        log_warning(f"{country}: 设置菜单未完全展开，尝试再次悬停")
                        # 再次悬停
                        actions.move_to_element(settings_button).perform()
                        time.sleep(2)
                        return True
                except:
                    log_warning(f"{country}: 无法验证菜单状态，但点击成功")
                    return True
            else:
                log_error(f"{country}: 设置按钮第二次点击失败")
                return False

        except TimeoutException:
            log_error(f"{country}: 未找到设置按钮")
            return False

    def handle_edit_page_popup(self, driver, country):
        """处理进入编辑页面时出现的弹窗 - 支持多种弹窗类型（保守优化版）"""
        try:
            # 使用智能等待管理器进行弹窗检测
            with SmartWaitManager(driver) as wait_mgr:
                wait_mgr.set_context_wait("popup_detection")

            # 定义多种弹窗类型和对应的确定按钮xpath
            popup_configs = [
                {
                    "name": "可售库存自动设置弹窗",
                    "detection_xpaths": [
                        "//*[@id='sc-content-container']/section/ngb-modal-window",
                        "//h4[contains(text(), '可售库存自动设置')]",
                        "//kat-button[@data-testid='rsf-entry-modal-ok']"
                    ],
                    "button_xpaths": [
                        "//*[@id='sc-content-container']/section/ngb-modal-window/div/div/div[3]/kat-button//button",
                        "//kat-button[@data-testid='rsf-entry-modal-ok']//button",
                        "//kat-button[@data-testid='rsf-entry-modal-ok']",
                        "//*[@id='sc-content-container']/section/ngb-modal-window//kat-button"
                    ]
                },
                {
                    "name": "通用编辑页面弹窗",
                    "detection_xpaths": [
                        "//*[@id='vibes-close-button']"
                    ],
                    "button_xpaths": [
                        "//*[@id='vibes-close-button']"
                    ]
                },
                {
                    "name": "配置确认弹窗",
                    "detection_xpaths": [
                        "//*[@id='sc-content-container']/section/ngb-modal-window/div/div/div[3]/kat-button"
                    ],
                    "button_xpaths": [
                        "//*[@id='sc-content-container']/section/ngb-modal-window/div/div/div[3]/kat-button",
                        "//*[@id='sc-content-container']/section/ngb-modal-window/div/div/div[3]/kat-button//button"
                    ]
                }
            ]

            popup_found = False

            for config in popup_configs:
                # 检测弹窗是否存在
                for detection_xpath in config["detection_xpaths"]:
                    try:
                        popup_element = WebDriverWait(driver, 2).until(
                            EC.presence_of_element_located((By.XPATH, detection_xpath))
                        )
                        log_step(f"{country}: 发现{config['name']}，尝试点击确定按钮")
                        popup_found = True

                        # 尝试点击确定按钮
                        button_clicked = False
                        for button_xpath in config["button_xpaths"]:
                            try:
                                button = WebDriverWait(driver, 3).until(
                                    EC.presence_of_element_located((By.XPATH, button_xpath))
                                )
                                log_info(f"{country}: 找到确定按钮: {button_xpath}")

                                # 使用多重点击策略
                                if self.try_multiple_click_strategies(driver, button, country, f"{config['name']}确定按钮"):
                                    log_success(f"{country}: 已处理{config['name']}")
                                    button_clicked = True
                                    time.sleep(3)  # 等待弹窗关闭
                                    break

                            except Exception as btn_e:
                                log_warning(f"{country}: 按钮xpath {button_xpath} 失败: {str(btn_e)}")
                                continue

                        if button_clicked:
                            return True
                        else:
                            log_error(f"{country}: {config['name']}的所有按钮xpath都失败了")
                        break

                    except TimeoutException:
                        continue
                    except Exception as det_e:
                        log_warning(f"{country}: 检测xpath {detection_xpath} 异常: {str(det_e)}")
                        continue

                if popup_found:
                    break

            if not popup_found:
                log_info(f"{country}: 未发现任何弹窗")

        except Exception as e:
            log_error(f"{country}: 处理编辑页面弹窗时发生错误: {str(e)}")

        return popup_found

    def wait_for_page_loading_complete(self, driver, country, max_wait_seconds=10):
        """
        等待页面加载完成
        通过检查加载器元素的style属性来判断加载状态
        display: block; 表示正在加载中
        display: none; 表示加载完成
        """
        try:
            log_step(f"{country}: 等待页面加载完成...")

            loading_completed = False

            # 等待最多max_wait_seconds秒
            for attempt in range(max_wait_seconds):
                try:
                    # 查找加载器元素
                    loader_element = driver.find_element(By.XPATH, "//*[@id='sbr_page_over_screen']")

                    # 获取style属性
                    style_attr = loader_element.get_attribute("style")

                    if style_attr:
                        # 检查是否包含display: none，表示加载完成
                        if "display: none" in style_attr:
                            loading_completed = True
                            log_success(f"{country}: 检测到加载器隐藏，页面加载完成")
                            break
                        elif "display: block" in style_attr:
                            log_info(f"{country}: 检测到页面正在加载中，继续等待... (第{attempt+1}秒)")
                        else:
                            # 如果style中没有明确的display属性，检查元素是否可见
                            if not loader_element.is_displayed():
                                loading_completed = True
                                log_success(f"{country}: 检测到加载器不可见，页面加载完成")
                                break
                            else:
                                log_info(f"{country}: 加载器仍可见，继续等待... (第{attempt+1}秒)")
                    else:
                        # 如果没有style属性，检查元素是否可见
                        if not loader_element.is_displayed():
                            loading_completed = True
                            log_success(f"{country}: 检测到加载器不可见，页面加载完成")
                            break
                        else:
                            log_info(f"{country}: 加载器仍可见，继续等待... (第{attempt+1}秒)")

                except Exception as e:
                    # 如果找不到加载器元素，可能表示加载已完成
                    loading_completed = True
                    log_success(f"{country}: 未找到加载器元素，认为页面加载完成")
                    break

                time.sleep(1)

            if loading_completed:
                log_success(f"{country}: 页面加载完成，准备进行下一步")
                return True
            else:
                log_info(f"{country}: 等待加载超时({max_wait_seconds}秒)，继续下一步")
                return False

        except Exception as e:
            log_error(f"{country}: 检测页面加载状态时发生错误: {str(e)}")
            return False

    def handle_shipping_automation_popup(self, driver, country):
        """处理配送设置自动化弹窗"""
        try:
            log_step(f"{country}: 检查配送设置自动化相关元素")

            # 首先检查是否存在配送设置表单容器
            try:
                form_container = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, "//*[@id='shipping_template_preference_form']/div/div"))
                )
                log_success(f"{country}: 找到配送设置表单容器")

                # 检查是否有配送地址相关的元素
                try:
                    address_element = WebDriverWait(driver, 3).until(
                        EC.presence_of_element_located((By.XPATH, "//*[@id='shipping_template_preference_form']/div/div/div/div[2]/div/span/div[1]"))
                    )

                    # 获取元素内容并检查是否包含"配送地址"
                    element_text = address_element.text.strip()
                    log_info(f"{country}: 配送相关元素内容: {element_text}")

                    if "配送地址" in element_text:
                        log_step(f"{country}: 发现配送地址相关内容，需要取消SSA")

                        # 点击取消SSA的开关
                        try:
                            ssa_toggle = WebDriverWait(driver, 5).until(
                                EC.element_to_be_clickable((By.XPATH, "//*[@id='isCarrierBasedAutomationEnabled']/label/div"))
                            )

                            if self.safe_click(driver, ssa_toggle, f"{country}SSA开关"):
                                log_success(f"{country}: 已点击SSA开关")
                                time.sleep(2)

                                # 处理确认弹窗
                                try:
                                    confirm_button = WebDriverWait(driver, 5).until(
                                        EC.element_to_be_clickable((By.XPATH, "//*[@id='shipping-preference-cancel-automation-confirm-btn']/span"))
                                    )

                                    if self.safe_click(driver, confirm_button, f"{country}取消SSA确认按钮"):
                                        log_success(f"{country}: 已确认取消SSA")

                                        # 等待加载完成
                                        self.wait_for_page_loading_complete(driver, country, 10)

                                    else:
                                        log_error(f"{country}: 取消SSA确认按钮点击失败")

                                except TimeoutException:
                                    log_error(f"{country}: 未找到取消SSA确认按钮")

                            else:
                                log_error(f"{country}: SSA开关点击失败")

                        except TimeoutException:
                            log_error(f"{country}: 未找到SSA开关")

                    else:
                        log_info(f"{country}: 元素内容不包含配送地址，无需处理SSA")

                except TimeoutException:
                    log_info(f"{country}: 未找到配送地址相关元素，无需处理SSA")

            except TimeoutException:
                log_info(f"{country}: 未找到配送设置表单容器")

        except Exception as e:
            log_error(f"{country}: 处理配送自动化弹窗时发生错误: {str(e)}")

    def handle_shipping_services(self, driver, country):
        """处理配送服务勾选，取消除标准配送外的其他服务"""
        try:
            log_step(f"{country}: 检查配送服务勾选框")

            # 处理DOMESTIC配送服务
            self.handle_shipping_service_type(driver, country, "DOMESTIC")

            # 处理INTERNATIONAL配送服务
            self.handle_shipping_service_type(driver, country, "INTERNATIONAL")

        except Exception as e:
            log_error(f"{country}: 处理配送服务勾选时发生错误: {str(e)}")

    def handle_shipping_service_type(self, driver, country, service_type):
        """处理指定类型的配送服务（DOMESTIC或INTERNATIONAL）"""
        try:
            log_step(f"{country}: 检查{service_type}配送服务勾选框")

            # 查找所有以指定服务类型结尾的服务容器
            try:
                services = driver.find_elements(By.XPATH, f"//*[contains(@id, '.{service_type}')]")
                log_info(f"{country}: 找到 {len(services)} 个{service_type}配送服务")

                unchecked_count = 0

                for service_element in services:
                    try:
                        service_id = service_element.get_attribute("id")
                        if not service_id or not service_id.endswith(f".{service_type}"):
                            continue

                        # 跳过标准配送
                        if f"_STANDARD.{service_type}" in service_id:
                            log_info(f"{country}: 跳过标准配送 {service_id}")
                            continue

                        # 构建勾选框xpath（基于用户提供的规律）
                        checkbox_xpath = f"//*[@id='{service_id}']/div[1]/div/div/div/div[1]/div[1]/label/input"

                        try:
                            checkbox = WebDriverWait(driver, 3).until(
                                EC.presence_of_element_located((By.XPATH, checkbox_xpath))
                            )

                            # 先判断勾选框能否点击
                            if not checkbox.is_enabled():
                                log_info(f"{country}: 服务 {service_id} 的勾选框不可点击，跳过")
                                continue

                            # 检查是否可点击
                            try:
                                clickable_checkbox = WebDriverWait(driver, 2).until(
                                    EC.element_to_be_clickable((By.XPATH, checkbox_xpath))
                                )
                            except TimeoutException:
                                log_info(f"{country}: 服务 {service_id} 的勾选框不可点击，跳过")
                                continue

                            if checkbox.is_selected():
                                log_step(f"{country}: 取消勾选 {service_id}")

                                if self.safe_click(driver, clickable_checkbox, f"{country}{service_id}勾选框"):
                                    log_success(f"{country}: 已取消勾选 {service_id}")
                                    unchecked_count += 1
                                    time.sleep(1)

                                    # 处理可能的确认弹窗
                                    try:
                                        confirm_button = WebDriverWait(driver, 3).until(
                                            EC.element_to_be_clickable((By.XPATH, "//*[@id='submitButtonInPopup-announce']"))
                                        )
                                        if self.safe_click(driver, confirm_button, f"{country}取消勾选确认按钮"):
                                            log_success(f"{country}: 已确认取消勾选 {service_id}")
                                            time.sleep(2)
                                    except TimeoutException:
                                        log_info(f"{country}: 无需确认取消勾选")
                                else:
                                    log_error(f"{country}: 取消勾选 {service_id} 失败")
                            else:
                                log_info(f"{country}: {service_id} 已经是未勾选状态")

                        except TimeoutException:
                            log_info(f"{country}: 服务 {service_id} 的勾选框未找到")

                    except Exception as service_e:
                        log_error(f"{country}: 处理服务 {service_id} 时出错: {str(service_e)}")
                        continue

                log_success(f"{country}: 完成{service_type}配送服务处理，共取消勾选 {unchecked_count} 个服务")

            except Exception as e:
                log_error(f"{country}: 查找{service_type}配送服务时出错: {str(e)}")

        except Exception as e:
            log_error(f"{country}: 处理{service_type}配送服务勾选时发生错误: {str(e)}")

    def get_country_code(self, country):
        """
        获取国家对应的代码
        """
        country_mapping = {
            "美国": "US",
            "US": "US",
            "日本": "JP",
            "JP": "JP",
            "JAPAN": "JP",
            "英国": "GB",
            "UK": "GB",
            "GB": "GB",
            "加拿大": "CA",
            "CA": "CA",
            "CANADA": "CA",
            "澳大利亚": "AU",
            "AU": "AU",
            "AUSTRALIA": "AU",
            "德国": "DE",
            "DE": "DE",
            "GERMANY": "DE",
            "法国": "FR",
            "FR": "FR",
            "FRANCE": "FR",
            "意大利": "IT",
            "IT": "IT",
            "ITALY": "IT",
            "西班牙": "ES",
            "ES": "ES",
            "SPAIN": "ES",
            "墨西哥": "MX",
            "MX": "MX",
            "MEXICO": "MX"
        }
        return country_mapping.get(country.upper(), country.upper())

    def get_special_regions_for_country(self, country_code):
        """
        获取每个国家的特殊地区关键词
        """
        special_regions_mapping = {
            "US": ["夏威夷", "阿拉斯加", "波多黎各"],
            "JP": ["冲绳县", "东离岛", "偏远离岛", "北海道离岛", "冲绳离岛", "西离岛"],
            "DE": ["德国大陆", "离岸地区"],  # 德国特殊配送地区
            "FR": ["法国本土和摩纳哥", "科西嘉岛"],  # 法国特殊配送地区 (排除DOM TOM)
            "CA": ["加拿大"],  # 加拿大特殊配送地区
            "MX": ["墨西哥"],  # 墨西哥特殊配送地区
            "ES": ["西班牙大陆", "巴利阿里群岛", "加那利群岛、休达和梅利利亚"],  # 西班牙特殊配送地区
            "IT": ["意大利大陆, 意大利本土偏远地区", "意大利岛屿（撒丁岛、西西里岛、厄尔巴岛和其他岛屿）"],  # 意大利特殊配送地区
            "GB": ["苏格兰(邓弗里斯, 爱丁堡, 格拉斯哥, 基尔马诺克, 马瑟韦尔), 英格兰, 威尔士", "苏格兰(阿伯丁, 邓迪, 福尔柯克, 外赫布里底群岛, 因弗尼斯, 柯克沃尔, 柯科迪, 佩斯里, 珀斯, 加拉希尔斯, 设得兰群岛), 北爱尔兰, 海峡群岛, 马恩岛", "BFPO"],  # 英国特殊配送地区
            # 可以继续添加其他国家的特殊地区
        }
        return special_regions_mapping.get(country_code, [])

    def handle_special_regions(self, driver, country, store_info=None):
        """
        通用的特殊地区运费设置方法
        支持多个国家的特殊地区处理
        """
        try:
            # 获取国家代码
            country_code = self.get_country_code(country)
            special_regions = self.get_special_regions_for_country(country_code)

            # 如果没有配置特殊地区，跳过处理
            if not special_regions:
                log_info(f"{country}: 该国家暂未配置特殊地区处理规则，跳过")
                return

            log_step(f"{country}: 开始处理特殊地区运费设置")

            # 尝试多种可能的xpath模式
            standard_shipping_xpaths = [
                f"//*[@id='{country_code}_STANDARD.DOMESTIC']/div[4]/div[2]/div/table/tbody",  # 美国模式
                f"//*[@id='{country_code}_STANDARD.DOMESTIC_shipping_duration_table']/tbody",  # 日本模式
                f"//*[@id='{country_code}_STANDARD.DOMESTIC']//table/tbody",  # 通用模式1
                f"//*[@id='{country_code}_STANDARD.DOMESTIC']//tbody",  # 通用模式2
                "//*[@id='EU_STANDARD.DOMESTIC']/div[4]/div[2]/div/table/tbody",  # 欧盟标准模式(德国/法国等)
                "//*[@id='EU_STANDARD.DOMESTIC']//table/tbody",  # 欧盟通用模式1
                "//*[@id='EU_STANDARD.DOMESTIC']//tbody"  # 欧盟通用模式2
            ]

            standard_shipping_xpath = None
            table_body = None

            # 尝试找到合适的表格xpath
            for xpath in standard_shipping_xpaths:
                try:
                    table_body = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, xpath))
                    )
                    standard_shipping_xpath = xpath
                    log_success(f"{country}: 找到标准配送运输时间表格，使用xpath: {xpath}")
                    break
                except TimeoutException:
                    continue

            if not table_body or not standard_shipping_xpath:
                log_error(f"{country}: 未找到标准配送运输时间表格")
                return
            #获取表头，找到各个列的序号标记，组合成字典，列名：序号
            headers = table_body.find_elements(By.XPATH, "./tr[1]/th")
            header_dict = {}
            for i, header in enumerate(headers):
                # 序号从1开始
                header_dict[header.text] = i+1
            log_info(f"{country}: 找到表头，列名：序号")
        

            # 处理特殊地区运费和运输时间
            log_step(f"{country}: 处理特殊地区运费和运输时间")
            self.handle_special_region_fees(driver, country, store_info, standard_shipping_xpath, special_regions, header_dict)

        except Exception as e:
            log_error(f"{country}: 处理特殊地区运费时发生错误: {str(e)}")
    # 根据列名字典获取列的序号
    def get_column_index(self, header_dict, column_name):
        for key, value in header_dict.items():
            if column_name in key:
                return value
        return None

    def set_shipping_fees_by_row(self, driver, table_xpath, header_dict, row_number, 
                                price_per_order, unit_price, log_prefix):
        """
        设置指定行的两个运费输入框
        
        Args:
            driver: WebDriver实例
            table_xpath: 表格xpath
            header_dict: 表头字典，用于获取运费列序号
            row_number: 行号（从1开始）
            price_per_order: 每个订单运费值
            unit_price: 每单位运费值
            log_prefix: 日志前缀
        
        Returns:
            bool: 设置是否成功
        """
        try:
            log_step(f"{log_prefix}: 开始设置第{row_number}行运费 - 每个订单:{price_per_order}, 每单位:{unit_price}")
            
            # 获取运费列序号
            shipping_fee_column_index = self.get_column_index(header_dict, "运费")
            if shipping_fee_column_index is None:
                log_error(f"{log_prefix}: 未找到运费列")
                return False
            
            log_info(f"{log_prefix}: 运费列序号为 {shipping_fee_column_index}")
            
            # 设置每个订单运费
            if not self.set_shipping_fee_input_by_name(driver, table_xpath, row_number, 
                                                     shipping_fee_column_index, "pricePerOrder", 
                                                     price_per_order, f"{log_prefix}: 每个订单"):
                return False
            
            # 设置每单位运费
            if not self.set_shipping_fee_input_by_name(driver, table_xpath, row_number, 
                                                     shipping_fee_column_index, "unitPrice", 
                                                     unit_price, f"{log_prefix}: 每单位"):
                return False
            
            log_success(f"{log_prefix}: 第{row_number}行运费设置完成")
            return True
            
        except Exception as e:
            error_msg = f"设置第{row_number}行运费时发生错误: {str(e)}"
            log_error(f"{log_prefix}: {error_msg}")
            return False

    def set_shipping_fee_input_by_name(self, driver, table_xpath, row_number, 
                                      column_index, input_name, value, log_prefix):
        """
        通过name属性设置运费输入框
        
        Args:
            driver: WebDriver实例
            table_xpath: 表格xpath
            row_number: 行号（从1开始）
            column_index: 运费列序号
            input_name: 输入框name属性值（pricePerOrder 或 unitPrice）
            value: 要设置的值
            log_prefix: 日志前缀
        
        Returns:
            bool: 设置是否成功
        """
        try:
            # 构建运费输入框的xpath
            fee_input_xpath = f"{table_xpath}/tr[{row_number}]/td[{column_index}]//input[@name='{input_name}']"
            
            for attempt in range(3):
                try:
                    # 查找运费输入框
                    fee_input = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, fee_input_xpath))
                    )
                    
                    # 滚动到元素
                    driver.execute_script("arguments[0].scrollIntoView();", fee_input)
                    time.sleep(0.5)
                    
                    # 点击输入框（聚焦）
                    fee_input.click()
                    time.sleep(0.5)
                    
                    # 按删除键清空内容（模拟手动操作）
                    from selenium.webdriver.common.keys import Keys
                    fee_input.send_keys(Keys.CONTROL + "a")  # 全选
                    time.sleep(0.2)
                    fee_input.send_keys(Keys.DELETE)  # 删除
                    time.sleep(0.2)
                    
                    # 输入新值
                    fee_input.send_keys(str(value))
                    time.sleep(0.5)
                    
                    # 验证输入是否成功
                    input_value = fee_input.get_attribute("value")
                    if input_value == str(value):
                        log_success(f"{log_prefix}已设置为{value}")
                        return True
                    else:
                        log_info(f"{log_prefix}设置后值为{input_value}，期望值为{value}，尝试重新设置")
                        continue
                        
                except Exception as e:
                    if "stale element reference" in str(e).lower() and attempt < 2:
                        log_info(f"{log_prefix}遇到stale element，第{attempt+1}次重试")
                        time.sleep(1)
                        continue
                    else:
                        log_error(f"{log_prefix}设置失败 (第{attempt+1}次尝试): {str(e)}")
                        if attempt == 2:  # 最后一次尝试
                            return False
                        continue
            
            return False
            
        except Exception as e:
            log_error(f"{log_prefix}设置过程中发生错误: {str(e)}")
            return False

    def _handle_shipping_time_by_text(self, driver, country, standard_shipping_xpath, row_count_or_index, header_dict, match_texts=None, fallback_to_last=True):
        """
        通用的运输时间设置方法，通过文本匹配选择合适的选项

        Args:
            driver: WebDriver实例
            country: 国家名称
            standard_shipping_xpath: 标准运输表格的XPath
            row_count_or_index: 表格行数（批量处理）或单行索引（单行处理）
            header_dict: 表头字典，列名：序号
            match_texts: 匹配文本列表，按优先级排序。支持以下格式:
                        - 字符串列表: ["14-18工作日", "14", "18"]
                        - 字符串列表的列表: [["14", "工作日"], ["18"]] (选项需包含子列表中所有字符串)
                        - None或空列表: 使用fallback_to_last策略
            fallback_to_last: 当没有匹配项时是否选择最后一个选项，默认True
                             当match_texts为空或None时，此参数自动设为True

        Returns:
            bool: 处理是否成功
        """
        try:
            # 参数处理：如果match_texts为空或None，启用fallback_to_last策略
            if not match_texts:
                match_texts = []
                fallback_to_last = True
                log_info(f"{country}: match_texts为空，启用fallback_to_last策略")
            
            log_step(f"{country}: 开始处理运输时间设置，匹配文本: {match_texts}, fallback_to_last: {fallback_to_last}")

            # 判断是单行处理还是批量处理
            # 如果row_count_or_index <= 20，认为是单行索引；否则认为是总行数
            if row_count_or_index <= 20:
                # 单行处理模式
                row_indices = [row_count_or_index]
                log_info(f"{country}: 单行处理模式，处理第{row_count_or_index}行")
            else:
                # 批量处理模式
                row_indices = list(range(2, row_count_or_index + 1))  # 从第2行开始（跳过标题行）
                log_info(f"{country}: 批量处理模式，处理第2行到第{row_count_or_index}行")

            for i in row_indices:
                try:
                    # 每次重新获取表格避免stale element
                    current_table = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, standard_shipping_xpath))
                    )

                    # 根据header_dict获取运输时间,找出key包含"运输时间"那一列的序号
                    shipping_time_column_index = None
                    for key, value in header_dict.items():
                        if "运输时间" in key:
                            shipping_time_column_index = value
                            break
                    if shipping_time_column_index is None:
                        log_error(f"{country}: 未找到运输时间列")
                        return False
                    # 根据序号获取运输时间
                    shipping_time = current_table.find_element(By.XPATH, f"./tr[{i}]/td[{shipping_time_column_index}]")
                    log_info(f"{country}: 第{i}行运输时间: {shipping_time}")
               
                    # 查找运输时间这一列的下拉框
                    dropdown = shipping_time.find_element(By.XPATH, ".//span/span/span")
                    # 如果为空，则表示没有找到下拉框
                    if not dropdown:
                        log_info(f"{country}: 第{i}行运输时间下拉框未找到，可能不存在")
                        continue

                    dropdown_clicked = False

                    try:
                        if self.safe_click(driver, dropdown, f"{country}第{i}行运输时间下拉框"):
                            time.sleep(1)
                            dropdown_clicked = True
                            log_success(f"{country}: 第{i}行运输时间下拉框已点击")
                            
                            # 查找并选择匹配的选项
                            try:
                                # 获取所有选项
                                all_options = driver.find_elements(By.XPATH, "//ul[@role='listbox']//li[@role='option']")
                                log_info(f"{country}: 第{i}行找到 {len(all_options)} 个运输时间选项")
                                
                                # 记录所有选项的文本，用于调试
                                for idx, option in enumerate(all_options):
                                    log_info(f"{country}: 第{i}行选项{idx+1}: {option.text}")
                                
                                # 按优先级匹配文本
                                target_option = None
                                
                                for match_item in match_texts:
                                    for option in all_options:
                                        option_text = option.text.strip()
                                        
                                        # 检查match_item是否为字符串列表（需要包含所有字符串）
                                        if isinstance(match_item, list):
                                            # 字符串列表匹配：选项需包含列表中所有字符串
                                            if all(text in option_text for text in match_item):
                                                target_option = option
                                                log_info(f"{country}: 第{i}行找到列表匹配选项 {match_item}: {option_text}")
                                                break
                                        else:
                                            # 单个字符串匹配：优先进行完全匹配，如果完全匹配失败则进行包含匹配
                                            if match_item == option_text:
                                                # 完全匹配优先级最高
                                                target_option = option
                                                log_info(f"{country}: 第{i}行找到完全匹配选项 '{match_item}': {option_text}")
                                                break
                                            elif match_item in option_text:
                                                # 包含匹配作为备选
                                                target_option = option
                                                log_info(f"{country}: 第{i}行找到包含匹配选项 '{match_item}': {option_text}")
                                                break
                                    
                                    if target_option:
                                        break
                                
                                # 备选策略：选择最后一个选项
                                if not target_option and fallback_to_last and all_options:
                                    target_option = all_options[-1]
                                    log_info(f"{country}: 第{i}行使用最后一个选项: {target_option.text}")
                                
                                if target_option:
                                    if self.safe_click(driver, target_option, f"{country}第{i}行运输时间选项"):
                                        log_success(f"{country}: 第{i}行运输时间已设置为: {target_option.text}")
                                        time.sleep(1)
                                    else:
                                        log_warning(f"{country}: 第{i}行运输时间选项点击失败")
                                else:
                                    log_warning(f"{country}: 第{i}行未找到合适的运输时间选项")
                                        
                            except Exception as option_e:
                                log_error(f"{country}: 第{i}行运输时间选项选择失败: {str(option_e)}")

                    except Exception as e:
                        log_info(f"{country}: 第{i}行运输时间下拉框未找到，可能不存在")
                        continue

                    if not dropdown_clicked:
                        log_info(f"{country}: 第{i}行运输时间下拉框未找到，可能不存在")

                except Exception as dropdown_e:
                    log_error(f"{country}: 处理第{i}行运输时间下拉框时出错: {str(dropdown_e)}")
                    continue

        except Exception as e:
            log_error(f"{country}: 处理运输时间下拉框时发生错误: {str(e)}")
            return False
            
        return True


    def handle_special_region_fees(self, driver, country, store_info, standard_shipping_xpath, special_regions, header_dict):
        """
        处理特殊地区的运费设置和运输时间
        """
        try:

            # 如果是美国
            if country == "美国":
                return self.handle_us_special_regions(driver, country, standard_shipping_xpath, special_regions, header_dict)

            # 如果是日本
            if country == "日本":
                return self.handle_japan_special_regions(driver, country, standard_shipping_xpath, special_regions, header_dict)

            # 如果是德国
            if country == "德国":
                return self.handle_germany_special_regions(driver, country, store_info, standard_shipping_xpath, special_regions, header_dict)
            
            # 如果是法国
            if country == "法国":
                return self.handle_france_special_regions(driver, country, standard_shipping_xpath, special_regions, header_dict)
            
            # 如果是加拿大
            if country == "加拿大":
                return self.handle_canada_special_regions(driver, country, store_info, standard_shipping_xpath, special_regions, header_dict)
            
            # 如果是墨西哥
            if country == "墨西哥":
                return self.handle_mexico_special_regions(driver, country, store_info, standard_shipping_xpath, special_regions, header_dict)

            # 如果是西班牙
            if country == "西班牙":
                return self.handle_spain_special_regions(driver, country, store_info, standard_shipping_xpath, special_regions, header_dict)

            # 如果是意大利
            if country == "意大利":
                return self.handle_italy_special_regions(driver, country, store_info, standard_shipping_xpath, special_regions, header_dict)

            # 如果是英国
            if country == "英国":
                return self.handle_uk_special_regions(driver, country, store_info, standard_shipping_xpath, special_regions, header_dict)

            return None





        except Exception as e:
            log_error(f"{country}: 处理特殊地区运费时发生错误: {str(e)}")
            return None

    def handle_us_special_regions(self, driver, country,standard_shipping_xpath,special_regions,header_dict):
        """处理特殊地区运费设置"""
        try:
            log_step(f"{country}: 开始处理特殊地区运费设置")
            # standard_shipping_xpath记录错误日志返回
            if not standard_shipping_xpath:
                error_msg = "未找到标准配送运输时间表格"
                log_error(f"{country}: {error_msg}")
                self.log_setting_exception(getattr(self, 'current_store_info', None), country, "配送设置", f"美国特殊地区处理失败: {error_msg}")
                return

            try:
                table_body = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, standard_shipping_xpath))
                )

                # 获取所有行
                rows = table_body.find_elements(By.XPATH, "./tr")
                log_info(f"{country}: 找到 {len(rows)} 行运输时间数据")

                # 默认的处理运输时间逻辑
                self._handle_shipping_time_by_text(driver, country,standard_shipping_xpath,len(rows),header_dict)

                # 查找包含"夏威夷, 阿拉斯加州, 波多黎各"的行
                log_step(f"{country}: 查找夏威夷、阿拉斯加、波多黎各地区行")
                target_row_index = None

                # 查找区域列 - 从表头字典中获取区域列的序号
                region_column_index = None
                for key, value in header_dict.items():
                    if "区域" in key or "地区" in key or "Region" in key:
                        region_column_index = value 
                        break               
                if region_column_index is None:
                    log_error(f"{country}: 未找到区域列，配送设置失败")
                    return
                for i in range(2, len(rows) + 1):  # 从第2行开始（跳过标题行）
                    try:

                        region_xpath = f"{standard_shipping_xpath}/tr[{i}]/td[{region_column_index}]"
                        region_cell = WebDriverWait(driver, 3).until(
                            EC.presence_of_element_located((By.XPATH, region_xpath))
                        )
                        region_text = region_cell.text.strip()

                        if "夏威夷" in region_text and "阿拉斯加" in region_text and "波多黎各" in region_text:
                            target_row_index = i
                            log_success(f"{country}: 找到目标地区行，第{i}行: {region_text}")
                            break
                    except:
                        continue

                if target_row_index:
                    # 修改该行的运费
                    log_step(f"{country}: 修改第{target_row_index}行的运费")

                    # 使用统一的运费设置方法
                    if not self.set_shipping_fees_by_row(driver, standard_shipping_xpath, header_dict, 
                                                        target_row_index, "20", "0", f"{country}: 美国特殊地区"):
                        error_msg = f"设置美国特殊地区运费失败"
                        self.log_setting_exception(getattr(self, 'current_store_info', None), country, "配送设置", f"美国特殊地区处理失败: {error_msg}")
                        return
                else:
                    error_msg = "未找到包含夏威夷、阿拉斯加、波多黎各的地区行"
                    log_error(f"{country}: {error_msg}")
                    self.log_setting_exception(getattr(self, 'current_store_info', None), country, "配送设置", f"美国特殊地区处理失败: {error_msg}")

            except TimeoutException:
                error_msg = "未找到标准配送运输时间表格"
                log_error(f"{country}: {error_msg}")
                self.log_setting_exception(getattr(self, 'current_store_info', None), country, "配送设置", f"美国特殊地区处理失败: {error_msg}")

        except Exception as e:
            error_msg = f"处理美国特殊地区运费时发生错误: {str(e)}"
            log_error(f"{country}: {error_msg}")
            self.log_setting_exception(getattr(self, 'current_store_info', None), country, "配送设置", f"美国特殊地区处理失败: {error_msg}")

    def handle_japan_special_regions(self, driver, country,standard_shipping_xpath,special_regions,header_dict):
        """
        处理日本特殊地区运费设置
        包括设置运输时间下拉框和特殊地区运费
        日本还需要冲绳离岛移到冲绳县那一行
        """
        try:
            log_step(f"{country}: 开始处理日本特殊地区设置")

            try:
                # 等待标准配送运输时间表格出现
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, standard_shipping_xpath))
                )
                log_success(f"{country}: 找到标准配送运输时间表格")

                # 获取表格行数
                rows = driver.find_elements(By.XPATH, f"{standard_shipping_xpath}/tr")
                row_count = len(rows)
                log_info(f"{country}: 标准配送表格共有 {row_count} 行")
                # 默认的处理运输时间逻辑
                self._handle_shipping_time_by_text(driver, country, standard_shipping_xpath, len(rows),header_dict)
                # 从第二行开始遍历所有行，观察每一行的第二个td，是否包含冲绳县，如果包含则判断是否还包含冲绳离岛如果不包含
                # 则从第二行开始遍历所有行找到包含冲绳离岛的行，然后将其拖动到包含冲绳县的行
                log_step(f"{country}: 查找包含冲绳县的行")
                region_column_index = self.get_column_index(header_dict, '区域')
                target_row_index = None
                for i in range(2, len(rows) + 1):  # 从第2行开始（跳过标题行）
                    try:
                        # 查找区域列
                        region_xpath = f"{standard_shipping_xpath}/tr[{i}]/td[{region_column_index}]"
                        region_cell = WebDriverWait(driver, 3).until(
                            EC.presence_of_element_located((By.XPATH, region_xpath))
                        )
                        region_text = region_cell.text.strip()

                        if "冲绳县" in region_text:
                            target_row_index = i
                            log_success(f"{country}: 找到目标地区行，第{i}行: {region_text}")
                            # 判断是否还包含冲绳离岛
                            if "冲绳离岛" in region_text:
                                log_info(f"{country}: 冲绳县那一行还包含了冲绳离岛，无需改动")
                                break
                            else:
                                log_info(f"{country}: 冲绳县那一行不包含冲绳离岛，需要查找包含冲绳离岛的那一行")
                                # 从第二行开始遍历所有行找到包含冲绳离岛的行
                                for j in range(2, len(rows) + 1):
                                    try:
                                        # 查找区域列
                                        region2_xpath = f"{standard_shipping_xpath}/tr[{j}]/td[{region_column_index}]"
                                        region2_cell = WebDriverWait(driver, 3).until(
                                            EC.presence_of_element_located((By.XPATH, region2_xpath))
                                        )
                                        region2_text = region2_cell.text.strip()

                                        if "冲绳离岛" in region2_text:
                                            log_success(f"{country}: 找到包含冲绳离岛的行，第{j}行: {region2_text}")
                                            # 将其拖动到包含冲绳县的行
                                            if not self.drag_and_drop_row(driver, standard_shipping_xpath, region2_xpath, region_xpath):
                                                error_msg = "将包含冲绳离岛拖动到包含冲绳县的行失败"
                                                log_error(f"{country}: {error_msg}")
                                                self.log_setting_exception(getattr(self, 'current_store_info', None), country, "配送设置", f"日本特殊地区处理失败: {error_msg}")
                                            else:
                                                log_success(f"{country}: 已将包含冲绳离岛的行拖动到包含冲绳县的行")
                                            break
                                    except Exception as e:
                                        log_error(f"{country}: 查找包含冲绳离岛的行时出错。错误原因: {str(e)}")
                                        continue
                            break
                    except:
                        continue

                #设置几个特殊区域的运费设置
                # 从第二行开始遍历所有行
                for i in range(2, len(rows) + 1):  # 从第2行开始（跳过标题行）
                    try:
                        # 查找区域列
                        region_xpath = f"{standard_shipping_xpath}/tr[{i}]/td[{region_column_index}]"
                        region_cell = WebDriverWait(driver, 3).until(
                            EC.presence_of_element_located((By.XPATH, region_xpath))
                        )
                        region_text = region_cell.text.strip()
                        if "北海道" in region_text:
                            log_success(f"{country}: 找到北海道行，第{i}行: {region_text}")
                            # 修改该行的运费
                            log_step(f"{country}: 修改第{i}行的运费")

                            # 使用统一的运费设置方法
                            if not self.set_shipping_fees_by_row(driver, standard_shipping_xpath, header_dict, 
                                                                i, "0", "1699", f"{country}: 北海道"):
                                error_msg = f"北海道运费设置失败"
                                self.log_setting_exception(getattr(self, 'current_store_info', None), country, "配送设置", f"日本特殊地区处理失败: {error_msg}")
                                continue

                            #将第二个输入框后面的单位改为商品
                            if not self.set_fee_unit(driver, standard_shipping_xpath, i,  f"{country}: 修改单位"):
                                error_msg = f"北海道运费单位设置失败"
                                self.log_setting_exception(getattr(self, 'current_store_info', None), country, "配送设置", f"日本特殊地区处理失败: {error_msg}")
                                continue
                            continue
                        if "冲绳县" in region_text:
                            log_success(f"{country}: 找到冲绳县行，第{i}行: {region_text}")
                            # 修改该行的运费
                            log_step(f"{country}: 修改第{i}行的运费")

                            # 使用统一的运费设置方法
                            if not self.set_shipping_fees_by_row(driver, standard_shipping_xpath, header_dict, 
                                                                i, "0", "3199", f"{country}: 冲绳县"):
                                error_msg = f"冲绳县运费设置失败"
                                self.log_setting_exception(getattr(self, 'current_store_info', None), country, "配送设置", f"日本特殊地区处理失败: {error_msg}")
                                continue
                            #将第二个输入框后面的单位改为商品
                            if not self.set_fee_unit(driver, standard_shipping_xpath, i,  f"{country}: 修改单位"):
                                error_msg = f"冲绳县运费单位设置失败"
                                self.log_setting_exception(getattr(self, 'current_store_info', None), country, "配送设置", f"日本特殊地区处理失败: {error_msg}")
                                continue
                            continue
                        if "东离岛" in region_text and "偏远离岛" in region_text and "北海道离岛" in region_text and "西离岛" in region_text:
                            log_success(f"{country}: 找到其他离岛行，第{i}行: {region_text}")
                            # 修改该行的运费
                            log_step(f"{country}: 修改第{i}行的运费")

                            # 使用统一的运费设置方法
                            if not self.set_shipping_fees_by_row(driver, standard_shipping_xpath, header_dict, 
                                                                i, "0", "1699", f"{country}: 其他离岛"):
                                error_msg = f"其他离岛运费设置失败"
                                self.log_setting_exception(getattr(self, 'current_store_info', None), country, "配送设置", f"日本特殊地区处理失败: {error_msg}")
                                continue
                            #将第二个输入框后面的单位改为商品
                            if not self.set_fee_unit(driver, standard_shipping_xpath, i,  f"{country}: 修改单位"):
                                error_msg = f"其他离岛运费单位设置失败"
                                self.log_setting_exception(getattr(self, 'current_store_info', None), country, "配送设置", f"日本特殊地区处理失败: {error_msg}")
                                continue
                            continue


                    except Exception as e:
                        error_msg = f"查找特殊地区行时出错。错误原因: {str(e)}"
                        log_error(f"{country}: {error_msg}")
                        self.log_setting_exception(getattr(self, 'current_store_info', None), country, "配送设置", f"日本特殊地区处理失败: {error_msg}")
                        continue

            except TimeoutException:
                error_msg = "未找到标准配送运输时间表格"
                log_error(f"{country}: {error_msg}")
                self.log_setting_exception(getattr(self, 'current_store_info', None), country, "配送设置", f"日本特殊地区处理失败: {error_msg}")

        except Exception as e:
            error_msg = f"处理日本特殊地区运费时发生错误: {str(e)}"
            log_error(f"{country}: {error_msg}")
            self.log_setting_exception(getattr(self, 'current_store_info', None), country, "配送设置", f"日本特殊地区处理失败: {error_msg}")

    def handle_germany_special_regions(self, driver, country, store_info, standard_shipping_xpath, special_regions, header_dict):
        """处理德国特殊配送地区设置 - 使用升级版通用EU方法，保持现有逻辑"""
        return self.handle_eu_special_regions(
            driver, country, store_info, standard_shipping_xpath, special_regions, header_dict,
            shipping_time="14 - 18 工作日", price_per_order="0", unit_price="0", unit_type="商品",
            skip_unit_setting=True, max_retries=3
        )

    def handle_france_special_regions(self, driver, country, store_info, standard_shipping_xpath, special_regions, header_dict):
        """处理法国特殊配送地区设置 - 使用升级版通用EU方法"""
        return self.handle_eu_special_regions(
            driver, country, store_info, standard_shipping_xpath, special_regions, header_dict,
            shipping_time="14 - 18 工作日", price_per_order="0", unit_price="0", unit_type="商品",
            skip_unit_setting=False, max_retries=3
        )

    def handle_canada_special_regions(self, driver, country, store_info, standard_shipping_xpath, special_regions, header_dict):
        """
        处理加拿大特殊配送地区设置
        1. 查找加拿大地区行
        2. 设置运费：每订单(pricePerOrder)为0，每单位(unitPrice)为20
        3. 设置单位下拉框选择"商品"选项
        """
        try:
            log_step(f"{country}: 开始处理加拿大特殊配送地区设置")

            # 检查表格是否存在
            try:
                table_body = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, standard_shipping_xpath))
                )
                log_success(f"{country}: 找到加拿大配送设置表格")
            except TimeoutException:
                error_msg = "未找到加拿大配送设置表格"
                log_error(f"{country}: {error_msg}")
                self.log_setting_exception(getattr(self, 'current_store_info', None), country, "配送设置", f"加拿大特殊地区处理失败: {error_msg}")
                return

            # 获取所有行
            rows = table_body.find_elements(By.XPATH, "./tr")
            log_info(f"{country}: 找到 {len(rows)} 行运输时间数据")

            # 查找加拿大地区行
            log_step(f"{country}: 查找加拿大地区行")
            target_row_index = None
            region_column_index = self.get_column_index(header_dict, '区域')

            if region_column_index is None:
                log_error(f"{country}: 未找到区域列，配送设置失败")
                return False

            for i in range(2, len(rows) + 1):  # 从第2行开始（跳过标题行）
                try:
                    # 查找区域列
                    region_xpath = f"{standard_shipping_xpath}/tr[{i}]/td[{region_column_index}]"
                    region_cell = WebDriverWait(driver, 3).until(
                        EC.presence_of_element_located((By.XPATH, region_xpath))
                    )
                    
                    # 从div.regions_name_left下的span获取地区名称
                    try:
                        region_span = region_cell.find_element(By.XPATH, ".//div[contains(@class, 'regions_name_left')]//span")
                        region_text = region_span.text.strip()
                    except:
                        # 如果找不到span元素，则使用整个cell的文本
                        region_text = region_cell.text.strip()

                    log_info(f"{country}: 检查第{i}行地区: '{region_text}'")

                    if "加拿大" in region_text:
                        target_row_index = i
                        log_success(f"{country}: 找到加拿大地区行，第{i}行: {region_text}")
                        break
                except Exception as e:
                    log_warning(f"{country}: 读取第{i}行地区名称失败: {str(e)}")
                    continue

            if target_row_index:
                # 设置运费
                log_step(f"{country}: 设置加拿大地区运费 - 每订单:0, 每单位:20")
                
                if not self.set_shipping_fees_by_row(driver, standard_shipping_xpath, header_dict, 
                                                    target_row_index, "0", "20", f"{country}: 加拿大"):
                    error_msg = "设置加拿大地区运费失败"
                    self.log_setting_exception(store_info, country, "配送设置", f"加拿大特殊地区处理失败: {error_msg}")
                    return False

                # 设置单位下拉框为"商品"
                log_step(f"{country}: 设置加拿大地区单位下拉框为'商品'")
                if not self.set_canada_fee_unit(driver, standard_shipping_xpath, target_row_index, f"{country}: 加拿大"):
                    error_msg = "设置加拿大地区单位下拉框失败"
                    self.log_setting_exception(store_info, country, "配送设置", f"加拿大特殊地区处理失败: {error_msg}")
                    return False

                log_success(f"{country}: 加拿大特殊配送地区设置完成")
                return True
            else:
                error_msg = "未找到加拿大地区行"
                log_error(f"{country}: {error_msg}")
                self.log_setting_exception(store_info, country, "配送设置", f"加拿大特殊地区处理失败: {error_msg}")
                return False

        except Exception as e:
            error_msg = f"处理加拿大特殊配送地区时发生错误: {str(e)}"
            log_error(f"{country}: {error_msg}")
            self.log_setting_exception(getattr(self, 'current_store_info', None), country, "配送设置", f"加拿大特殊地区处理失败: {error_msg}")
            return False

    @crash_recovery_decorator
    def handle_mexico_special_regions(self, driver, country, store_info, standard_shipping_xpath, special_regions, header_dict):
        """
        处理墨西哥特殊配送地区设置
        1. 遍历所有行，设置运输时间为"19-22天"
        2. 设置每行的运费：每订单(pricePerOrder)为0，每单位(unitPrice)为0
        """
        try:
            log_step(f"{country}: 开始处理墨西哥特殊配送地区设置")

            # 检查表格是否存在
            try:
                table_body = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, standard_shipping_xpath))
                )
                log_success(f"{country}: 找到墨西哥配送设置表格")
            except TimeoutException:
                error_msg = "未找到墨西哥配送设置表格"
                log_error(f"{country}: {error_msg}")
                self.log_setting_exception(getattr(self, 'current_store_info', None), country, "配送设置", f"墨西哥特殊地区处理失败: {error_msg}")
                return False

            # 获取所有行
            rows = table_body.find_elements(By.XPATH, "./tr")
            log_info(f"{country}: 找到 {len(rows)} 行运输时间数据")

            # 处理每一行
            for row_index in range(2, len(rows) + 1):  # 从第2行开始（跳过标题行）
                log_step(f"{country}: 处理第{row_index}行")
                
                # 设置运输时间为"19-22天"
                log_step(f"{country}: 设置第{row_index}行运输时间为'19-22天'")
                self._handle_shipping_time_by_text(driver, country, standard_shipping_xpath, row_index, header_dict, ["19-22天",["19","22","天"]])
                
                # 设置运费：每订单为0，每单位为0
                log_step(f"{country}: 设置第{row_index}行运费 - 每订单:0, 每单位:0")
                if not self.set_shipping_fees_by_row(driver, standard_shipping_xpath, header_dict, 
                                                    row_index, "0", "0", f"{country}: 墨西哥第{row_index}行"):
                    error_msg = f"设置第{row_index}行运费失败"
                    self.log_setting_exception(store_info, country, "配送设置", f"墨西哥特殊地区处理失败: {error_msg}")
                    return False

            log_success(f"{country}: 墨西哥特殊配送地区设置完成")
            return True

        except Exception as e:
            error_msg = f"处理墨西哥特殊配送地区时发生错误: {str(e)}"
            log_error(f"{country}: {error_msg}")
            self.log_setting_exception(getattr(self, 'current_store_info', None), country, "配送设置", f"墨西哥特殊地区处理失败: {error_msg}")
            return False

    def handle_spain_special_regions(self, driver, country, store_info, standard_shipping_xpath, special_regions, header_dict):
        """处理西班牙特殊配送地区设置 - 使用升级版通用EU方法"""
        return self.handle_eu_special_regions(
            driver, country, store_info, standard_shipping_xpath, special_regions, header_dict,
            shipping_time="14 - 18 工作日", price_per_order="0", unit_price="0", unit_type="商品",
            skip_unit_setting=False, max_retries=3
        )



    @crash_recovery_decorator
    def handle_eu_special_regions(self, driver, country, store_info, standard_shipping_xpath, special_regions, header_dict,
                                 shipping_time="14 - 18 工作日", price_per_order="0", unit_price="0", unit_type="商品",
                                 skip_unit_setting=False, max_retries=3):
        """
        处理欧盟国家特殊配送地区设置的通用方法 (升级版)
        适用于使用EU_STANDARD.DOMESTIC结构的所有欧盟国家

        Args:
            shipping_time: 运输时间设置
            price_per_order: 每订单运费
            unit_price: 每单位运费
            unit_type: 运费单位类型
            skip_unit_setting: 是否跳过单位设置 (默认False)
            max_retries: 最大重试次数 (默认3次)

        Features:
            - 内存监控和优化
            - 重试机制 (每个地区独立重试)
            - 灵活的单位设置控制
            - 统一的错误处理和日志记录
        """
        try:
            log_step(f"{country}: 开始处理欧盟特殊配送地区设置")

            # 检查表格是否存在
            try:
                table_body = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, standard_shipping_xpath))
                )
                log_success(f"{country}: 找到欧盟配送设置表格")
            except TimeoutException:
                error_msg = f"未找到{country}配送设置表格"
                log_error(f"{country}: {error_msg}")
                self.log_setting_exception(store_info, country, "配送设置", f"{country}特殊地区处理失败: {error_msg}")
                return False

            # 步骤1: 删除不需要的地区（由具体国家的删除方法处理）
            country_code = self.get_country_code(country)
            delete_method_name = f"_delete_unwanted_{country_code.lower()}_regions"

            if hasattr(self, delete_method_name):
                delete_method = getattr(self, delete_method_name)
                log_step(f"{country}: 删除不需要的地区")
                if not delete_method(driver, country, standard_shipping_xpath, header_dict):
                    error_msg = f"删除不需要的{country}地区失败"
                    log_error(f"{country}: {error_msg}")
                    self.log_setting_exception(store_info, country, "配送设置", f"{country}特殊地区处理失败: {error_msg}")
                    return False
            else:
                log_info(f"{country}: 未找到专用删除方法，跳过地区删除步骤")

            # 步骤2: 重新获取表格以避免索引变化
            log_step(f"{country}: 重新获取表格以避免索引变化")
            try:
                time.sleep(2)  # 等待DOM更新
                table_body = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, standard_shipping_xpath))
                )
                rows = table_body.find_elements(By.XPATH, "./tr")
                log_success(f"{country}: 重新获取表格成功，当前有 {len(rows)} 行")
            except TimeoutException:
                error_msg = f"重新获取{country}配送设置表格失败"
                log_error(f"{country}: {error_msg}")
                self.log_setting_exception(store_info, country, "配送设置", f"{country}特殊地区处理失败: {error_msg}")
                return False

            # 步骤3: 统一设置所有地区 (升级版：添加内存监控和重试机制)
            success_count = 0
            total_regions = len(rows) - 1  # 减去标题行

            # 内存监控：在设置前检查内存
            if hasattr(self, 'memory_monitor') and self.memory_monitor.should_optimize(country):
                log_warning(f"{country}: 内存使用率过高，进行内存优化")
                if hasattr(self, 'memory_optimization'):
                    self.memory_optimization(driver, country)

            for row_index in range(2, len(rows) + 1):  # 从第2行开始（跳过标题行）
                region_name = self._get_region_name_from_row(driver, standard_shipping_xpath, row_index, header_dict)
                log_step(f"{country}: 处理第{row_index}行 - {region_name}")

                # 为每个地区添加重试机制
                retry_count = 0
                region_success = False

                while retry_count < max_retries and not region_success:
                    try:
                        log_info(f"{country}: 设置{region_name} (第{retry_count + 1}次尝试)")

                        # 设置运输时间
                        time_success = self._set_eu_shipping_time(driver, country, standard_shipping_xpath, row_index, header_dict, shipping_time)
                        if not time_success:
                            raise Exception("运输时间设置失败")

                        # 设置运费
                        fee_success = self._set_eu_shipping_fees(driver, country, standard_shipping_xpath, row_index, header_dict, price_per_order, unit_price)
                        if not fee_success:
                            raise Exception("运费设置失败")

                        # 设置单位 (根据参数决定是否跳过)
                        if skip_unit_setting:
                            log_info(f"{country}: 第{row_index}行跳过单位设置 - {region_name}")
                            unit_success = True
                        else:
                            unit_success = self._set_eu_fee_unit(driver, country, standard_shipping_xpath, row_index, header_dict, unit_type)
                            if not unit_success:
                                raise Exception("单位设置失败")

                        # 所有设置成功
                        success_count += 1
                        region_success = True
                        log_success(f"{country}: 第{row_index}行设置完成 - {region_name}")

                    except Exception as e:
                        retry_count += 1
                        error_msg = f"设置{region_name}失败 (第{retry_count}次): {str(e)}"
                        log_warning(f"{country}: {error_msg}")

                        if retry_count >= max_retries:
                            log_error(f"{country}: 第{row_index}行设置失败，已达到最大重试次数 - {region_name}")
                            self.log_setting_exception(store_info, country, "配送设置", f"{country}特殊地区处理失败: {error_msg}")
                        else:
                            time.sleep(1)  # 等待后重试

            # 检查设置结果
            if success_count == total_regions:
                log_success(f"{country}: 欧盟特殊配送地区设置完成，成功设置 {success_count}/{total_regions} 个地区")
                return True
            else:
                error_msg = f"部分地区设置失败，成功设置 {success_count}/{total_regions} 个地区"
                log_error(f"{country}: {error_msg}")
                self.log_setting_exception(store_info, country, "配送设置", f"{country}特殊地区处理失败: {error_msg}")
                return False

        except Exception as e:
            error_msg = f"处理{country}特殊配送地区时发生错误: {str(e)}"
            log_error(f"{country}: {error_msg}")
            self.log_setting_exception(store_info, country, "配送设置", f"{country}特殊地区处理失败: {error_msg}")
            return False

    def handle_italy_special_regions(self, driver, country, store_info, standard_shipping_xpath, special_regions, header_dict):
        """处理意大利特殊配送地区设置 - 使用升级版通用EU方法"""
        return self.handle_eu_special_regions(
            driver, country, store_info, standard_shipping_xpath, special_regions, header_dict,
            shipping_time="14 - 18 工作日", price_per_order="0", unit_price="0", unit_type="商品",
            skip_unit_setting=False, max_retries=3
        )

    def handle_uk_special_regions(self, driver, country, store_info, standard_shipping_xpath, special_regions, header_dict):
        """处理英国特殊配送地区设置 - 使用升级版通用EU方法"""
        return self.handle_eu_special_regions(
            driver, country, store_info, standard_shipping_xpath, special_regions, header_dict,
            shipping_time="14 - 18 工作日", price_per_order="0", unit_price="0", unit_type="商品",
            skip_unit_setting=False, max_retries=3
        )

    def set_canada_fee_unit(self, driver, table_xpath, row_index, log_prefix):
        """
        设置加拿大运费单位下拉框为"商品"
        根据用户提供的HTML结构，需要点击下拉框并选择"商品"选项
        """
        try:
            log_step(f"{log_prefix}: 开始设置单位下拉框为'商品'")
            
            # 获取运费列
            tr_elements = driver.find_elements(By.XPATH, f"{table_xpath}/tr[{row_index}]")
            if len(tr_elements) > 1:
                log_error(f"{log_prefix}: 找到多个tr元素，无法确定目标行")
                return False
                
            tr_element = tr_elements[0]
            td_elements = tr_element.find_elements(By.XPATH, "./td")
            shipping_fee_td = None
            
            for td in td_elements:
                if "shippingFee" in td.get_attribute("class"):
                    shipping_fee_td = td
                    break
                    
            if not shipping_fee_td:
                log_error(f"{log_prefix}: 未找到运费列")
                return False

            # 查找单位下拉框 - 根据用户提供的HTML结构
            try:
                # 查找name为shippingFeePlusDiv的div下的下拉框
                unit_dropdown = shipping_fee_td.find_element(By.XPATH, ".//div[@name='shippingFeePlusDiv']//span[@class='a-dropdown-container']/span/span/span")
                
                # 点击下拉框
                if self.safe_click(driver, unit_dropdown, f"{log_prefix}单位下拉框"):
                    log_success(f"{log_prefix}: 成功点击单位下拉框")
                    time.sleep(1)  # 等待下拉菜单展开
                    
                    # 查找并点击"商品"选项
                    # 参考运输时间下拉框的文本匹配逻辑
                    try:
                        # 获取所有选项
                        all_options = driver.find_elements(By.XPATH, "//ul[@role='listbox']//li[@role='option']")
                        log_info(f"{log_prefix}: 找到 {len(all_options)} 个单位选项")
                        
                        # 记录所有选项的文本，用于调试
                        for idx, option in enumerate(all_options):
                            log_info(f"{log_prefix}: 选项{idx+1}: {option.text}")
                        
                        # 查找"商品"选项
                        target_option = None
                        for option in all_options:
                            option_text = option.text.strip()
                            if "商品" in option_text:
                                target_option = option
                                log_info(f"{log_prefix}: 找到'商品'选项: {option_text}")
                                break
                        
                        if target_option:
                            if self.safe_click(driver, target_option, f"{log_prefix}'商品'选项"):
                                log_success(f"{log_prefix}: 成功选择'商品'单位")
                                time.sleep(0.5)  # 等待选择生效
                                return True
                            else:
                                log_error(f"{log_prefix}: 点击'商品'选项失败")
                                return False
                        else:
                            log_error(f"{log_prefix}: 未找到'商品'选项")
                            return False
                            
                    except Exception as e:
                        log_error(f"{log_prefix}: 选择单位选项失败: {str(e)}")
                        return False
                        
                else:
                    log_error(f"{log_prefix}: 点击单位下拉框失败")
                    return False
                    
            except Exception as e:
                log_error(f"{log_prefix}: 设置单位下拉框失败: {str(e)}")
                return False

        except Exception as e:
            log_error(f"{log_prefix}: 设置加拿大运费单位时发生错误: {str(e)}")
            return False





    def _delete_unwanted_fr_regions(self, driver, country, standard_shipping_xpath, header_dict):
        """删除不需要的法国地区行，只保留指定的2个地区"""
        # 保留的地区名称 - 必须与页面显示的文本完全一致
        keep_regions = ["法国本土和摩纳哥", "科西嘉岛"]

        # 调用通用删除方法，使用精确匹配策略
        return self._delete_unwanted_regions(
            driver, country, standard_shipping_xpath, header_dict,
            keep_regions, match_strategy='exact'
        )

    def _delete_unwanted_spain_regions(self, driver, country, standard_shipping_xpath, header_dict):
        """删除不需要的西班牙地区行，只保留指定的3个地区"""
        # 保留的地区名称 - 必须与页面显示的文本完全一致
        keep_regions = ["西班牙大陆", "巴利阿里群岛", "加那利群岛、休达和梅利利亚"]

        # 调用通用删除方法，使用精确匹配策略
        return self._delete_unwanted_regions(
            driver, country, standard_shipping_xpath, header_dict,
            keep_regions, match_strategy='exact'
        )

    def _delete_unwanted_italy_regions(self, driver, country, standard_shipping_xpath, header_dict):
        """删除不需要的意大利地区行，只保留指定的2个地区"""
        # 保留的地区名称 - 必须与页面显示的文本完全一致
        keep_regions = ["意大利大陆, 意大利本土偏远地区", "意大利岛屿（撒丁岛、西西里岛、厄尔巴岛和其他岛屿）"]

        # 调用通用删除方法，使用精确匹配策略
        return self._delete_unwanted_regions(
            driver, country, standard_shipping_xpath, header_dict,
            keep_regions, match_strategy='exact'
        )

    def _delete_unwanted_gb_regions(self, driver, country, standard_shipping_xpath, header_dict):
        """删除不需要的英国地区行，只保留指定的3个地区"""
        # 保留的地区名称 - 必须与页面显示的文本完全一致
        keep_regions = [
            "苏格兰(邓弗里斯, 爱丁堡, 格拉斯哥, 基尔马诺克, 马瑟韦尔), 英格兰, 威尔士",
            "苏格兰(阿伯丁, 邓迪, 福尔柯克, 外赫布里底群岛, 因弗尼斯, 柯克沃尔, 柯科迪, 佩斯里, 珀斯, 加拉希尔斯, 设得兰群岛), 北爱尔兰, 海峡群岛, 马恩岛",
            "BFPO"
        ]

        # 调用通用删除方法，使用精确匹配策略
        return self._delete_unwanted_regions(
            driver, country, standard_shipping_xpath, header_dict,
            keep_regions, match_strategy='exact'
        )

    def _delete_unwanted_de_regions(self, driver, country, standard_shipping_xpath, header_dict):
        """德国地区删除方法 - 德国不需要删除地区，保留现有的2个地区"""
        # 德国保留所有现有地区：德国大陆、离岸地区
        # 不执行删除操作，直接返回成功
        log_info(f"{country}: 德国保留所有现有地区，无需删除操作")
        return True

    def _delete_unwanted_regions(self, driver, country, standard_shipping_xpath, header_dict,
                               keep_regions, match_strategy='exact'):
        """
        通用的删除不需要地区行的方法

        Args:
            driver: WebDriver实例
            country: 国家名称
            standard_shipping_xpath: 表格xpath
            header_dict: 表头字典
            keep_regions: 需要保留的地区列表
            match_strategy: 匹配策略 ('exact': 精确匹配, 'contains': 包含匹配)

        Returns:
            bool: 删除是否成功
        """
        try:
            log_step(f"{country}: 开始删除不需要的地区行，保留策略: {match_strategy}")

            # 查找所有地区行
            region_rows = driver.find_elements(By.XPATH, f"{standard_shipping_xpath}/tr[contains(@class, 'nonPrimeRule')]")

            log_info(f"{country}: 找到 {len(region_rows)} 个地区行")

            # 获取区域列的序号
            region_column_index = self.get_column_index(header_dict, '区域')
            if region_column_index is None:
                log_error(f"{country}: 未找到区域列")
                return False

            # 获取操作列的序号
            operation_column_index = self.get_column_index(header_dict, '操作')
            if operation_column_index is None:
                log_error(f"{country}: 未找到操作列")
                return False

            # 需要删除的行索引（从后往前删除以避免索引变化）
            rows_to_delete = []
            for i, row in enumerate(region_rows):
                try:
                    # 获取地区名称 - 精确匹配regions_name_left下的内容
                    region_cell = row.find_element(By.XPATH, f"./td[{region_column_index}]")
                    try:
                        # 精确获取regions_name_left下的span元素文本
                        region_span = region_cell.find_element(By.XPATH, ".//div[@class='a-section regions_name_left']//span")
                        region_name = region_span.text.strip()
                    except:
                        # 如果精确匹配失败，尝试包含匹配作为备选
                        try:
                            region_span = region_cell.find_element(By.XPATH, ".//div[contains(@class, 'regions_name_left')]//span")
                            region_name = region_span.text.strip()
                        except:
                            # 最后备选：使用整个cell的文本，但排除编辑按钮文本
                            full_text = region_cell.text.strip()
                            # 移除"编辑"文本
                            region_name = full_text.replace("编辑", "").strip()

                    log_info(f"{country}: 检查地区行 {i+1}: '{region_name}'")

                    # 根据匹配策略判断是否需要保留
                    should_keep = False
                    if match_strategy == 'exact':
                        # 精确匹配：页面地区文本完全等于保留列表中的某一项
                        should_keep = region_name in keep_regions
                    elif match_strategy == 'contains':
                        # 包含匹配：页面地区文本包含保留列表中的某一项
                        for keep_region in keep_regions:
                            if keep_region in region_name:
                                should_keep = True
                                break

                    if not should_keep:
                        rows_to_delete.append((i, region_name, row))
                        log_info(f"{country}: 标记删除地区: '{region_name}'")
                    else:
                        log_info(f"{country}: 保留地区: '{region_name}'")

                except Exception as e:
                    log_warning(f"{country}: 读取第 {i+1} 行地区名称失败: {str(e)}")
                    continue

            # 从后往前删除行（避免索引变化）
            for i, region_name, row in reversed(rows_to_delete):
                try:
                    log_step(f"{country}: 删除地区 '{region_name}'")

                    # 查找删除按钮 - 在操作列中
                    delete_button = row.find_element(By.XPATH, f"./td[{operation_column_index}]//a[contains(@class, 'deleteConfigRule')]")

                    # 使用安全点击方法
                    if self.safe_click(driver, delete_button, f"删除{region_name}地区按钮"):
                        log_success(f"{country}: 成功点击删除{region_name}地区按钮")

                        # 等待可能的确认弹窗
                        time.sleep(1)
                        try:
                            confirm_button = WebDriverWait(driver, 3).until(
                                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '确认') or contains(text(), 'Confirm')]"))
                            )
                            if self.safe_click(driver, confirm_button, "确认删除按钮"):
                                log_success(f"{country}: 确认删除{region_name}地区")
                            else:
                                log_warning(f"{country}: 确认删除{region_name}地区失败")
                        except TimeoutException:
                            # 没有确认弹窗，直接删除成功
                            log_info(f"{country}: 删除{region_name}地区无需确认")
                    else:
                        log_error(f"{country}: 删除{region_name}地区按钮点击失败")

                except Exception as delete_e:
                    log_error(f"{country}: 删除{region_name}地区时发生错误: {str(delete_e)}")
                time.sleep(1)

            log_success(f"{country}: 完成删除不需要的地区行")
            return True

        except Exception as e:
            error_msg = f"删除不需要的地区时发生错误: {str(e)}"
            log_error(f"{country}: {error_msg}")
            return False

    def _get_region_name_from_row(self, driver, standard_shipping_xpath, row_index, header_dict):
        """从指定行获取地区名称"""
        try:
            region_column_index = self.get_column_index(header_dict, '区域')
            if region_column_index is None:
                return "未知地区"

            region_xpath = f"{standard_shipping_xpath}/tr[{row_index}]/td[{region_column_index}]"
            region_cell = driver.find_element(By.XPATH, region_xpath)

            try:
                # 精确获取regions_name_left下的span元素文本
                region_span = region_cell.find_element(By.XPATH, ".//div[@class='a-section regions_name_left']//span")
                return region_span.text.strip()
            except:
                # 如果精确匹配失败，尝试包含匹配作为备选
                try:
                    region_span = region_cell.find_element(By.XPATH, ".//div[contains(@class, 'regions_name_left')]//span")
                    return region_span.text.strip()
                except:
                    # 最后备选：使用整个cell的文本，但排除编辑按钮文本
                    full_text = region_cell.text.strip()
                    # 移除"编辑"文本
                    return full_text.replace("编辑", "").strip()
        except Exception as e:
            log_warning(f"获取第{row_index}行地区名称失败: {str(e)}")
            return f"第{row_index}行"

    def _set_spain_shipping_time(self, driver, country, standard_shipping_xpath, row_index, header_dict):
        """设置西班牙地区的运输时间为14-18工作日"""
        return self._set_eu_shipping_time(driver, country, standard_shipping_xpath, row_index, header_dict, "14 - 18 工作日")

    def _set_spain_shipping_fees(self, driver, country, standard_shipping_xpath, row_index, header_dict):
        """设置西班牙地区的运费为0"""
        return self._set_eu_shipping_fees(driver, country, standard_shipping_xpath, row_index, header_dict, "0", "0")

    def _set_spain_fee_unit(self, driver, country, standard_shipping_xpath, row_index, header_dict):
        """设置西班牙地区的运费单位为商品"""
        return self._set_eu_fee_unit(driver, country, standard_shipping_xpath, row_index, header_dict, "商品")

    def _set_eu_shipping_time(self, driver, country, standard_shipping_xpath, row_index, header_dict, target_time="14 - 18 工作日"):
        """设置欧盟国家地区的运输时间（通用方法）"""
        try:
            # 使用现有的运输时间设置方法，支持多种文本格式
            self._handle_shipping_time_by_text(
                driver, country, standard_shipping_xpath, row_index, header_dict,
                [target_time, target_time.replace(" ", ""), target_time.replace(" - ", "-")],
                fallback_to_last=False
            )
            return True
        except Exception as e:
            log_error(f"{country}: 设置第{row_index}行运输时间失败: {str(e)}")
            return False

    def _set_eu_shipping_fees(self, driver, country, standard_shipping_xpath, row_index, header_dict, price_per_order="0", unit_price="0"):
        """设置欧盟国家地区的运费（通用方法）"""
        try:
            # 使用现有的运费设置方法
            return self.set_shipping_fees_by_row(
                driver, standard_shipping_xpath, header_dict,
                row_index, price_per_order, unit_price, f"{country}: 欧盟第{row_index}行"
            )
        except Exception as e:
            log_error(f"{country}: 设置第{row_index}行运费失败: {str(e)}")
            return False

    def _set_eu_fee_unit(self, driver, country, standard_shipping_xpath, row_index, header_dict, unit_type="商品"):
        """设置欧盟国家地区的运费单位（通用方法）"""
        try:
            # 获取运费列
            shipping_fee_column_index = self.get_column_index(header_dict, '运费')
            if shipping_fee_column_index is None:
                log_error(f"{country}: 未找到运费列")
                return False

            # 查找单位选择框
            unit_select_xpath = f"{standard_shipping_xpath}/tr[{row_index}]/td[{shipping_fee_column_index}]//select[@name='unitMeasure']"

            try:
                unit_select = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, unit_select_xpath))
                )

                # 使用Select类选择指定单位
                from selenium.webdriver.support.ui import Select
                select = Select(unit_select)

                # 根据单位类型选择对应的value
                unit_value_mapping = {
                    "商品": "Per Item",
                    "Kg": "Per Kilo"
                }

                target_value = unit_value_mapping.get(unit_type, "Per Item")

                # 尝试通过value选择
                try:
                    select.select_by_value(target_value)
                    log_success(f"{country}: 第{row_index}行单位设置为'{unit_type}'成功")
                    return True
                except:
                    # 如果通过value失败，尝试通过可见文本选择
                    try:
                        select.select_by_visible_text(unit_type)
                        log_success(f"{country}: 第{row_index}行单位设置为'{unit_type}'成功")
                        return True
                    except:
                        log_error(f"{country}: 第{row_index}行单位设置失败，无法找到'{unit_type}'选项")
                        return False

            except TimeoutException:
                log_error(f"{country}: 第{row_index}行未找到单位选择框")
                return False

        except Exception as e:
            log_error(f"{country}: 设置第{row_index}行单位失败: {str(e)}")
            return False

    def _set_italy_shipping_time(self, driver, country, standard_shipping_xpath, row_index, header_dict):
        """设置意大利地区的运输时间为14-18工作日"""
        return self._set_eu_shipping_time(driver, country, standard_shipping_xpath, row_index, header_dict, "14 - 18 工作日")

    def _set_italy_shipping_fees(self, driver, country, standard_shipping_xpath, row_index, header_dict):
        """设置意大利地区的运费为0"""
        return self._set_eu_shipping_fees(driver, country, standard_shipping_xpath, row_index, header_dict, "0", "0")

    def _set_italy_fee_unit(self, driver, country, standard_shipping_xpath, row_index, header_dict):
        """设置意大利地区的运费单位为商品"""
        return self._set_eu_fee_unit(driver, country, standard_shipping_xpath, row_index, header_dict, "商品")

    def handle_us_fba_settings(self, driver, country, store_info):
        """
        处理美国的亚马逊物流(FBA)设置（新方法）
        在完成配送设置后，先导航到商铺首页，再进行FBA相关配置
        """
        try:
            log_step(f"{country}: 开始处理亚马逊物流(FBA)设置")

            # 步骤0: 先导航到商铺首页
            log_step(f"{country}: 导航到商铺首页")
            if not self.navigate_to_seller_central_home(driver):
                log_error(f"{country}: 导航到商铺首页失败")
                return False

            # 等待页面加载
            time.sleep(3)
            self.handle_edit_page_popup(driver, country)

            # 步骤1: 点击设置按钮
            log_step(f"{country}: 点击设置按钮")
            if not self.click_settings_button(driver, country):
                self.log_setting_exception(store_info, country, "亚马逊物流", "点击设置按钮失败")
                return False

            # 设置按钮点击后检查弹窗
            self.handle_edit_page_popup(driver, country)

            # 步骤2: 点击亚马逊物流链接
            log_step(f"{country}: 点击亚马逊物流链接")
            try:
                fba_link = WebDriverWait(driver, 15).until(
                    EC.element_to_be_clickable((By.XPATH, "//*[@id='navbar']/div[3]/div/div[2]/div/div[2]/div[1]/div/div[1]/a[11]"))
                )
                if self.safe_click(driver, fba_link, f"{country}亚马逊物流链接"):
                    log_success(f"{country}: 已点击亚马逊物流链接")
                    time.sleep(5)  # 等待页面加载
                    self.handle_edit_page_popup(driver, country)
                else:
                    self.log_setting_exception(store_info, country, "亚马逊物流", "点击亚马逊物流链接失败")
                    return False
            except TimeoutException:
                self.log_setting_exception(store_info, country, "亚马逊物流", "未找到亚马逊物流链接")
                return False

            # 步骤3: 处理入库设置部分（新逻辑）
            log_step(f"{country}: 处理入库设置部分")
            try:
                self.handle_new_inbound_settings(driver, country)
            except Exception as e:
                self.log_setting_exception(store_info, country, "亚马逊物流", f"处理入库设置时发生错误: {str(e)}")
                return False

            # 步骤4: 处理不可售商品自动设置（新逻辑）
            log_step(f"{country}: 处理不可售商品自动设置")
            try:
                self.handle_new_unfulfillable_settings(driver, country)
            except Exception as e:
                self.log_setting_exception(store_info, country, "亚马逊物流", f"处理不可售商品自动设置时发生错误: {str(e)}")
                return False

            # 步骤5: 处理亚马逊物流商品条形码首选项（保持不变）
            log_step(f"{country}: 处理条形码首选项")
            try:
                self.handle_barcode_preferences(driver, country)
            except Exception as e:
                self.log_setting_exception(store_info, country, "亚马逊物流", f"处理条形码首选项时发生错误: {str(e)}")
                return False

            # 步骤6: 处理可售商品自动设置（新增）
            log_step(f"{country}: 处理可售商品自动设置")
            try:
                self.handle_sellable_settings(driver, country)
            except Exception as e:
                self.log_setting_exception(store_info, country, "亚马逊物流", f"处理可售商品自动设置时发生错误: {str(e)}")
                return False

            log_success(f"{country}: 亚马逊物流(FBA)设置处理完成")
            return True

        except Exception as e:
            self.log_setting_exception(store_info, country, "亚马逊物流", f"处理亚马逊物流(FBA)设置时发生错误: {str(e)}")
            return False

    # 原有的可选服务处理方法（暂时注释）
    def handle_optional_services_old(self, driver, country):
        """处理可选服务部分的MWS贴标服务（原有方法，暂时注释）"""
        try:
            log_step(f"{country}: 检查可选服务部分")

            # 查找可选服务表格中的所有行
            table_rows = driver.find_elements(By.XPATH, "//*[@id='root']/div/div/div[1]/kat-table/kat-table-body/kat-table-row")

            mws_row_found = False
            for i, row in enumerate(table_rows, 1):
                try:
                    # 检查第一列是否包含"MWS 贴标服务"
                    first_cell = row.find_element(By.XPATH, f"//*[@id='root']/div/div/div[1]/kat-table/kat-table-body/kat-table-row[{i}]/kat-table-cell[1]")
                    if "MWS 贴标服务" in first_cell.text:
                        log_success(f"{country}: 找到MWS贴标服务行，第{i}行")
                        mws_row_found = True

                        # 检查第二列的状态
                        second_cell = row.find_element(By.XPATH, f"//*[@id='root']/div/div/div[1]/kat-table/kat-table-body/kat-table-row[{i}]/kat-table-cell[2]")
                        if "已禁用" not in second_cell.text:
                            log_step(f"{country}: MWS贴标服务未禁用，需要编辑")

                            # 点击编辑按钮
                            edit_button = WebDriverWait(driver, 10).until(
                                EC.element_to_be_clickable((By.XPATH, "//*[@id='root']/div/div/div[1]/kat-table/kat-table-head/kat-table-row/kat-table-cell[2]/span/a/kat-button"))
                            )
                            if self.safe_click(driver, edit_button, f"{country}可选服务编辑按钮"):
                                time.sleep(3)

                                # 在编辑页面中找到MWS贴标服务行并禁用
                                self.disable_mws_labeling_service(driver, country)

                                # 点击更新按钮
                                update_button = WebDriverWait(driver, 10).until(
                                    EC.element_to_be_clickable((By.XPATH, "//*[@id='root']/div/div/div/a[1]/kat-button"))
                                )
                                if self.safe_click(driver, update_button, f"{country}可选服务更新按钮"):
                                    log_success(f"{country}: 可选服务设置更新完成")
                                    time.sleep(3)  # 等待页面更新

                                    # 验证更新是否成功
                                    self.verify_optional_services_update(driver, country)

                                    # 处理编辑页面弹窗
                                    self.handle_edit_page_popup(driver, country)
                        else:
                            log_info(f"{country}: MWS贴标服务已经是禁用状态")
                        break
                except:
                    continue

            if not mws_row_found:
                log_info(f"{country}: 未找到MWS贴标服务行")

        except Exception as e:
            # 注意：这里应该在handle_us_fba_settings中调用时传入store_info
            log_error(f"{country}: 处理可选服务时发生错误: {str(e)}")

    def disable_mws_labeling_service(self, driver, country):
        """在编辑页面中禁用MWS贴标服务"""
        try:
            # 查找编辑页面中的所有行
            edit_rows = driver.find_elements(By.XPATH, "//*[@id='root']/div/div/kat-table/kat-table-body/kat-table-row")

            for i, row in enumerate(edit_rows, 1):
                try:
                    first_cell = row.find_element(By.XPATH, f"//*[@id='root']/div/div/kat-table/kat-table-body/kat-table-row[{i}]/kat-table-cell[1]")
                    if "MWS 贴标服务" in first_cell.text:
                        log_step(f"{country}: 在编辑页面找到MWS贴标服务行，第{i}行")

                        # 点击禁用单选框 //*[@id="root"]/div/div/kat-table/kat-table-body/kat-table-row[1]/kat-table-cell[2]/kat-radiobutton-group/div/kat-radiobutton[2]
                        disable_radio = WebDriverWait(driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, f"//*[@id='root']/div/div/kat-table/kat-table-body/kat-table-row[{i}]/kat-table-cell[2]/kat-radiobutton-group/div/kat-radiobutton[2]/input[@type='radio']"))
                        )
                        if self.safe_click(driver, disable_radio, f"{country}MWS贴标服务禁用单选框"):
                            log_success(f"{country}: 已选择禁用MWS贴标服务")
                        else:
                            log_error(f"{country}: 禁用MWS贴标服务失败")
                        break
                except:
                    continue

        except Exception as e:
            log_error(f"{country}: 禁用MWS贴标服务时发生错误: {str(e)}")

    # 原有的入库设置处理方法（暂时注释）
    def handle_inbound_settings_old(self, driver, country):
        """处理入库设置部分（原有方法，暂时注释）"""
        try:
            log_step(f"{country}: 检查入库设置部分")

            # 定义需要检查的设置项和期望状态
            settings_to_check = [
                ("亚马逊 API 的入库配置服务费用选项", "亚马逊优化货件拆分", 1),
                ("显示受限商品警告", "已禁用", 2),
                ("用于提供箱内物品信息的 2D 条形码", "已禁用", 3),
                ("显示商品是否可售的提示", "已启用", 4)
            ]

            need_edit = False

            # 检查每个设置项
            for setting_name, expected_status, row_num in settings_to_check:
                try:
                    row_xpath = f"//*[@id='root']/div/div/div[3]/kat-table/kat-table-body/kat-table-row[{row_num}]/kat-table-cell[1]"
                    status_xpath = f"//*[@id='root']/div/div/div[3]/kat-table/kat-table-body/kat-table-row[{row_num}]/kat-table-cell[2]"

                    # 增加等待时间确保元素可用
                    row_element = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, row_xpath))
                    )
                    status_element = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, status_xpath))
                    )

                    if setting_name in row_element.text:
                        current_status = status_element.text
                        if expected_status not in current_status:
                            log_step(f"{country}: {setting_name} 当前状态不符合期望，需要编辑")
                            need_edit = True
                        else:
                            log_info(f"{country}: {setting_name} 状态正确: {current_status}")
                except Exception as e:
                    log_info(f"{country}: 无法检查 {setting_name}: {str(e)}")

            if need_edit:
                # 点击编辑按钮，增加重试机制
                edit_success = False
                for attempt in range(3):
                    try:
                        log_step(f"{country}: 尝试点击入库设置编辑按钮 (第{attempt+1}次)")
                        edit_button = WebDriverWait(driver, 15).until(
                            EC.element_to_be_clickable((By.XPATH, "//*[@id='root']/div/div/div[3]/kat-table/kat-table-head/kat-table-row/kat-table-cell[2]/span/a/kat-button"))
                        )

                        # 滚动到编辑按钮
                        driver.execute_script("arguments[0].scrollIntoView(true);", edit_button)
                        time.sleep(1)

                        if self.safe_click(driver, edit_button, f"{country}入库设置编辑按钮"):
                            log_success(f"{country}: 入库设置编辑按钮点击成功")
                            edit_success = True
                            time.sleep(5)  # 增加等待时间
                            break
                        else:
                            log_error(f"{country}: 入库设置编辑按钮点击失败 (第{attempt+1}次)")
                            time.sleep(2)
                    except Exception as e:
                        log_error(f"{country}: 点击入库设置编辑按钮异常 (第{attempt+1}次): {str(e)}")
                        time.sleep(2)

                if edit_success:
                    # 检查页面是否正确加载
                    try:
                        # 等待编辑页面加载
                        WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.XPATH, "//*[@id='root']/div/div/kat-table/kat-table-body"))
                        )
                        log_success(f"{country}: 入库设置编辑页面加载成功")

                        # 在编辑页面中进行具体设置
                        self.configure_inbound_settings(driver, country)

                        # 点击更新按钮
                        update_success = False
                        for attempt in range(3):
                            try:
                                update_button = WebDriverWait(driver, 10).until(
                                    EC.element_to_be_clickable((By.XPATH, "//*[@id='root']/div/div/div/a[1]/kat-button"))
                                )
                                if self.safe_click(driver, update_button, f"{country}入库设置更新按钮"):
                                    log_success(f"{country}: 入库设置更新完成")
                                    update_success = True
                                    time.sleep(5)

                                    # 验证入库设置更新是否成功
                                    self.verify_inbound_settings_update(driver, country)

                                    # 处理编辑页面弹窗
                                    self.handle_edit_page_popup(driver, country)
                                    break
                                else:
                                    log_error(f"{country}: 入库设置更新按钮点击失败 (第{attempt+1}次)")
                                    time.sleep(2)
                            except Exception as e:
                                log_error(f"{country}: 点击入库设置更新按钮异常 (第{attempt+1}次): {str(e)}")
                                time.sleep(2)

                        if not update_success:
                            log_error(f"{country}: 入库设置更新失败，已尝试3次")

                    except Exception as e:
                        log_error(f"{country}: 入库设置编辑页面加载失败: {str(e)}")
                else:
                    log_error(f"{country}: 入库设置编辑按钮点击失败，已尝试3次")
            else:
                log_info(f"{country}: 入库设置已符合要求，无需编辑")

        except Exception as e:
            log_error(f"{country}: 处理入库设置时发生错误: {str(e)}")
            log_error(f"{country}: 入库设置详细错误信息: {traceback.format_exc()}")

    def configure_inbound_settings(self, driver, country):
        """在编辑页面中配置入库设置"""
        try:
            log_step(f"{country}: 开始配置入库设置")

            # 配置亚马逊优化货件拆分
            log_step(f"{country}: 配置亚马逊优化货件拆分")
            try:
                # 查找包含"亚马逊优化货件拆分"的选项
                radio_options = WebDriverWait(driver, 10).until(
                    EC.presence_of_all_elements_located((By.XPATH, "//*[@id='root']/div/div/kat-table/kat-table-body/kat-table-row[1]/kat-table-cell[2]/div/kat-radiobutton"))
                )

                optimization_found = False
                for i, option in enumerate(radio_options):
                    try:
                        option_text = option.text
                        log_info(f"{country}: 检查选项 {i+1}: {option_text}")
                        if "亚马逊优化货件拆分" in option_text:
                            if self.safe_click(driver, option, f"{country}亚马逊优化货件拆分选项"):
                                log_success(f"{country}: 已选择亚马逊优化货件拆分")
                                optimization_found = True
                            break
                    except Exception as e:
                        log_error(f"{country}: 检查选项 {i+1} 时出错: {str(e)}")
                        continue

                if not optimization_found:
                    log_error(f"{country}: 未找到亚马逊优化货件拆分选项")

            except Exception as e:
                log_error(f"{country}: 配置亚马逊优化货件拆分失败: {str(e)}")

            # 禁用显示受限商品警告
            log_step(f"{country}: 禁用显示受限商品警告")
            # //*[@id="katal-id-8"]
            # //*[@id="root"]/div/div/kat-table/kat-table-body/kat-table-row[2]/kat-table-cell[2]/kat-radiobutton-group/div/kat-radiobutton[2]
            try:
                disable_radio = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, '//*[@id="root"]/div/div/kat-table/kat-table-body/kat-table-row[2]/kat-table-cell[2]/kat-radiobutton-group/div/kat-radiobutton[2]/input[@type="radio"]'))
                )

                # 滚动到元素
                driver.execute_script("arguments[0].scrollIntoView(true);", disable_radio)
                time.sleep(1)

                if self.safe_click(driver, disable_radio, f"{country}显示受限商品警告禁用"):
                    log_success(f"{country}: 已禁用显示受限商品警告")
                else:
                    log_error(f"{country}: 禁用显示受限商品警告失败")
            except Exception as e:
                log_error(f"{country}: 禁用显示受限商品警告异常: {str(e)}")

            # 禁用2D条形码
            log_step(f"{country}: 禁用2D条形码")
            # //*[@id="root"]/div/div/kat-table/kat-table-body/kat-table-row[3]/kat-table-cell[2]/kat-radiobutton-group/div/kat-radiobutton[2]
            try:
                disable_barcode = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, '//*[@id="root"]/div/div/kat-table/kat-table-body/kat-table-row[3]/kat-table-cell[2]/kat-radiobutton-group/div/kat-radiobutton[2]/input[@type="radio"]'))
                )

                # 滚动到元素
                driver.execute_script("arguments[0].scrollIntoView(true);", disable_barcode)
                time.sleep(1)

                if self.safe_click(driver, disable_barcode, f"{country}2D条形码禁用"):
                    log_success(f"{country}: 已禁用2D条形码")
                else:
                    log_error(f"{country}: 禁用2D条形码失败")
            except Exception as e:
                log_error(f"{country}: 禁用2D条形码异常: {str(e)}")

            # 启用显示商品是否可售的提示
            log_step(f"{country}: 启用显示商品是否可售的提示")
            try:
                enable_tips = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, '//*[@id="root"]/div/div/kat-table/kat-table-body/kat-table-row[4]/kat-table-cell[2]/kat-radiobutton-group/div/kat-radiobutton[1]/input[@type="radio"]'))
                )

                # 滚动到元素
                driver.execute_script("arguments[0].scrollIntoView(true);", enable_tips)
                time.sleep(1)

                if self.safe_click(driver, enable_tips, f"{country}显示商品是否可售的提示启用"):
                    log_success(f"{country}: 已启用显示商品是否可售的提示")
                else:
                    log_error(f"{country}: 启用显示商品是否可售的提示失败")
            except Exception as e:
                log_error(f"{country}: 启用显示商品是否可售的提示异常: {str(e)}")

            log_success(f"{country}: 入库设置配置完成")

        except Exception as e:
            log_error(f"{country}: 配置入库设置时发生错误: {str(e)}")
            log_error(f"{country}: 配置入库设置详细错误信息: {traceback.format_exc()}")

    # 原有的不可售商品自动设置处理方法（暂时注释）
    def handle_unfulfillable_settings_old(self, driver, country):
        """处理不可售商品自动设置部分（原有方法，暂时注释）"""
        try:
            log_step(f"{country}: 检查不可售商品自动设置")

            # 检查不可售商品自动设置是否已启用
            settings_compliant = True

            try:
                # 检查不可售商品自动设置
                auto_setting_element = driver.find_element(By.XPATH, "//*[@id='root']/div/div/div[5]/kat-table/kat-table-body/kat-table-row[1]/kat-table-cell[2]")
                if "已启用" not in auto_setting_element.text:
                    settings_compliant = False
                    log_step(f"{country}: 不可售商品自动设置未启用")
            except:
                settings_compliant = False
                log_info(f"{country}: 无法检查不可售商品自动设置状态")

            try:
                # 检查退还或弃置设置
                disposal_element = driver.find_element(By.XPATH, "//*[@id='root']/div/div/div[5]/kat-table/kat-table-body/kat-table-row[3]/kat-table-cell[2]")
                if "弃置" not in disposal_element.text:
                    settings_compliant = False
                    log_step(f"{country}: 退还或弃置设置不是弃置")
            except:
                settings_compliant = False
                log_info(f"{country}: 无法检查退还或弃置设置")

            if not settings_compliant:
                # 点击编辑按钮
                edit_button = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//*[@id='root']/div/div/div[5]/kat-table/kat-table-head/kat-table-row/kat-table-cell[2]/span/a/kat-button"))
                )
                if self.safe_click(driver, edit_button, f"{country}不可售商品自动设置编辑按钮"):
                    time.sleep(3)

                    # 处理可能的弹窗 //*[@id="sc-content-container"]/section/ngb-modal-window/div/div/div[3]/kat-button
                    #//*[@id="sc-content-container"]/section/ngb-modal-window/div/div/div[3]/kat-button//button
                    try:
                        confirm_button = WebDriverWait(driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, "//*[@id='sc-content-container']/section/ngb-modal-window/div/div/div[3]/kat-button"))
                        )
                        if self.safe_click(driver, confirm_button, f"{country}确定配置弹窗"):
                            log_success(f"{country}: 已点击确定以配置设置")
                            time.sleep(2)
                    except:
                        log_info(f"{country}: 未发现配置弹窗")

                    # 启用不可售商品自动设置 //*[@id="sc-content-container"]/section/app-root/recovery-rsu-automated-unfulfillable/div[1]/div[2]/recovery-rsu-overview/kat-table/kat-table-body/kat-table-row/kat-table-cell/recovery-setting-row/div/div[2]/kat-radiobutton[1]
                    try:
                        enable_checkbox = WebDriverWait(driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, '//*[@id="sc-content-container"]/section/app-root/recovery-rsu-automated-unfulfillable/div[1]/div[2]/recovery-rsu-overview/kat-table/kat-table-body/kat-table-row/kat-table-cell/recovery-setting-row/div/div[2]/kat-radiobutton[1]/input[@type="radio"]'))
                        )
                        if self.safe_click(driver, enable_checkbox, f"{country}启用不可售商品自动设置"):
                            log_success(f"{country}: 已启用不可售商品自动设置")
                            time.sleep(2)
                    except:
                        log_error(f"{country}: 启用不可售商品自动设置失败")

                    # 设置退还或弃置为弃置
                    try:
                        # 查找退还或弃置行 //*[@id="sc-content-container"]/section/app-root/recovery-rsu-automated-unfulfillable/div[1]/div[2]/recovery-rsu-return-or-dispose/div/kat-table/kat-table-body/kat-table-row[2]/kat-table-cell/recovery-setting-row/div/div[1]/p
                        disposal_rows = driver.find_elements(By.XPATH, "//*[@id='sc-content-container']/section/app-root/recovery-rsu-automated-unfulfillable/div[1]/div[2]/recovery-rsu-return-or-dispose/div/kat-table/kat-table-body/kat-table-row")
                        for i, row in enumerate(disposal_rows, 1):
                            try:
                                cell_text = row.find_element(By.XPATH, f"//*[@id='sc-content-container']/section/app-root/recovery-rsu-automated-unfulfillable/div[1]/div[2]/recovery-rsu-return-or-dispose/div/kat-table/kat-table-body/kat-table-row[{i}]/kat-table-cell/recovery-setting-row/div/div[1]").text
                                if "退还或弃置" in cell_text:
                                    # 选择弃置选项 //*[@id="sc-content-container"]/section/app-root/recovery-rsu-automated-unfulfillable/div[1]/div[2]/recovery-rsu-return-or-dispose/div/kat-table/kat-table-body/kat-table-row[2]/kat-table-cell/recovery-setting-row/div/div[2]/recovery-radiobutton-group/kat-radiobutton-group/div/kat-radiobutton[2]
                                    dispose_radio = driver.find_element(By.XPATH, f"//*[@id='sc-content-container']/section/app-root/recovery-rsu-automated-unfulfillable/div[1]/div[2]/recovery-rsu-return-or-dispose/div/kat-table/kat-table-body/kat-table-row[{i}]/kat-table-cell/recovery-setting-row/div/div[2]/recovery-radiobutton-group/kat-radiobutton-group/div/kat-radiobutton[2]/input[@type='radio']")
                                    if self.safe_click(driver, dispose_radio, f"{country}弃置选项"):
                                        log_success(f"{country}: 已选择弃置选项")
                                    break
                            except:
                                continue
                    except:
                        log_error(f"{country}: 设置弃置选项失败")

                    # 点击更新按钮
                    try:
                        update_button = WebDriverWait(driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, "//*[@id='sc-content-container']/section/app-root/recovery-rsu-automated-unfulfillable/div[2]/div/kat-button[2]"))
                        )
                        if self.safe_click(driver, update_button, f"{country}不可售商品设置更新按钮"):
                                                    log_success(f"{country}: 不可售商品设置更新完成")
                        time.sleep(3)  # 等待页面更新

                        # 验证不可售商品设置更新是否成功
                        self.verify_unfulfillable_settings_update(driver, country)

                        self.handle_edit_page_popup(driver, country)
                    except:
                        log_error(f"{country}: 点击不可售商品设置更新按钮失败")
            else:
                log_info(f"{country}: 不可售商品自动设置已符合要求")

        except Exception as e:
            log_error(f"{country}: 处理不可售商品自动设置时发生错误: {str(e)}")

    def handle_barcode_preferences(self, driver, country, store_info=None):
        """处理亚马逊物流商品条形码首选项"""
        try:
            log_step(f"{country}: 检查亚马逊物流商品条形码首选项")

            # 检查当前设置
            try:
                # 等待元素加载
                barcode_status = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, "//*[@id='root']/div/div/div[8]/kat-table/kat-table-body/kat-table-row/kat-table-cell[2]"))
                )

                current_status = barcode_status.text
                log_info(f"{country}: 当前条形码首选项状态: {current_status}")

                if "亚马逊条形码" not in current_status:
                    log_step(f"{country}: 条形码首选项不是亚马逊条形码，需要编辑")

                    # 点击编辑按钮，增加重试机制
                    edit_success = False
                    for attempt in range(3):
                        try:
                            log_step(f"{country}: 尝试点击条形码首选项编辑按钮 (第{attempt+1}次)")
                            # //*[@id="root"]/div/div/div[8]/kat-table/kat-table-head/kat-table-row/kat-table-cell[2]/span/a/kat-button//button
                            edit_button = WebDriverWait(driver, 15).until(
                                EC.element_to_be_clickable((By.XPATH, "//*[@id='root']/div/div/div[8]/kat-table/kat-table-head/kat-table-row/kat-table-cell[2]/span/a/kat-button"))
                            )

                            # 滚动到编辑按钮
                            driver.execute_script("arguments[0].scrollIntoView(true);", edit_button)
                            time.sleep(1)

                            if self.safe_click(driver, edit_button, f"{country}条形码首选项编辑按钮"):
                                log_success(f"{country}: 条形码首选项编辑按钮点击成功")
                                edit_success = True
                                time.sleep(5)  # 增加等待时间
                                break
                            else:
                                log_error(f"{country}: 条形码首选项编辑按钮点击失败 (第{attempt+1}次)")
                                time.sleep(2)
                        except Exception as e:
                            log_error(f"{country}: 点击条形码首选项编辑按钮异常 (第{attempt+1}次): {str(e)}")
                            time.sleep(2)

                    if edit_success:
                        # 检查编辑页面是否加载
                        try:
                            # 等待编辑页面的关键元素
                            WebDriverWait(driver, 10).until(
                                EC.presence_of_element_located((By.XPATH, "//*[@id='root']/div/div/kat-table/kat-table-body"))
                            )
                            log_success(f"{country}: 条形码首选项编辑页面加载成功")

                            # 选择亚马逊条形码，增加重试机制
                            radio_success = False
                            for attempt in range(3):
                                try:
                                    amazon_barcode_radio = WebDriverWait(driver, 10).until(
                                        EC.element_to_be_clickable((By.XPATH, '//*[@id="root"]/div/div/kat-table/kat-table-body/kat-table-row/kat-table-cell[2]/kat-radiobutton-group/div/kat-radiobutton[2]/input[@type="radio"]'))
                                    )

                                    # 滚动到单选框
                                    driver.execute_script("arguments[0].scrollIntoView(true);", amazon_barcode_radio)
                                    time.sleep(1)

                                    if self.safe_click(driver, amazon_barcode_radio, f"{country}亚马逊条形码选项"):
                                        log_success(f"{country}: 已选择亚马逊条形码")
                                        radio_success = True
                                        break
                                    else:
                                        log_error(f"{country}: 选择亚马逊条形码失败 (第{attempt+1}次)")
                                        time.sleep(2)
                                except Exception as e:
                                    log_error(f"{country}: 选择亚马逊条形码异常 (第{attempt+1}次): {str(e)}")
                                    time.sleep(2)

                            if radio_success:
                                # 点击更新按钮
                                update_success = False
                                for attempt in range(3):
                                    try:
                                        update_button = WebDriverWait(driver, 10).until(
                                            EC.element_to_be_clickable((By.XPATH, "//*[@id='root']/div/div/div/a[1]/kat-button"))
                                        )
                                        if self.safe_click(driver, update_button, f"{country}条形码首选项更新按钮"):
                                            log_success(f"{country}: 条形码首选项更新完成")
                                            update_success = True
                                            time.sleep(5)

                                            # 验证条形码首选项更新是否成功
                                            self.verify_barcode_preferences_update(driver, country)

                                            # 处理编辑页面弹窗
                                            self.handle_edit_page_popup(driver, country)
                                            break
                                        else:
                                            log_error(f"{country}: 条形码首选项更新按钮点击失败 (第{attempt+1}次)")
                                            time.sleep(2)
                                    except Exception as e:
                                        log_error(f"{country}: 点击条形码首选项更新按钮异常 (第{attempt+1}次): {str(e)}")
                                        time.sleep(2)

                                if not update_success:
                                    log_error(f"{country}: 条形码首选项更新失败，已尝试3次")
                            else:
                                log_error(f"{country}: 选择亚马逊条形码失败，已尝试3次")

                        except Exception as e:
                            log_error(f"{country}: 条形码首选项编辑页面加载失败: {str(e)}")
                    else:
                        log_error(f"{country}: 条形码首选项编辑按钮点击失败，已尝试3次")
                else:
                    log_info(f"{country}: 条形码首选项已经是亚马逊条形码")

            except Exception as e:
                log_error(f"{country}: 检查条形码首选项状态失败: {str(e)}")

        except Exception as e:
            log_error(f"{country}: 处理条形码首选项时发生错误: {str(e)}")
            log_error(f"{country}: 条形码首选项详细错误信息: {traceback.format_exc()}")

    @crash_recovery_decorator
    def save_shipping_settings_with_validation(self, driver, country, store_info):
        """
        保存配送设置并验证页面重新加载
        如果保存失败则重试一次，增加容错性
        """
        max_attempts = 2  # 最多尝试2次

        for attempt in range(max_attempts):
            try:
                log_step(f"{country}: 保存配送设置 (第{attempt+1}次尝试)")
                
                # 增加冷却期，让页面完全稳定
                log_info(f"{country}: 操作完成后等待页面稳定...")
                time.sleep(5)  # 等待5秒让页面完全稳定
                
                # 简化的页面状态检查，只检查关键元素
                try:
                    log_info(f"{country}: 开始简化页面状态检查...")
                    
                    # 检查WebDriver是否还活着
                    try:
                        driver.execute_script("return document.readyState;")
                    except Exception as e:
                        if "tab crashed" in str(e).lower():
                            log_error(f"{country}: 检测到标签页崩溃，跳过当前国家")
                            return False
                        else:
                            log_warning(f"{country}: WebDriver检查失败: {str(e)}")
                    
                    # 只检查保存按钮是否存在
                    save_button = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, "//button[contains(text(), '保存')]"))
                    )
                    log_success(f"{country}: 页面状态检查通过，保存按钮可用")
                    
                except Exception as e:
                    if "tab crashed" in str(e).lower():
                        log_error(f"{country}: 检测到标签页崩溃，跳过当前国家")
                        return False
                    else:
                        log_error(f"{country}: 页面状态检查失败: {str(e)}")
                        if attempt < max_attempts - 1:
                            continue
                        else:
                            return False
                
                # 内存检查
                self.operation_count += 1
                if self.operation_count % self.memory_check_interval == 0:
                    if not self.check_memory_usage(country):
                        log_warning(f"{country}: 内存使用率过高，进行内存优化")
                        self.memory_optimization(driver, country)

                
                # 增加操作间隔，避免频繁操作导致崩溃
                self.throttle_manager._ensure_operation_interval(country)
                log_info(f"{country}: 操作间隔控制完成，准备执行保存操作")

                # 记录保存前的URL
                before_save_url = driver.current_url
                log_info(f"{country}: 保存前URL: {before_save_url}")

                # 智能处理保存按钮（检查状态并决定是否需要点击）
                save_result = self.handle_save_button_intelligently(driver, country, attempt + 1)
                if save_result == "retry":
                    if attempt == max_attempts - 1:  # 最后一次尝试
                        return False
                    continue
                elif save_result == "no_changes":
                    log_success(f"{country}: 没有更改需要保存，操作完成 (第{attempt+1}次尝试)")
                    return True
                elif save_result == "clicked":
                    log_success(f"{country}: 保存按钮点击成功 (第{attempt+1}次尝试)")
                    # 继续执行后续验证逻辑
                elif save_result == "failed":
                    if attempt == max_attempts - 1:  # 最后一次尝试
                        return False
                    continue

                # 保存按钮点击后检查弹窗
                self.handle_edit_page_popup(driver, country)

                # 等待页面加载完成
                loading_success = self.wait_for_page_loading_complete(driver, country, 10)

                if loading_success:
                    log_success(f"{country}: 配送设置保存操作完成，页面已重新加载 (第{attempt+1}次尝试)")

                    # 验证保存是否成功 - 检查编辑模板按钮是否存在
                    if self.verify_shipping_settings_save_success(driver, country):
                        log_success(f"{country}: 配送设置保存验证成功")
                        return True
                    else:
                        log_error(f"{country}: 配送设置保存验证失败，未找到编辑模板按钮")
                        if attempt < max_attempts - 1:  # 不是最后一次尝试
                            log_step(f"{country}: 准备重试保存操作...")
                            time.sleep(3)  # 等待一下再重试
                            continue
                        else:
                            # 最后一次尝试失败，需要记录到调用层的store_info
                            store_name = store_info.get('browserName', '未知店铺')
                            self.record_country_failure(store_name, country, "配送设置保存验证失败，未找到编辑模板按钮", "save")
                            return False
                else:
                    log_info(f"{country}: 保存操作等待超时 (第{attempt+1}次尝试)")
                    if attempt < max_attempts - 1:  # 不是最后一次尝试
                        log_step(f"{country}: 准备重试保存操作...")
                        time.sleep(3)  # 等待一下再重试
                        continue
                    else:
                        log_info(f"{country}: 保存操作已达到最大重试次数，但继续执行下一步")
                        return True  # 最后一次即使超时也继续执行

            except Exception as e:
                error_msg = str(e)
                log_error(f"{country}: 保存配送设置时发生错误 (第{attempt+1}次尝试): {error_msg}")
                
                # 检查是否是tab crashed错误
                if "tab crashed" in error_msg.lower():
                    log_error(f"{country}: 检测到标签页崩溃，记录崩溃信息并终止操作")
                    log_error(f"{country}: 崩溃发生时机: 保存配送设置操作")
                    log_error(f"{country}: 崩溃前操作: 设置运费和单位为商品完成，准备保存")
                    log_error(f"{country}: 崩溃原因: 页面过载或DOM操作冲突")
                    log_error(f"{country}: 建议: 增加操作间隔或优化页面交互逻辑")
                    return False  # 直接终止，不进行重启重试
                
                if attempt < max_attempts - 1:  # 不是最后一次尝试
                    log_step(f"{country}: 发生错误，准备重试保存操作...")
                    time.sleep(3)  # 等待一下再重试
                    continue
                else:
                    return False

        return False  # 如果所有尝试都失败

    def verify_shipping_settings_save_success(self, driver, country):
        """
        验证配送设置保存是否成功
        验证依据：是否能找到编辑模板按钮
        """
        try:
            log_step(f"{country}: 验证配送设置保存是否成功")

            # 等待页面稳定
            time.sleep(3)

            # 检查是否能找到编辑模板按钮
            try:
                edit_template_button = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, "//*[@id='template_actions_split_dropdown']/span[1]/span/button"))
                )

                if edit_template_button.is_displayed():
                    log_success(f"{country}: 找到编辑模板按钮，配送设置保存成功")
                    return True
                else:
                    log_error(f"{country}: 编辑模板按钮存在但不可见，配送设置保存可能失败")
                    return False

            except TimeoutException:
                log_error(f"{country}: 未找到编辑模板按钮，配送设置保存失败")
                return False

        except Exception as e:
            log_error(f"{country}: 验证配送设置保存成功性时发生错误: {str(e)}")
            return False

    def handle_save_button_intelligently(self, driver, country, attempt_number):
        """
        智能处理保存按钮 - 检查按钮状态并决定适当的操作

        Returns:
            "clicked": 按钮已成功点击
            "no_changes": 按钮被禁用（没有更改需要保存）
            "retry": 需要重试
            "failed": 失败
        """
        try:
            log_step(f"{country}: 智能检查保存按钮状态 (第{attempt_number}次尝试)")
            
            # 增加操作间隔，避免频繁操作导致崩溃
            self.throttle_manager._ensure_operation_interval(country)
            log_info(f"{country}: 保存按钮操作间隔控制完成")

            # 尝试多种可能的保存按钮定位策略
            save_button_selectors = [
                "//*[@id='submitButton-announce']",  # 用户提供的主要XPath
                "//*[@id='submitButton']",            # 用户提供的备用XPath
                "//button[contains(text(), '保存')]",
                "//button[contains(text(), 'Save')]",
                "//input[@type='submit' and contains(@value, '保存')]",
                "//span[@id='submitButton']//button"
            ]

            save_button = None
            used_selector = None

            # 尝试找到保存按钮
            for selector in save_button_selectors:
                try:
                    log_info(f"{country}: 尝试定位器: {selector}")
                    # 使用element_to_be_clickable等待条件，确保按钮真正可点击
                    save_button = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    if save_button:
                        used_selector = selector
                        log_info(f"{country}: 找到可点击的保存按钮，使用定位器: {selector}")
                        break
                    else:
                        log_info(f"{country}: 定位器未找到可点击按钮: {selector}")
                        continue
                except TimeoutException:
                    log_info(f"{country}: 定位器超时: {selector}")
                    continue
                except Exception as e:
                    log_warning(f"{country}: 定位器出错 {selector}: {str(e)}")
                    continue

            if not save_button:
                log_error(f"{country}: 所有定位策略都未找到保存按钮")
                return "failed"

            # 滚动到保存按钮位置
            try:
                driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", save_button)
                time.sleep(1)  # 等待滚动完成
                log_info(f"{country}: 已滚动到保存按钮位置")
            except Exception as e:
                log_warning(f"{country}: 滚动到保存按钮失败: {str(e)}")

            # 检查按钮状态
            try:
                is_enabled = save_button.is_enabled()
                is_displayed = save_button.is_displayed()
                button_text = save_button.text.strip()

                # 检查按钮的class属性
                button_class = save_button.get_attribute("class")
                button_disabled = save_button.get_attribute("disabled")

                log_info(f"{country}: 按钮状态详情:")
                log_info(f"  - 文本: '{button_text}'")
                log_info(f"  - 启用状态: {is_enabled}")
                log_info(f"  - 显示状态: {is_displayed}")
                log_info(f"  - Class属性: {button_class}")
                log_info(f"  - Disabled属性: {button_disabled}")
                log_info(f"  - 使用的定位器: {used_selector}")

                # 检查外层容器的状态（针对您提供的HTML结构）
                try:
                    parent_span = save_button.find_element(By.XPATH, "..")
                    parent_class = parent_span.get_attribute("class")
                    log_info(f"  - 父元素Class: {parent_class}")

                    # 检查是否包含disabled相关的class
                    if parent_class and "disabled" in parent_class.lower():
                        log_info(f"{country}: 按钮父元素包含disabled class，按钮被禁用")
                        is_enabled = False
                except:
                    pass

            except Exception as e:
                log_error(f"{country}: 获取按钮状态失败: {str(e)}")
                return "retry"

            # 根据按钮状态决定操作
            if not is_displayed:
                log_error(f"{country}: 保存按钮不可见")
                return "retry"

            if not is_enabled or (button_disabled is not None) or (button_class and "disabled" in button_class.lower()):
                log_info(f"{country}: 保存按钮被禁用，说明没有更改需要保存")
                log_success(f"{country}: 页面状态正常，无需保存操作")
                return "no_changes"

            # 按钮可用，尝试点击
            log_step(f"{country}: 保存按钮可用，尝试点击...")

            # 多种点击策略 - 按优先级尝试
            click_success = False
            click_methods = []

            # 策略1: WebDriver原生点击（最稳定）
            try:
                log_info(f"{country}: 尝试WebDriver原生点击")
                
                # 确保元素在视图中
                driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", save_button)
                time.sleep(1)
                
                # 检查元素是否真正可点击
                if not save_button.is_enabled():
                    log_warning(f"{country}: 保存按钮被禁用，无法点击")
                    return "no_changes"
                
                # 记录点击前的URL
                before_click_url = driver.current_url
                log_info(f"{country}: 点击前URL: {before_click_url}")
                
                # 执行点击
                save_button.click()
                
                # 等待页面响应
                time.sleep(3)
                
                # 检查页面是否有变化（URL变化、加载指示器等）
                after_click_url = driver.current_url
                url_changed = before_click_url != after_click_url
                
                # 检查是否有加载指示器
                loading_indicators = driver.find_elements(By.XPATH, "//*[contains(@class, 'loading') or contains(@class, 'spinner')]")
                has_loading = len(loading_indicators) > 0
                
                if url_changed or has_loading:
                    log_success(f"{country}: WebDriver原生点击成功，检测到页面变化")
                    click_success = True
                    click_methods.append("WebDriver原生点击")
                else:
                    log_warning(f"{country}: WebDriver原生点击可能未生效，页面无变化")
                    click_success = False
                    
            except Exception as e:
                log_warning(f"{country}: WebDriver原生点击失败: {str(e)}")
                click_success = False

            # 策略2: 节流点击（如果原生点击失败）
            if not click_success:
                try:
                    log_info(f"{country}: 尝试节流点击")
                    
                    # 记录点击前的URL
                    before_click_url = driver.current_url
                    log_info(f"{country}: 节流点击前URL: {before_click_url}")
                    
                    if self.throttle_manager.throttled_click(driver, save_button, "保存按钮", country):
                        # 等待页面响应
                        time.sleep(3)
                        
                        # 检查页面是否有变化
                        after_click_url = driver.current_url
                        url_changed = before_click_url != after_click_url
                        
                        # 检查是否有加载指示器
                        loading_indicators = driver.find_elements(By.XPATH, "//*[contains(@class, 'loading') or contains(@class, 'spinner')]")
                        has_loading = len(loading_indicators) > 0
                        
                        if url_changed or has_loading:
                            log_success(f"{country}: 节流点击成功，检测到页面变化")
                            click_success = True
                            click_methods.append("节流点击")
                        else:
                            log_warning(f"{country}: 节流点击可能未生效，页面无变化")
                            click_success = False
                    else:
                        log_warning(f"{country}: 节流点击失败")
                        click_success = False
                except Exception as e:
                    log_warning(f"{country}: 节流点击异常: {str(e)}")
                    click_success = False

            # 策略3: JavaScript点击（如果前面都失败）
            if not click_success:
                try:
                    log_info(f"{country}: 尝试JavaScript点击")
                    
                    # 记录点击前的URL
                    before_click_url = driver.current_url
                    log_info(f"{country}: JavaScript点击前URL: {before_click_url}")
                    
                    driver.execute_script("arguments[0].click();", save_button)
                    
                    # 等待页面响应
                    time.sleep(3)
                    
                    # 检查页面是否有变化
                    after_click_url = driver.current_url
                    url_changed = before_click_url != after_click_url
                    
                    # 检查是否有加载指示器
                    loading_indicators = driver.find_elements(By.XPATH, "//*[contains(@class, 'loading') or contains(@class, 'spinner')]")
                    has_loading = len(loading_indicators) > 0
                    
                    if url_changed or has_loading:
                        log_success(f"{country}: JavaScript点击成功，检测到页面变化")
                        click_success = True
                        click_methods.append("JavaScript点击")
                    else:
                        log_warning(f"{country}: JavaScript点击可能未生效，页面无变化")
                        click_success = False
                        
                except Exception as e:
                    log_warning(f"{country}: JavaScript点击失败: {str(e)}")
                    click_success = False

            # 策略4: 动作链点击（如果前面都失败）
            if not click_success:
                try:
                    log_info(f"{country}: 尝试动作链点击")
                    
                    # 记录点击前的URL
                    before_click_url = driver.current_url
                    log_info(f"{country}: 动作链点击前URL: {before_click_url}")
                    
                    from selenium.webdriver.common.action_chains import ActionChains
                    actions = ActionChains(driver)
                    actions.move_to_element(save_button).click().perform()
                    
                    # 等待页面响应
                    time.sleep(3)
                    
                    # 检查页面是否有变化
                    after_click_url = driver.current_url
                    url_changed = before_click_url != after_click_url
                    
                    # 检查是否有加载指示器
                    loading_indicators = driver.find_elements(By.XPATH, "//*[contains(@class, 'loading') or contains(@class, 'spinner')]")
                    has_loading = len(loading_indicators) > 0
                    
                    if url_changed or has_loading:
                        log_success(f"{country}: 动作链点击成功，检测到页面变化")
                        click_success = True
                        click_methods.append("动作链点击")
                    else:
                        log_warning(f"{country}: 动作链点击可能未生效，页面无变化")
                        click_success = False
                        
                except Exception as e:
                    log_warning(f"{country}: 动作链点击失败: {str(e)}")
                    click_success = False

            # 策略5: 强制触发事件（最后尝试）
            if not click_success:
                try:
                    log_info(f"{country}: 尝试强制触发事件")
                    
                    # 记录点击前的URL
                    before_click_url = driver.current_url
                    log_info(f"{country}: 强制触发事件前URL: {before_click_url}")
                    
                    driver.execute_script("""
                        var element = arguments[0];
                        var event = new MouseEvent('click', {
                            'view': window,
                            'bubbles': true,
                            'cancelable': true
                        });
                        element.dispatchEvent(event);
                    """, save_button)
                    
                    # 等待页面响应
                    time.sleep(3)
                    
                    # 检查页面是否有变化
                    after_click_url = driver.current_url
                    url_changed = before_click_url != after_click_url
                    
                    # 检查是否有加载指示器
                    loading_indicators = driver.find_elements(By.XPATH, "//*[contains(@class, 'loading') or contains(@class, 'spinner')]")
                    has_loading = len(loading_indicators) > 0
                    
                    if url_changed or has_loading:
                        log_success(f"{country}: 强制触发事件成功，检测到页面变化")
                        click_success = True
                        click_methods.append("强制触发事件")
                    else:
                        log_warning(f"{country}: 强制触发事件可能未生效，页面无变化")
                        click_success = False
                        
                except Exception as e:
                    log_warning(f"{country}: 强制触发事件失败: {str(e)}")
                    click_success = False

            # 策略6: Enter键触发（如果前面都失败）
            if not click_success:
                try:
                    log_info(f"{country}: 尝试Enter键触发")
                    
                    # 记录点击前的URL
                    before_click_url = driver.current_url
                    log_info(f"{country}: Enter键触发前URL: {before_click_url}")
                    
                    # 先聚焦到按钮
                    driver.execute_script("arguments[0].focus();", save_button)
                    time.sleep(0.5)
                    # 发送Enter键
                    from selenium.webdriver.common.keys import Keys
                    save_button.send_keys(Keys.ENTER)
                    
                    # 等待页面响应
                    time.sleep(3)
                    
                    # 检查页面是否有变化
                    after_click_url = driver.current_url
                    url_changed = before_click_url != after_click_url
                    
                    # 检查是否有加载指示器
                    loading_indicators = driver.find_elements(By.XPATH, "//*[contains(@class, 'loading') or contains(@class, 'spinner')]")
                    has_loading = len(loading_indicators) > 0
                    
                    if url_changed or has_loading:
                        log_success(f"{country}: Enter键触发成功，检测到页面变化")
                        click_success = True
                        click_methods.append("Enter键触发")
                    else:
                        log_warning(f"{country}: Enter键触发可能未生效，页面无变化")
                        click_success = False
                        
                except Exception as e:
                    log_warning(f"{country}: Enter键触发失败: {str(e)}")
                    click_success = False

            if click_success:
                # 记录成功的点击方法
                log_success(f"{country}: 保存按钮点击成功，使用的方法: {', '.join(click_methods)}")
                
                # 验证点击是否真正生效
                time.sleep(2)  # 给点击一些反应时间
                
                # 检查页面是否有变化（URL变化、元素消失等）
                try:
                    # 检查按钮是否变为禁用状态（表示点击生效）
                    if not save_button.is_enabled():
                        log_success(f"{country}: 保存按钮已变为禁用状态，点击验证成功")
                    else:
                        log_info(f"{country}: 保存按钮仍为启用状态，继续监控页面变化")
                        
                    # 检查是否有加载指示器出现
                    loading_indicators = driver.find_elements(By.XPATH, "//*[contains(@class, 'loading') or contains(@class, 'spinner')]")
                    if loading_indicators:
                        log_success(f"{country}: 检测到页面加载指示器，点击验证成功")
                    
                except Exception as verify_e:
                    log_warning(f"{country}: 点击验证检查失败: {str(verify_e)}")
                
                return "clicked"
            else:
                log_error(f"{country}: 所有6种点击策略都失败了")
                log_error(f"{country}: 尝试的方法: WebDriver原生点击, 节流点击, JavaScript点击, 动作链点击, 强制触发事件, Enter键触发")
                return "failed"

        except Exception as e:
            log_error(f"{country}: 智能保存按钮处理异常: {str(e)}")
            return "retry"



    def set_fee_input(self, driver, table_xpath, row_index, input_index, value, log_prefix):
        """
        设置运费输入框，处理stale element reference问题
        按照用户说明：点击输入框、按删除、输入数字
        """
        try:
            # 获取table_xpath/tr[{row_index}]下所有td 找到class包含shippingFee的那一列
            tr_elements = driver.find_elements(By.XPATH, f"{table_xpath}/tr[{row_index}]")
            #如果是多个tr则日志报错，并返回
            if len(tr_elements) > 1:
                log_error(f"{log_prefix}找到多个tr元素，无法确定目标行")
                return False
            tr_element = tr_elements[0]
            td_elements = tr_element.find_elements(By.XPATH, "./td")
            shipping_fee_td = None
            for td in td_elements:
                if "shippingFee" in td.get_attribute("class"):
                    shipping_fee_td = td
                    break
            if not shipping_fee_td:
                log_error(f"{log_prefix}未找到运费列")
                return False

            # 直接在shipping_fee_td下查找输入框
            for attempt in range(3):
                try:
                    if input_index == 1:
                        input_element = shipping_fee_td.find_element(By.XPATH, "./div[1]/div/div/input")
                    else:
                        input_element = shipping_fee_td.find_element(By.XPATH, "./div[2]/div[2]/div/input")

                    # 滚动到元素
                    driver.execute_script("arguments[0].scrollIntoView();", input_element)
                    time.sleep(0.5)

                    # 点击输入框（聚焦）
                    input_element.click()
                    time.sleep(0.5)

                    # 按删除键清空内容（模拟手动操作）
                    from selenium.webdriver.common.keys import Keys
                    input_element.send_keys(Keys.CONTROL + "a")  # 全选
                    time.sleep(0.2)
                    input_element.send_keys(Keys.DELETE)  # 删除
                    time.sleep(0.2)

                    # 输入新值
                    input_element.send_keys(value)
                    time.sleep(0.5)

                    # 验证输入是否成功
                    input_value = input_element.get_attribute("value")
                    if input_value == value:
                        log_success(f"{log_prefix}已设置为{value}")
                        return True
                    else:
                        log_info(f"{log_prefix}设置后值为{input_value}，期望值为{value}，尝试重新设置")
                        continue
                except Exception as e:
                    if "stale element reference" in str(e).lower() and attempt < 2:
                        log_info(f"{log_prefix}遇到stale element，第{attempt+1}次重试")
                        time.sleep(1)
                        continue
                    else:
                        log_error(f"{log_prefix}设置失败 (第{attempt+1}次尝试): {str(e)}")
                        if attempt == 2:  # 最后一次尝试
                            return False
                        continue
            return False
        except Exception as e:
            log_error(f"{log_prefix}设置过程中发生错误: {str(e)}")
            return False



    def execute_main_task(self, driver, upload_file_path, store_info):
        """
        执行主要的RPA任务（模块化版本）：
        根据用户选择的模块配置执行对应任务
        """
        try:
            log_info("开始执行店铺设置主任务（模块化）")

            # 首先检查是否能找到设置按钮
            if not self._check_settings_button_exists(driver):
                log_warning("未找到设置按钮，可能处于未知页面")
                return False  # 返回False触发兜底机制

            enabled_modules = self.get_enabled_modules()
            log_info(f"已启用的模块: {', '.join([m['name'] for m in enabled_modules.values()])}")

            # 检查是否启用了退货设置模块
            if not self.is_module_enabled("return_settings"):
                log_info("退货设置模块未启用，跳过主任务")
                return True

            log_step("=== 开始执行退货设置模块 ===")
            return self.execute_return_settings_module(driver, store_info)

        except Exception as e:
            self.log_setting_exception(store_info, "通用", "退货设置", f"执行主要任务时发生错误: {str(e)}")
            return False

    def _check_settings_button_exists(self, driver):
        """检查设置按钮是否存在"""
        try:
            log_step("检查设置按钮是否存在...")

            # 使用多种策略查找设置按钮
            settings_strategies = [
                # 主要的设置按钮定位策略
                (By.XPATH, "//*[@id='navbar']/div[3]/div/div[2]/div/div[1]/div"),
                # 备用策略
                (By.XPATH, "//span[contains(text(), '设置') or contains(text(), 'Settings') or contains(text(), '設定')]"),
                (By.XPATH, "//a[contains(@href, 'settings')]"),
                (By.XPATH, "//button[contains(text(), '设置') or contains(text(), 'Settings')]"),
                (By.ID, "settings-link"),
                (By.CLASS_NAME, "settings-button")
            ]

            for i, (by, locator) in enumerate(settings_strategies, 1):
                try:
                    element = WebDriverWait(driver, 3).until(
                        EC.presence_of_element_located((by, locator))
                    )
                    if element:
                        log_info(f"✅ 找到设置按钮 (策略{i}): {by} = {locator}")
                        return True
                except:
                    log_info(f"策略{i}未找到设置按钮: {by} = {locator}")
                    continue

            log_warning("❌ 所有策略都未找到设置按钮")
            return False

        except Exception as e:
            log_error(f"检查设置按钮时发生错误: {str(e)}")
            return False

    def execute_return_settings_module(self, driver, store_info):
        """执行退货设置模块的具体逻辑"""
        try:
            log_step("退货设置模块：开始执行")

            # 等待页面完全加载
            time.sleep(5)

            # 步骤1: 点击设置按钮（点击两次防止弹窗干扰）
            log_step("步骤1: 点击设置按钮")
            try:
                settings_button = WebDriverWait(driver, 15).until(
                    EC.element_to_be_clickable((By.XPATH, "//*[@id='navbar']/div[3]/div/div[2]/div/div[1]/div"))
                )

                # 记录当前URL用于调试
                log_info(f"点击前URL: {driver.current_url}")

                # 第一次点击（可能消除弹窗或引导）
                log_step("第一次点击设置按钮（防止弹窗干扰）")
                if self.safe_click(driver, settings_button, "设置按钮(第1次)"):
                    time.sleep(2)  # 等待可能的弹窗处理
                    log_info(f"第一次点击后URL: {driver.current_url}")

                # 重新获取设置按钮元素，防止stale element
                settings_button = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//*[@id='navbar']/div[3]/div/div[2]/div/div[1]/div"))
                )

                # 第二次点击（真正的有效点击）
                log_step("第二次点击设置按钮（实际操作）")
                if self.safe_click(driver, settings_button, "设置按钮(第2次)"):
                    time.sleep(3)  # 增加等待时间
                    log_info(f"第二次点击后URL: {driver.current_url}")
                    # 设置按钮点击后检查弹窗
                    self.handle_edit_page_popup(driver, "主任务")
                else:
                    self.log_setting_exception(store_info, "通用", "退货设置", "设置按钮第二次点击失败")
                    return False

            except TimeoutException:
                self.log_setting_exception(store_info, "通用", "退货设置", "无法找到设置按钮")
                return False

            # 步骤2: 点击退货设置
            log_step("步骤2: 点击退货设置")
            time.sleep(3)  # 增加等待设置菜单展开的时间

            try:
                # 先检查设置菜单是否已展开
                log_step("检查设置菜单是否展开...")
                menu_expanded = False
                for attempt in range(3):
                    try:
                        return_settings_link = driver.find_element(By.XPATH, "//*[@id='navbar']/div[3]/div/div[2]/div/div[2]/div[1]/div/div[1]/a[5]")
                        if return_settings_link.is_displayed():
                            menu_expanded = True
                            log_success("设置菜单已展开")
                            break
                    except NoSuchElementException:
                        pass

                    log_step(f"设置菜单未展开，尝试重新点击设置按钮 (第{attempt+1}次)")
                    settings_button = driver.find_element(By.XPATH, "//*[@id='navbar']/div[3]/div/div[2]/div/div[1]/div")
                    self.safe_click(driver, settings_button, "设置按钮")
                    time.sleep(2)

                if not menu_expanded:
                    self.log_setting_exception(store_info, "通用", "退货设置", "设置菜单无法展开")
                    return False

                # 点击退货设置
                return_settings_link = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//*[@id='navbar']/div[3]/div/div[2]/div/div[2]/div[1]/div/div[1]/a[5]"))
                )

                log_info(f"点击退货设置前URL: {driver.current_url}")

                if self.safe_click(driver, return_settings_link, "退货设置链接"):
                    time.sleep(5)  # 增加等待页面跳转的时间
                    log_info(f"点击退货设置后URL: {driver.current_url}")
                    # 退货设置页面加载后检查弹窗
                    self.handle_edit_page_popup(driver, "主任务")
                else:
                    self.log_setting_exception(store_info, "通用", "退货设置", "退货设置链接点击失败")
                    return False

            except TimeoutException:
                self.log_setting_exception(store_info, "通用", "退货设置", "无法找到退货设置链接")
                return False

            # 等待退货设置页面完全加载
            log_step("等待退货设置页面加载...")
            time.sleep(5)

            # 检查是否成功跳转到退货设置页面
            current_url = driver.current_url
            if "returns" not in current_url.lower() and "setting" not in current_url.lower():
                self.log_setting_exception(store_info, "通用", "退货设置", f"未成功跳转到退货设置页面，当前URL: {current_url}")

            # 步骤3: 处理管理退货地址（在常规设置之前）
            log_step("步骤3: 处理管理退货地址")
            self.process_return_address_management(driver)

            # 步骤4: 确保在常规设置标签页（原来的步骤3）
            log_step("步骤4: 确保在常规设置标签页")
            try:
                general_settings_tab = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//*[@id='settings-tabs-wrapper']/kat-tab-pane/kat-tab-header[1]/div/span[1]/span"))
                )
                if self.safe_click(driver, general_settings_tab, "常规设置标签页"):
                    log_success("已切换到常规设置标签页")
                    time.sleep(3)
                    # 常规设置标签页切换后检查弹窗
                    self.handle_edit_page_popup(driver, "主任务")
                else:
                    self.log_setting_exception(store_info, "通用", "退货设置", "切换到常规设置标签页失败")
                    return False
            except TimeoutException:
                self.log_setting_exception(store_info, "通用", "退货设置", "未找到常规设置标签页")
                return False

            # 步骤5: 处理退货批准设置（原来的步骤4）
            self.process_return_approval_settings(driver)

            # 尝试保存设置
            self.save_settings_if_available(driver)

            log_success("店铺设置主任务执行完成")
            return True

        except Exception as e:
            self.log_setting_exception(store_info, "通用", "退货设置", f"执行主要任务时发生错误: {str(e)}")
            # 保存错误截图
            try:
                screenshot_path = f"error_screenshot_{int(time.time())}.png"
                driver.save_screenshot(screenshot_path)
                log_info(f"错误截图已保存: {screenshot_path}")
            except:
                pass
            return False

    def is_policy_compliant_option(self, text):
        """
        判断文本是否为符合政策要求的选项
        使用多种匹配方式来提高匹配成功率
        """
        if not text:
            return False

        text_lower = text.lower()

        # 中文匹配关键词
        chinese_keywords = [
            "符合政策要求",
            "符合政策",
            "政策要求",
            "符合要求",
            "政策符合",
            "满足政策要求",
            "满足政策",
            "政策规定",
            "符合规定"
        ]

        # 英文匹配关键词
        english_keywords = [
            "meets policy requirements",
            "policy compliant",
            "compliant",
            "meets requirements",
            "policy requirements",
            "requirement compliant",
            "meets policy",
            "policy approved",
            "approved"
        ]

        # 检查中文关键词
        for keyword in chinese_keywords:
            if keyword in text:
                log_info(f"文本匹配成功 (中文): '{text}' 包含 '{keyword}'")
                return True

        # 检查英文关键词
        for keyword in english_keywords:
            if keyword in text_lower:
                log_info(f"文本匹配成功 (英文): '{text}' 包含 '{keyword}'")
                return True

        return False

    def safe_click(self, driver, element, element_name):
        """
        安全点击元素的方法，尝试多种点击方式
        """
        try:
            # 方法1: 滚动到元素并使用原生点击
            log_step(f"尝试点击 {element_name}...")
            driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element)
            time.sleep(1)

            # 检查元素是否可见和可点击
            if not element.is_displayed():
                log_error(f"{element_name} 不可见")
                return False

            if not element.is_enabled():
                log_error(f"{element_name} 不可点击")
                return False

            # 尝试原生点击
            try:
                element.click()
                log_success(f"使用原生点击成功点击 {element_name}")
                return True
            except Exception as e1:
                log_step(f"原生点击失败: {str(e1)}，尝试JavaScript点击...")

                # 方法2: JavaScript点击
                try:
                    driver.execute_script("arguments[0].click();", element)
                    log_success(f"使用JavaScript成功点击 {element_name}")
                    return True
                except Exception as e2:
                    log_step(f"JavaScript点击失败: {str(e2)}，尝试坐标点击...")

                    # 方法3: 使用坐标点击
                    try:
                        ActionChains(driver).move_to_element(element).click().perform()
                        log_success(f"使用ActionChains成功点击 {element_name}")
                        return True
                    except Exception as e3:
                        log_error(f"所有点击方法都失败 {element_name}: {str(e3)}")
                        return False

        except Exception as e:
            log_error(f"点击 {element_name} 时发生意外错误: {str(e)}")
            return False

    def find_navbar_element_with_id_priority(self, driver, element_type, timeout=15):
        """
        使用ID优先策略查找导航栏元素

        :param driver: WebDriver实例
        :param element_type: 元素类型 ('settings_button', 'shipping_link', 'fba_link', 'return_link')
        :param timeout: 超时时间
        :return: 找到的元素或None
        """
        try:
            log_step(f"🔍 查找导航栏元素: {element_type}")

            # 定义ID优先的选择器策略
            selectors = {
                'settings_button': [
                    # ID选择器（优先）
                    ("ID", "navbar-settings-button"),
                    ("ID", "settings-menu-trigger"),
                    ("ID", "navbar-settings"),
                    # XPath选择器（备选）
                    ("XPATH", "//*[@id='navbar']/div[3]/div/div[2]/div/div[1]/div"),
                    ("XPATH", "//div[contains(@class, 'navbar')]//div[contains(@class, 'settings')]"),
                ],
                'shipping_link': [
                    # ID选择器（优先）
                    ("ID", "shipping-settings-link"),
                    ("ID", "navbar-shipping-link"),
                    # XPath选择器（备选）
                    ("XPATH", "//*[@id='navbar']/div[3]/div/div[2]/div/div[2]/div[1]/div/div[1]/a[7]"),
                    ("XPATH", "//a[contains(@href, 'shipping') or contains(text(), '配送设置')]"),
                ],
                'fba_link': [
                    # ID选择器（优先）
                    ("ID", "fba-settings-link"),
                    ("ID", "navbar-fba-link"),
                    # XPath选择器（备选）
                    ("XPATH", "//*[@id='navbar']/div[3]/div/div[2]/div/div[2]/div[1]/div/div[1]/a[11]"),
                    ("XPATH", "//a[contains(@href, 'fba') or contains(text(), '亚马逊物流')]"),
                ],
                'return_link': [
                    # ID选择器（优先）
                    ("ID", "return-settings-link"),
                    ("ID", "navbar-return-link"),
                    # XPath选择器（备选）
                    ("XPATH", "//*[@id='navbar']/div[3]/div/div[2]/div/div[2]/div[1]/div/div[1]/a[5]"),
                    ("XPATH", "//a[contains(@href, 'return') or contains(text(), '退货设置')]"),
                ]
            }

            if element_type not in selectors:
                log_error(f"❌ 未知的元素类型: {element_type}")
                return None

            # 尝试各种选择器策略
            for selector_type, selector in selectors[element_type]:
                try:
                    if selector_type == "ID":
                        log_info(f"🎯 尝试ID定位: {selector}")
                        element = WebDriverWait(driver, timeout // len(selectors[element_type])).until(
                            EC.element_to_be_clickable((By.ID, selector))
                        )
                        log_success(f"✅ 使用ID成功找到元素: {selector}")
                        return element
                    elif selector_type == "XPATH":
                        log_info(f"📍 尝试XPath定位: {selector}")
                        element = WebDriverWait(driver, timeout // len(selectors[element_type])).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                        log_success(f"✅ 使用XPath成功找到元素")
                        return element
                except TimeoutException:
                    log_warning(f"⚠️ {selector_type}定位超时: {selector}")
                    continue
                except Exception as e:
                    log_warning(f"⚠️ {selector_type}定位失败: {str(e)}")
                    continue

            log_error(f"❌ 所有定位策略都失败了: {element_type}")
            return None

        except Exception as e:
            log_error(f"❌ 查找导航栏元素时发生错误: {str(e)}")
            return None

    def process_return_approval_settings(self, driver):
        """
        处理退货批准设置列表
        遍历所有行，确保选择包含"符合政策要求"字样的选项
        """
        log_step("开始处理退货批准设置...")

        success_count = 0  # 添加成功计数器

        try:
            # 查找退货批准列表表格
            table_xpath = "//*[@id='settings-tabs-wrapper']/kat-tab[1]/div/kat-data-table/table/tbody"
            table = WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.XPATH, table_xpath))
            )

            if not table:
                raise Exception("无法找到退货设置表格")

            # 获取所有表格行
            rows = driver.find_elements(By.XPATH, f"{table_xpath}/tr")
            log_info(f"找到 {len(rows)} 行退货设置")

            for i, row in enumerate(rows, 1):
                try:
                    # 获取国家名称（用于日志）
                    try:
                        country_element = row.find_element(By.XPATH, "./td[1]//span[contains(@style, 'left: 10px')]")
                        country_name = country_element.text.strip()
                    except NoSuchElementException:
                        country_name = f"第{i}行"

                    log_step(f"处理 {country_name} 的退货批准设置")

                    # 退货批准下拉框（第2列）
                    approval_dropdown_xpath = f"{table_xpath}/tr[{i}]/td[2]//kat-dropdown/div/div[1]"

                    # 等待下拉框加载
                    time.sleep(1)
                    try:
                        approval_dropdown = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.XPATH, approval_dropdown_xpath))
                        )
                    except TimeoutException:
                        log_error(f"{country_name}: 无法找到退货批准下拉框")
                        continue

                    # 检查当前选项文本
                    current_text = approval_dropdown.text.strip()
                    log_info(f"{country_name}: 当前选项为: {current_text}")

                    # 检查是否已经是符合政策要求的选项
                    if "符合政策要求" in current_text:
                        log_success(f"{country_name}: 已经是符合政策要求选项，跳过")
                        continue

                    # 点击下拉框展开选项
                    log_step(f"{country_name}: 点击下拉框查看选项")
                    if not self.safe_click(driver, approval_dropdown, f"{country_name}的退货批准下拉框"):
                        log_error(f"{country_name}: 下拉框点击失败")
                        continue
                    time.sleep(3)  # 增加等待时间让选项完全加载

                    # 尝试多种方式查找选项容器
                    log_step(f"{country_name}: 查找下拉选项...")
                    options_found = False
                    target_option = None

                    # 可能的选项容器xpath列表
                    possible_containers = [
                        f"{table_xpath}/tr[{i}]/td[2]//kat-dropdown//div[@class='select-options']",
                        f"{table_xpath}/tr[{i}]/td[2]//kat-dropdown//div[contains(@class, 'options')]",
                        f"{table_xpath}/tr[{i}]/td[2]//kat-dropdown//ul",
                        f"{table_xpath}/tr[{i}]/td[2]//div[contains(@class, 'dropdown')]//div[contains(@class, 'option')]",
                        f"{table_xpath}/tr[{i}]/td[2]//kat-dropdown//div[@role='listbox']",
                        f"{table_xpath}/tr[{i}]/td[2]//kat-dropdown//div[@role='menu']"
                    ]

                    for container_xpath in possible_containers:
                        try:
                            log_step(f"{country_name}: 尝试选项容器: {container_xpath}")
                            options_container = WebDriverWait(driver, 3).until(
                                EC.presence_of_element_located((By.XPATH, container_xpath))
                            )

                            if options_container.is_displayed():
                                log_success(f"{country_name}: 找到选项容器")

                                # 尝试多种方式查找选项
                                option_selectors = [
                                    f"{container_xpath}//div[contains(@class, 'option')]",
                                    f"{container_xpath}//li",
                                    f"{container_xpath}//div[@role='option']",
                                    f"{container_xpath}//a",
                                    f"{container_xpath}//*[contains(text(), '')]"  # 任何包含文本的元素
                                ]

                                for option_selector in option_selectors:
                                    try:
                                        options = driver.find_elements(By.XPATH, option_selector)
                                        if options:
                                            log_info(f"{country_name}: 使用选择器 {option_selector} 找到 {len(options)} 个选项")

                                            # 列出所有选项
                                            for idx, option in enumerate(options):
                                                try:
                                                    option_text = option.text.strip()
                                                    if option_text:  # 只显示有文本的选项
                                                        log_info(f"{country_name}: 选项 {idx+1}: '{option_text}'")

                                                        # 使用更灵活的文本匹配
                                                        if self.is_policy_compliant_option(option_text):
                                                            target_option = option
                                                            log_success(f"{country_name}: 找到目标选项: '{option_text}'")
                                                            options_found = True
                                                            break
                                                except Exception as text_e:
                                                    log_step(f"{country_name}: 无法获取选项文本: {str(text_e)}")

                                            if options_found:
                                                break
                                    except Exception as selector_e:
                                        log_step(f"{country_name}: 选择器失败: {str(selector_e)}")
                                        continue

                                if options_found:
                                    break
                        except TimeoutException:
                            log_step(f"{country_name}: 容器未找到，尝试下一个")
                            continue
                        except Exception as container_e:
                            log_step(f"{country_name}: 容器查找错误: {str(container_e)}")
                            continue

                    if not options_found:
                        log_error(f"{country_name}: 未找到任何下拉选项，尝试直接查找所有可见元素")
                        # 最后的尝试：查找页面上所有包含"符合政策要求"的可见元素
                        try:
                            all_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '符合政策') or contains(text(), '政策要求') or contains(text(), 'policy')]")
                            for element in all_elements:
                                if element.is_displayed():
                                    element_text = element.text.strip()
                                    log_info(f"{country_name}: 页面上的相关元素: '{element_text}'")
                                    if self.is_policy_compliant_option(element_text):
                                        target_option = element
                                        options_found = True
                                        log_success(f"{country_name}: 在页面上找到目标选项: '{element_text}'")
                                        break
                        except Exception as e:
                            log_error(f"{country_name}: 搜索页面元素失败: {str(e)}")

                    # 如果找到了符合政策要求的选项，尝试选择它
                    if target_option:
                        log_step(f"{country_name}: 尝试选择选项 '{target_option.text.strip()}'")

                        # 尝试多种方式点击选项
                        option_selected = False

                        # 方法1: 滚动到选项并直接点击
                        try:
                            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", target_option)
                            time.sleep(0.5)
                            target_option.click()
                            log_info(f"{country_name}: 使用直接点击方式")
                            option_selected = True
                        except Exception as e:
                            log_info(f"{country_name}: 直接点击失败: {str(e)}")

                        # 方法2: JavaScript点击
                        if not option_selected:
                            try:
                                driver.execute_script("arguments[0].click();", target_option)
                                log_info(f"{country_name}: 使用JavaScript点击")
                                option_selected = True
                            except Exception as e:
                                log_info(f"{country_name}: JavaScript点击失败: {str(e)}")

                        # 方法3: ActionChains点击
                        if not option_selected:
                            try:
                                ActionChains(driver).move_to_element(target_option).click().perform()
                                log_info(f"{country_name}: 使用ActionChains点击")
                                option_selected = True
                            except Exception as e:
                                log_info(f"{country_name}: ActionChains点击失败: {str(e)}")

                        # 方法4: 模拟键盘选择
                        if not option_selected:
                            try:
                                target_option.send_keys("")  # 聚焦到元素
                                target_option.send_keys(Keys.ENTER)
                                log_info(f"{country_name}: 使用键盘Enter键选择")
                                option_selected = True
                            except Exception as e:
                                log_info(f"{country_name}: 键盘选择失败: {str(e)}")

                        # 方法5: 尝试点击选项的父元素或子元素
                        if not option_selected:
                            try:
                                # 尝试点击选项内的span或div子元素
                                clickable_child = target_option.find_element(By.XPATH, ".//span | .//div | .//a")
                                clickable_child.click()
                                log_info(f"{country_name}: 点击选项子元素")
                                option_selected = True
                            except:
                                try:
                                    # 尝试点击选项的父元素
                                    parent = target_option.find_element(By.XPATH, "..")
                                    parent.click()
                                    log_info(f"{country_name}: 点击选项父元素")
                                    option_selected = True
                                except Exception as e:
                                    log_info(f"{country_name}: 点击选项子/父元素失败: {str(e)}")

                        if option_selected:
                            time.sleep(2)  # 等待选择生效

                            # 点击下拉框外部区域关闭下拉框
                            try:
                                driver.execute_script("document.body.click();")
                                time.sleep(1)
                                log_info(f"{country_name}: 点击外部区域关闭下拉框")
                            except:
                                pass

                            # 验证选择是否生效
                            time.sleep(2)
                            try:
                                # 重新获取下拉框元素和当前选项
                                current_dropdown = WebDriverWait(driver, 5).until(
                                    EC.presence_of_element_located((By.XPATH, f"{table_xpath}/tr[{i}]/td[2]//kat-dropdown//div[1]"))
                                )
                                current_text_after = current_dropdown.text.strip()
                                log_info(f"{country_name}: 选择后的当前选项: '{current_text_after}'")

                                # 检查是否选择成功
                                if self.is_policy_compliant_option(current_text_after):
                                    log_success(f"{country_name}: 成功选择了符合政策要求的选项")
                                    success_count += 1
                                elif current_text_after != current_text:
                                    log_info(f"{country_name}: 选项已更改但可能不是预期选项")
                                    log_info(f"{country_name}: 从 '{current_text}' 更改为 '{current_text_after}'")
                                else:
                                    log_error(f"{country_name}: 选择似乎没有生效，选项未更改")

                            except Exception as e:
                                log_error(f"{country_name}: 验证选择结果时出错: {str(e)}")
                        else:
                            log_error(f"{country_name}: 所有点击方式都失败了")
                    else:
                        log_info(f"{country_name}: 未找到包含'符合政策要求'字样的选项")
                        # 如果找不到选项，尝试点击外部关闭下拉框
                        try:
                            driver.execute_script("document.body.click();")
                            time.sleep(1)
                        except:
                            pass

                except Exception as row_e:
                    log_error(f"处理第{i}行时发生错误: {str(row_e)}")
                    continue

            log_success(f"成功选择了 {success_count} 个符合政策要求的选项")

        except Exception as e:
            log_error(f"处理退货批准设置时发生错误: {str(e)}")
            raise

    def save_settings_if_available(self, driver):
        """
        尝试保存设置（如果保存按钮可用）
        单次执行，不重试
        """
        try:
            log_step("查找并尝试保存设置")

            save_button_selectors = [
                '//*[@id="settings-tabs-wrapper"]/kat-tab[1]/div/div/div[2]/kat-button',
                "//button[contains(text(), '保存设置')]",
                "//kat-button[contains(@label, '保存设置')]",
                "//*[@class='general-settings-page__submit-btn']//button"
            ]

            save_button = None
            used_selector = None
            for selector in save_button_selectors:
                try:
                    save_button = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    used_selector = selector
                    log_info(f"找到保存按钮: {selector}")
                    break
                except TimeoutException:
                    continue

            if save_button:
                # 检查按钮是否可用（不是disabled状态）
                if save_button.is_enabled():
                    if self.safe_click(driver, save_button, "保存设置按钮"):
                        log_success("设置已保存")
                        time.sleep(3)
                        # 保存按钮点击后检查弹窗
                        self.handle_edit_page_popup(driver, "主任务")
                        return True
                    else:
                        log_error("保存设置按钮点击失败")
                        return False
                else:
                    log_info("保存按钮不可用（可能没有更改需要保存）")
                    return True  # 没有需要保存的内容，也算成功
            else:
                log_info("未找到保存按钮")
                return True  # 没找到按钮也继续执行

        except Exception as e:
            log_error(f"保存设置时发生错误: {str(e)}")
            return False

    def process_return_address_management(self, driver):
        """
        处理管理退货地址标签页：
        1. 点击管理退货地址标签
        2. 检查默认退货地址列的所有行
        3. 如果发现"请选择您的默认退货地址"，则点击编辑图标
        4. 在弹窗中勾选radiobutton
        5. 点击"使用该地址"按钮
        """
        try:
            log_step("开始处理管理退货地址...")

            # 点击管理退货地址标签
            log_step("点击管理退货地址标签")
            manage_address_tab = WebDriverWait(driver, 15).until(
                EC.element_to_be_clickable((By.XPATH, "//*[@id='settings-tabs-wrapper']/kat-tab-pane/kat-tab-header[3]/div/span[1]/span"))
            )

            if self.safe_click(driver, manage_address_tab, "管理退货地址标签"):
                log_success("已点击管理退货地址标签")
                time.sleep(3)
            else:
                log_error("点击管理退货地址标签失败")
                return False

            # 等待表格加载
            time.sleep(3)

            # 检查默认退货地址列的所有行
            log_step("检查默认退货地址列表...")

            # 查找表格并获取所有行
            table_xpath = "//*[@id='settings-tabs-wrapper']/kat-tab[3]/div/div[2]/div[1]/div/kat-data-table/table/tbody"
            try:
                table = WebDriverWait(driver, 15).until(
                    EC.presence_of_element_located((By.XPATH, table_xpath))
                )

                # 获取所有行
                rows = table.find_elements(By.XPATH, "./tr")
                log_info(f"找到 {len(rows)} 行退货地址数据")

                if not rows:
                    log_info("表格中没有找到任何行数据")
                    return True

            except TimeoutException:
                log_error("未找到退货地址表格")
                return False

            processed_count = 0

            # 遍历所有行
            for i, row in enumerate(rows, 1):
                try:
                    log_step(f"检查第{i}行默认退货地址...")

                    # 构建第二列（默认退货地址列）的xpath
                    row_xpath = f"{table_xpath}/tr[{i}]/td[2]"

                    # 等待行元素加载
                    row_element = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, row_xpath))
                    )

                    # 获取行文本
                    row_text = row_element.text.strip()
                    log_info(f"第{i}行内容: '{row_text}'")

                    # 检查是否包含"请选择您的默认退货地址"
                    if "请选择您的默认退货地址" in row_text:
                        log_step(f"第{i}行需要设置默认退货地址，准备点击编辑图标...")

                        # 构建编辑图标的xpath
                        edit_icon_xpath = f"{table_xpath}/tr[{i}]/td[2]/div/div/kat-icon/i"

                        try:
                            # 查找并点击编辑图标
                            edit_icon = WebDriverWait(driver, 5).until(
                                EC.element_to_be_clickable((By.XPATH, edit_icon_xpath))
                            )

                            if self.safe_click(driver, edit_icon, f"第{i}行编辑图标"):
                                log_success(f"已点击第{i}行编辑图标")
                                time.sleep(2)

                                # 处理弹窗
                                if self.handle_address_selection_modal(driver, i):
                                    processed_count += 1
                                    # 处理完弹窗后等待页面更新
                                    time.sleep(2)
                                else:
                                    log_error(f"第{i}行地址选择弹窗处理失败")
                            else:
                                log_error(f"第{i}行编辑图标点击失败")

                        except TimeoutException:
                            log_error(f"第{i}行编辑图标未找到或不可点击")
                            # 尝试备用的编辑图标xpath
                            try:
                                backup_edit_xpath = f"{table_xpath}/tr[{i}]/td[2]//kat-icon[contains(@class, 'pencil')]"
                                backup_icon = driver.find_element(By.XPATH, backup_edit_xpath)
                                if self.safe_click(driver, backup_icon, f"第{i}行编辑图标(备用)"):
                                    log_success(f"使用备用xpath成功点击第{i}行编辑图标")
                                    time.sleep(2)
                                    if self.handle_address_selection_modal(driver, i):
                                        processed_count += 1
                                        time.sleep(2)
                            except:
                                log_error(f"第{i}行备用编辑图标也未找到")

                    else:
                        log_info(f"第{i}行已有默认地址，跳过处理")

                except Exception as row_e:
                    log_error(f"处理第{i}行时发生错误: {str(row_e)}")
                    continue

            log_success(f"管理退货地址处理完成，共处理了 {processed_count} 行")
            return True

        except Exception as e:
            log_error(f"处理管理退货地址时发生错误: {str(e)}")
            return False

    def handle_address_selection_modal(self, driver, row_number):
        """
        处理地址选择弹窗：
        1. 等待弹窗出现
        2. 勾选radiobutton
        3. 点击"使用该地址"按钮
        """
        try:
            log_step(f"第{row_number}行: 处理地址选择弹窗...")

            # 等待弹窗出现
            modal_container = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//*[@id='settings-tabs-wrapper']/kat-tab[3]/div/kat-modal"))
            )
            log_info(f"第{row_number}行: 弹窗已出现")
            time.sleep(2)

            # 查找并勾选radiobutton
            log_step(f"第{row_number}行: 查找radiobutton...")

            # 尝试多种可能的radiobutton selector
            radiobutton_selectors = [
                "//span[@class='kat-radiobutton-pos kat-radiobutton-icon']",
                "//kat-radiobutton//span[@class='kat-radiobutton-pos kat-radiobutton-icon']",
                "//input[@type='radio']",
                "//kat-radiobutton//input[@type='radio']"
            ]

            radiobutton_clicked = False

            for selector in radiobutton_selectors:
                try:
                    radiobutton_elements = driver.find_elements(By.XPATH, selector)
                    log_info(f"第{row_number}行: 使用选择器 '{selector}' 找到 {len(radiobutton_elements)} 个radiobutton")

                    if radiobutton_elements:
                        # 选择第一个可用的radiobutton
                        for radio in radiobutton_elements:
                            try:
                                if radio.is_displayed() and radio.is_enabled():
                                    if self.safe_click(driver, radio, f"第{row_number}行radiobutton"):
                                        log_success(f"第{row_number}行: 成功勾选radiobutton")
                                        radiobutton_clicked = True
                                        time.sleep(1)
                                        break
                            except Exception as radio_e:
                                log_info(f"第{row_number}行: radiobutton点击失败: {str(radio_e)}")
                                continue

                    if radiobutton_clicked:
                        break

                except Exception as selector_e:
                    log_info(f"第{row_number}行: 选择器 '{selector}' 失败: {str(selector_e)}")
                    continue

            if not radiobutton_clicked:
                log_error(f"第{row_number}行: 未能勾选任何radiobutton")
                return False

            # 查找并点击"使用该地址"按钮
            log_step(f"第{row_number}行: 查找使用该地址按钮...")

            # 尝试多种可能的按钮selector
            button_selectors = [
                            "//*[@id='settings-tabs-wrapper']/kat-tab[3]/div/kat-modal/kat-modal-content/kat-modal-footer/kat-button",
            "//kat-modal-footer//kat-button",
                "//kat-modal//button[contains(text(), '使用该地址')]",
                "//kat-modal//button[contains(text(), '使用')]",
                "//kat-modal//button[@type='button']",
                "//kat-modal-footer//button"
            ]

            button_clicked = False

            for selector in button_selectors:
                try:
                    button_elements = driver.find_elements(By.XPATH, selector)
                    log_info(f"第{row_number}行: 使用选择器 '{selector}' 找到 {len(button_elements)} 个按钮")

                    for button in button_elements:
                        try:
                            if button.is_displayed() and button.is_enabled():
                                button_text = button.text.strip()
                                log_info(f"第{row_number}行: 找到按钮文本: '{button_text}'")

                                # 检查按钮文本是否包含相关关键词
                                if any(keyword in button_text for keyword in ["使用该地址", "使用", "确认", "保存", "Submit"]):
                                    if self.safe_click(driver, button, f"第{row_number}行使用该地址按钮"):
                                        log_success(f"第{row_number}行: 成功点击使用该地址按钮")
                                        button_clicked = True
                                        time.sleep(2)
                                        break
                        except Exception as button_e:
                            log_info(f"第{row_number}行: 按钮点击失败: {str(button_e)}")
                            continue

                    if button_clicked:
                        break

                except Exception as selector_e:
                    log_info(f"第{row_number}行: 按钮选择器 '{selector}' 失败: {str(selector_e)}")
                    continue

            if not button_clicked:
                log_error(f"第{row_number}行: 未能点击使用该地址按钮")
                return False

            # 等待弹窗关闭
            try:
                WebDriverWait(driver, 5).until_not(
                    EC.presence_of_element_located((By.XPATH, "//*[@id='settings-tabs-wrapper']/kat-tab[3]/div/kat-modal"))
                )
                log_success(f"第{row_number}行: 弹窗已关闭")
            except:
                log_info(f"第{row_number}行: 弹窗可能未完全关闭，继续执行")

            return True

        except Exception as e:
            log_error(f"第{row_number}行: 处理地址选择弹窗时发生错误: {str(e)}")
            return False

    def verify_optional_services_update(self, driver, country):
        """验证可选服务设置更新是否成功"""
        try:
            log_step(f"{country}: 验证可选服务设置更新结果")

            # 等待页面刷新完成
            time.sleep(2)

            # 检查MWS贴标服务是否已禁用
            try:
                mws_status_element = driver.find_element(By.XPATH, "//*[@id='root']/div/div/div[1]/kat-table/kat-table-body/kat-table-row[1]/kat-table-cell[2]")
                mws_status = mws_status_element.text
                if "已禁用" in mws_status or "禁用" in mws_status:
                    log_success(f"{country}: MWS贴标服务禁用验证成功")
                else:
                    log_error(f"{country}: MWS贴标服务禁用验证失败，当前状态: {mws_status}")
            except Exception as e:
                log_error(f"{country}: 无法验证MWS贴标服务状态: {str(e)}")

        except Exception as e:
            log_error(f"{country}: 验证可选服务设置更新时发生错误: {str(e)}")

    def verify_inbound_settings_update(self, driver, country):
        """验证入库设置更新是否成功"""
        try:
            log_step(f"{country}: 验证入库设置更新结果")

            # 等待页面刷新完成
            time.sleep(2)

            verification_failed = False

            # 检查亚马逊优化货件拆分
            try:
                shipping_element = driver.find_element(By.XPATH, "//*[@id='root']/div/div/div[3]/kat-table/kat-table-body/kat-table-row[1]/kat-table-cell[2]")
                shipping_status = shipping_element.text
                if "亚马逊优化货件拆分" in shipping_status:
                    log_success(f"{country}: 亚马逊优化货件拆分验证成功")
                else:
                    log_error(f"{country}: 亚马逊优化货件拆分验证失败，当前状态: {shipping_status}")
                    verification_failed = True
            except Exception as e:
                log_error(f"{country}: 无法验证亚马逊优化货件拆分状态: {str(e)}")
                verification_failed = True

            # 检查显示受限商品警告
            try:
                warning_element = driver.find_element(By.XPATH, "//*[@id='root']/div/div/div[3]/kat-table/kat-table-body/kat-table-row[2]/kat-table-cell[2]")
                warning_status = warning_element.text
                if "已禁用" in warning_status or "禁用" in warning_status:
                    log_success(f"{country}: 显示受限商品警告禁用验证成功")
                else:
                    log_error(f"{country}: 显示受限商品警告禁用验证失败，当前状态: {warning_status}")
                    verification_failed = True
            except Exception as e:
                log_error(f"{country}: 无法验证显示受限商品警告状态: {str(e)}")
                verification_failed = True

            # 检查2D条形码
            try:
                barcode_element = driver.find_element(By.XPATH, "//*[@id='root']/div/div/div[3]/kat-table/kat-table-body/kat-table-row[3]/kat-table-cell[2]")
                barcode_status = barcode_element.text
                if "已禁用" in barcode_status or "禁用" in barcode_status:
                    log_success(f"{country}: 2D条形码禁用验证成功")
                else:
                    log_error(f"{country}: 2D条形码禁用验证失败，当前状态: {barcode_status}")
                    verification_failed = True
            except Exception as e:
                log_error(f"{country}: 无法验证2D条形码状态: {str(e)}")
                verification_failed = True

            # 检查显示商品是否可售的提示
            try:
                sellable_element = driver.find_element(By.XPATH, "//*[@id='root']/div/div/div[3]/kat-table/kat-table-body/kat-table-row[4]/kat-table-cell[2]")
                sellable_status = sellable_element.text
                if "已启用" in sellable_status or "启用" in sellable_status:
                    log_success(f"{country}: 显示商品是否可售的提示启用验证成功")
                else:
                    log_error(f"{country}: 显示商品是否可售的提示启用验证失败，当前状态: {sellable_status}")
                    verification_failed = True
            except Exception as e:
                log_error(f"{country}: 无法验证显示商品是否可售的提示状态: {str(e)}")
                verification_failed = True

            if verification_failed:
                log_error(f"{country}: 入库设置更新验证失败，存在不符合规定的设置")
            else:
                log_success(f"{country}: 入库设置更新验证全部通过")

        except Exception as e:
            log_error(f"{country}: 验证入库设置更新时发生错误: {str(e)}")

    def verify_unfulfillable_settings_update(self, driver, country):
        """验证不可售商品自动设置更新是否成功"""
        try:
            log_step(f"{country}: 验证不可售商品自动设置更新结果")

            # 等待页面刷新完成
            time.sleep(2)

            verification_failed = False

            # 检查不可售商品自动设置
            try:
                auto_setting_element = driver.find_element(By.XPATH, "//*[@id='root']/div/div/div[5]/kat-table/kat-table-body/kat-table-row[1]/kat-table-cell[2]")
                auto_status = auto_setting_element.text
                if "已启用" in auto_status or "启用" in auto_status:
                    log_success(f"{country}: 不可售商品自动设置启用验证成功")
                else:
                    log_error(f"{country}: 不可售商品自动设置启用验证失败，当前状态: {auto_status}")
                    verification_failed = True
            except Exception as e:
                log_error(f"{country}: 无法验证不可售商品自动设置状态: {str(e)}")
                verification_failed = True

            # 检查退还或弃置设置
            try:
                disposal_element = driver.find_element(By.XPATH, "//*[@id='root']/div/div/div[5]/kat-table/kat-table-body/kat-table-row[3]/kat-table-cell[2]")
                disposal_status = disposal_element.text
                if "弃置" in disposal_status:
                    log_success(f"{country}: 退还或弃置设置验证成功")
                else:
                    log_error(f"{country}: 退还或弃置设置验证失败，当前状态: {disposal_status}")
                    verification_failed = True
            except Exception as e:
                log_error(f"{country}: 无法验证退还或弃置设置状态: {str(e)}")
                verification_failed = True

            if verification_failed:
                log_error(f"{country}: 不可售商品自动设置更新验证失败，存在不符合规定的设置")
            else:
                log_success(f"{country}: 不可售商品自动设置更新验证全部通过")

        except Exception as e:
            log_error(f"{country}: 验证不可售商品自动设置更新时发生错误: {str(e)}")

    def verify_barcode_preferences_update(self, driver, country):
        """验证亚马逊物流商品条形码首选项更新是否成功"""
        try:
            log_step(f"{country}: 验证亚马逊物流商品条形码首选项更新结果")

            # 等待页面刷新完成
            time.sleep(2)

            # 检查条形码首选项
            try:
                barcode_element = driver.find_element(By.XPATH, "//*[@id='root']/div/div/div[8]/kat-table/kat-table-body/kat-table-row/kat-table-cell[2]")
                barcode_status = barcode_element.text
                if "亚马逊条形码" in barcode_status:
                    log_success(f"{country}: 亚马逊物流商品条形码首选项验证成功")
                else:
                    log_error(f"{country}: 亚马逊物流商品条形码首选项验证失败，当前状态: {barcode_status}")
            except Exception as e:
                log_error(f"{country}: 无法验证亚马逊物流商品条形码首选项状态: {str(e)}")

        except Exception as e:
            log_error(f"{country}: 验证亚马逊物流商品条形码首选项更新时发生错误: {str(e)}")

    def verify_fulfillable_settings_update(self, driver, country):
        """验证可售商品自动设置更新是否成功"""
        try:
            log_step(f"{country}: 验证可售商品自动设置更新结果")

            # 等待页面刷新完成
            time.sleep(2)

            # 检查可售商品自动设置是否已启用
            try:
                auto_setting_element = driver.find_element(By.XPATH, "//*[@id='root']/div/div/div[6]/kat-table/kat-table-body/kat-table-row[1]/kat-table-cell[2]")
                auto_status = auto_setting_element.text
                if "已启用" in auto_status or "启用" in auto_status:
                    log_success(f"{country}: 可售商品自动设置启用验证成功")
                else:
                    log_error(f"{country}: 可售商品自动设置启用验证失败，当前状态: {auto_status}")
            except Exception as e:
                log_error(f"{country}: 无法验证可售商品自动设置状态: {str(e)}")

        except Exception as e:
            log_error(f"{country}: 验证可售商品自动设置更新时发生错误: {str(e)}")

    def log_setting_exception(self, store_info, country, setting_type, error_message):
        """
        记录设置异常的统一日志格式，支持错误去重
        Args:
            store_info: 店铺信息字典
            country: 国家
            setting_type: 设置类型 ("退货设置", "配送设置", "亚马逊物流")
            error_message: 错误信息
        """
        try:
            # 获取店铺名，优先从store_info获取，其次尝试从实例属性获取
            store_name = "未知店铺"
            if store_info and isinstance(store_info, dict):
                store_name = store_info.get('店铺名', store_info.get('store_name', '未知店铺'))
            elif hasattr(self, 'current_store_info') and self.current_store_info:
                store_name = self.current_store_info.get('店铺名', self.current_store_info.get('store_name', '未知店铺'))
            elif hasattr(self, 'store_name') and self.store_name:
                store_name = self.store_name

            # 创建错误标识符
            error_key = (store_name, country, setting_type)

            # 检查是否已经记录过此错误
            if error_key in self.logged_errors:
                # 已记录过，只记录简单日志
                log_info(f"{store_name} {country} {setting_type}异常已记录，跳过重复日志")
                return

            # 添加到已记录集合
            self.logged_errors.add(error_key)

            # 构建统一的日志前缀
            log_prefix = f"（注意检查）{store_name} {country} {setting_type}异常"

            # 记录异常日志
            log_error(f"{log_prefix}: {error_message}")

        except Exception as e:
            # 如果日志记录本身出错，使用基本格式
            log_error(f"（注意检查）日志记录异常 {country} {setting_type}异常: {error_message}")
            log_error(f"日志记录异常详情: {str(e)}")

    def handle_new_inbound_settings(self, driver, country, store_info=None):
        """处理入库设置部分（新逻辑）"""
        try:
            log_step(f"{country}: 检查入库设置部分")

            # 检查亚马逊 API 的入库配置服务费用选项
            need_edit = False
            try:
                # 检查第一行是否是"最低货件拆分: 西部"
                service_fee_element = driver.find_element(By.XPATH, "//*[@id='root']/div/div/div[3]/kat-table/kat-table-body/kat-table-row[1]/kat-table-cell[2]")
                current_status = service_fee_element.text
                log_info(f"{country}: 当前入库配置服务费用选项状态: {current_status}")

                if "最低货件拆分: 西部" not in current_status:
                    log_step(f"{country}: 入库配置服务费用选项不是'最低货件拆分: 西部'，需要编辑")
                    need_edit = True
                else:
                    log_info(f"{country}: 入库配置服务费用选项已经是'最低货件拆分: 西部'")
            except Exception as e:
                log_error(f"{country}: 检查入库配置服务费用选项失败: {str(e)}")
                need_edit = True

            if need_edit:
                # 点击编辑按钮，增加重试机制
                edit_success = False
                for attempt in range(3):
                    try:
                        log_step(f"{country}: 尝试点击入库设置编辑按钮 (第{attempt+1}次)")
                        edit_button = WebDriverWait(driver, 15).until(
                            EC.element_to_be_clickable((By.XPATH, "//*[@id='root']/div/div/div[3]/kat-table/kat-table-head/kat-table-row/kat-table-cell[2]/span/a/kat-button"))
                        )

                        # 滚动到编辑按钮
                        driver.execute_script("arguments[0].scrollIntoView(true);", edit_button)
                        time.sleep(1)

                        if self.safe_click(driver, edit_button, f"{country}入库设置编辑按钮"):
                            log_success(f"{country}: 入库设置编辑按钮点击成功")
                            edit_success = True
                            time.sleep(5)  # 等待页面加载
                            break
                        else:
                            log_error(f"{country}: 入库设置编辑按钮点击失败 (第{attempt+1}次)")
                            time.sleep(2)
                    except Exception as e:
                        log_error(f"{country}: 点击入库设置编辑按钮异常 (第{attempt+1}次): {str(e)}")
                        time.sleep(2)

                if edit_success:
                    # 检查编辑页面是否正确加载
                    try:
                        # 等待编辑页面加载
                        WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.XPATH, "//*[@id='root']/div/div/kat-table/kat-table-body"))
                        )
                        log_success(f"{country}: 入库设置编辑页面加载成功")

                        # 点击包含"最低货件拆分"的选项
                        radio_success = False
                        try:
                            min_shipment_radio = WebDriverWait(driver, 10).until(
                                EC.element_to_be_clickable((By.XPATH, "//*[@id='root']/div/div/kat-table/kat-table-body/kat-table-row[1]/kat-table-cell[2]/div/kat-radiobutton[2]"))
                            )
                            if self.safe_click(driver, min_shipment_radio, f"{country}最低货件拆分选项"):
                                log_success(f"{country}: 已选择最低货件拆分选项")
                                time.sleep(1)

                                # 在radiobutton-group中选择"西部"选项
                                try:
                                    west_radio = WebDriverWait(driver, 10).until(
                                        EC.element_to_be_clickable((By.XPATH, "//*[@id='root']/div/div/kat-table/kat-table-body/kat-table-row[1]/kat-table-cell[2]/div/div/kat-radiobutton-group/div/kat-radiobutton[3]/input[@type='radio']"))
                                    )
                                    if self.safe_click(driver, west_radio, f"{country}西部选项"):
                                        log_success(f"{country}: 已选择西部选项")
                                        radio_success = True
                                    else:
                                        log_error(f"{country}: 选择西部选项失败")
                                except Exception as e:
                                    log_error(f"{country}: 选择西部选项时发生错误: {str(e)}")
                            else:
                                log_error(f"{country}: 选择最低货件拆分选项失败")
                        except Exception as e:
                            log_error(f"{country}: 选择最低货件拆分选项时发生错误: {str(e)}")

                        if radio_success:
                            # 点击更新按钮，增加刷新页面重试机制
                            update_success = False
                            max_update_attempts = 2  # 最多尝试2次（包括刷新重试）

                            for main_attempt in range(max_update_attempts):
                                try:
                                    log_step(f"{country}: 尝试点击入库设置更新按钮 (第{main_attempt+1}/{max_update_attempts}次)")

                                    # 子重试：普通重试3次
                                    for sub_attempt in range(3):
                                        try:
                                            update_button = WebDriverWait(driver, 10).until(
                                                EC.element_to_be_clickable((By.XPATH, "//*[@id='root']/div/div/div/a[1]/kat-button"))
                                            )

                                            # 优先使用WebDriver原生点击，失败时使用多重策略
                                            log_info(f"{country}: 尝试WebDriver原生点击入库设置更新按钮 (第{sub_attempt+1}次)")
                                            try:
                                                update_button.click()
                                                log_success(f"{country}: WebDriver原生点击入库设置更新按钮成功")
                                                update_success = True
                                                break
                                            except Exception as native_e:
                                                log_warning(f"{country}: WebDriver原生点击入库设置更新按钮失败: {str(native_e)}")
                                                log_info(f"{country}: 使用多重点击策略")
                                                if self.try_multiple_click_strategies(driver, update_button, country, "入库设置更新按钮"):
                                                    log_success(f"{country}: 入库设置多重点击策略成功")
                                                    update_success = True
                                                    break
                                                else:
                                                    log_error(f"{country}: 入库设置更新按钮点击失败 (子重试第{sub_attempt+1}次)")
                                                    time.sleep(2)
                                        except Exception as e:
                                            log_error(f"{country}: 点击入库设置更新按钮异常 (子重试第{sub_attempt+1}次): {str(e)}")
                                            time.sleep(2)

                                    if update_success:
                                        log_success(f"{country}: 入库设置更新完成")
                                        time.sleep(5)  # 等待页面更新

                                        # 验证入库设置更新是否成功
                                        self.verify_new_inbound_settings_update(driver, country)

                                        # 处理编辑页面弹窗
                                        self.handle_edit_page_popup(driver, country)
                                        break
                                    else:
                                        if main_attempt < max_update_attempts - 1:  # 不是最后一次尝试
                                            log_step(f"{country}: 入库设置更新按钮点击失败，刷新页面重试")
                                            driver.refresh()
                                            time.sleep(5)  # 等待页面重新加载

                                            # 重新检查编辑页面是否加载
                                            if not self.check_edit_page_loaded(driver, country, "inbound"):
                                                log_error(f"{country}: 刷新后入库设置编辑页面仍未正确加载")
                                                continue
                                except Exception as main_e:
                                    log_error(f"{country}: 入库设置更新按钮操作主循环异常 (第{main_attempt+1}次): {str(main_e)}")
                                    if main_attempt < max_update_attempts - 1:
                                        log_step(f"{country}: 发生异常，刷新页面重试")
                                        try:
                                            driver.refresh()
                                            time.sleep(5)

                                            # 重新检查编辑页面是否加载
                                            if not self.check_edit_page_loaded(driver, country, "inbound"):
                                                log_error(f"{country}: 刷新后入库设置编辑页面仍未正确加载")
                                                continue
                                        except Exception as refresh_e:
                                            log_error(f"{country}: 页面刷新失败: {str(refresh_e)}")
                                            continue

                            if not update_success:
                                log_error(f"{country}: 入库设置更新失败，已尝试{max_update_attempts}次（包括刷新重试）")
                                raise Exception("入库设置更新失败")
                        else:
                            log_error(f"{country}: 选择禁用可售商品自动设置失败")

                    except Exception as e:
                        log_error(f"{country}: 入库设置编辑页面加载失败: {str(e)}")
                else:
                    log_error(f"{country}: 入库设置编辑按钮点击失败，已尝试3次")
            else:
                log_info(f"{country}: 入库设置已符合要求，无需编辑")

        except Exception as e:
            log_error(f"{country}: 处理入库设置时发生错误: {str(e)}")
            log_error(f"{country}: 入库设置详细错误信息: {traceback.format_exc()}")

    def verify_new_inbound_settings_update(self, driver, country):
        """验证新入库设置更新是否成功"""
        try:
            log_step(f"{country}: 验证入库设置更新结果")

            # 等待页面刷新完成
            time.sleep(2)

            verification_failed = False

            # 检查最低货件拆分: 西部
            try:
                shipping_element = driver.find_element(By.XPATH, "//*[@id='root']/div/div/div[3]/kat-table/kat-table-body/kat-table-row[1]/kat-table-cell[2]")
                shipping_status = shipping_element.text
                if "最低货件拆分: 西部" in shipping_status:
                    log_success(f"{country}: 最低货件拆分: 西部验证成功")
                else:
                    log_error(f"{country}: 最低货件拆分: 西部验证失败，当前状态: {shipping_status}")
                    verification_failed = True
            except Exception as e:
                log_error(f"{country}: 无法验证最低货件拆分状态: {str(e)}")
                verification_failed = True

            if verification_failed:
                log_error(f"{country}: 入库设置更新验证失败，存在不符合规定的设置")
            else:
                log_success(f"{country}: 入库设置更新验证全部通过")

        except Exception as e:
            log_error(f"{country}: 验证入库设置更新时发生错误: {str(e)}")

    def handle_new_unfulfillable_settings(self, driver, country, store_info=None):
        """处理不可售商品自动设置部分（新逻辑）"""
        try:
            log_step(f"{country}: 检查不可售商品自动设置部分")

            # 检查价值回收选项
            need_edit = False
            try:
                # 检查价值回收选项那一行是否包含"批量清货"
                value_recovery_element = driver.find_element(By.XPATH, "//*[@id='root']/div/div/div[5]/kat-table/kat-table-body/kat-table-row[2]/kat-table-cell[2]")
                current_status = value_recovery_element.text
                log_info(f"{country}: 当前价值回收选项状态: {current_status}")

                if "批量清货" not in current_status:
                    log_step(f"{country}: 价值回收选项不包含'批量清货'，需要编辑")
                    need_edit = True
                else:
                    log_info(f"{country}: 价值回收选项已包含'批量清货'")
            except Exception as e:
                log_error(f"{country}: 检查价值回收选项失败: {str(e)}")
                need_edit = True

            if need_edit:
                # 点击编辑按钮，增加重试机制
                edit_success = False
                for attempt in range(3):
                    try:
                        log_step(f"{country}: 尝试点击不可售商品自动设置编辑按钮 (第{attempt+1}次)")
                        edit_button = WebDriverWait(driver, 15).until(
                            EC.element_to_be_clickable((By.XPATH, "//*[@id='root']/div/div/div[5]/kat-table/kat-table-head/kat-table-row/kat-table-cell[2]/span/a/kat-button"))
                        )

                        # 滚动到编辑按钮
                        driver.execute_script("arguments[0].scrollIntoView(true);", edit_button)
                        time.sleep(1)

                        if self.safe_click(driver, edit_button, f"{country}不可售商品自动设置编辑按钮"):
                            log_success(f"{country}: 不可售商品自动设置编辑按钮点击成功")
                            edit_success = True
                            # 设置编辑模式标志
                            self.in_edit_mode = True
                            time.sleep(5)  # 等待页面加载
                            break
                        else:
                            log_error(f"{country}: 不可售商品自动设置编辑按钮点击失败 (第{attempt+1}次)")
                            time.sleep(2)
                    except Exception as e:
                        log_error(f"{country}: 点击不可售商品自动设置编辑按钮异常 (第{attempt+1}次): {str(e)}")
                        time.sleep(2)

                if edit_success:
                    # 检查编辑页面是否真正加载成功
                    edit_page_loaded = self.check_edit_page_loaded(driver, country, "unfulfillable", timeout=15)

                    if edit_page_loaded:

                        # 处理可能的弹窗
                        try:
                            confirm_button = WebDriverWait(driver, 5).until(
                                EC.element_to_be_clickable((By.XPATH, "//*[@id='sc-content-container']/section/ngb-modal-window/div/div/div[3]/kat-button"))
                            )
                            if self.safe_click(driver, confirm_button, f"{country}确定配置弹窗"):
                                log_success(f"{country}: 已点击确定以配置设置")
                                time.sleep(2)
                        except:
                            log_info(f"{country}: 未发现配置弹窗")

                        # 增强的批量清货勾选策略
                        checkbox_success = self.try_liquidation_checkbox_with_refresh(driver, country)

                        if not checkbox_success:
                            log_error(f"{country}: 所有批量清货勾选尝试均失败")

                        if checkbox_success:
                            # 点击更新按钮，增加刷新页面重试机制
                            update_success = False
                            max_update_attempts = 2  # 最多尝试2次（包括刷新重试）

                            for main_attempt in range(max_update_attempts):
                                try:
                                    log_step(f"{country}: 尝试点击不可售商品设置更新按钮 (第{main_attempt+1}/{max_update_attempts}次)")

                                    # 子重试：普通重试3次
                                    for sub_attempt in range(3):
                                        try:
                                            update_button = WebDriverWait(driver, 10).until(
                                                EC.presence_of_element_located((By.XPATH, "//*[@id='sc-content-container']/section/app-root/recovery-rsu-automated-unfulfillable/div[2]/div/kat-button[2]"))
                                            )

                                            # 优先使用WebDriver原生点击，失败时使用多重策略
                                            log_info(f"{country}: 尝试WebDriver原生点击不可售商品更新按钮 (第{sub_attempt+1}次)")
                                            try:
                                                update_button.click()
                                                log_success(f"{country}: WebDriver原生点击不可售商品更新按钮成功")
                                                update_success = True
                                                break
                                            except Exception as native_e:
                                                log_warning(f"{country}: WebDriver原生点击不可售商品更新按钮失败: {str(native_e)}")
                                                log_info(f"{country}: 使用多重点击策略")
                                                if self.try_multiple_click_strategies(driver, update_button, country, "不可售商品设置更新按钮"):
                                                    log_success(f"{country}: 不可售商品多重点击策略成功")
                                                    update_success = True
                                                    break
                                                else:
                                                    log_error(f"{country}: 不可售商品设置更新按钮点击失败 (子重试第{sub_attempt+1}次)")
                                                    time.sleep(2)
                                        except Exception as e:
                                            log_error(f"{country}: 点击不可售商品设置更新按钮异常 (子重试第{sub_attempt+1}次): {str(e)}")
                                            time.sleep(2)

                                    if update_success:
                                        log_success(f"{country}: 不可售商品设置更新完成")
                                        time.sleep(5)  # 等待页面更新

                                        # 验证不可售商品设置更新是否成功
                                        self.verify_new_unfulfillable_settings_update(driver, country)

                                        # 处理编辑页面弹窗
                                        self.handle_edit_page_popup(driver, country)
                                        break
                                    else:
                                        if main_attempt < max_update_attempts - 1:  # 不是最后一次尝试
                                            log_step(f"{country}: 不可售商品更新按钮点击失败，刷新页面重试")
                                            driver.refresh()
                                            time.sleep(5)  # 等待页面重新加载

                                            # 重新检查编辑页面是否加载
                                            if not self.check_edit_page_loaded(driver, country, "unfulfillable"):
                                                log_error(f"{country}: 刷新后不可售商品编辑页面仍未正确加载")
                                                continue
                                except Exception as main_e:
                                    log_error(f"{country}: 不可售商品更新按钮操作主循环异常 (第{main_attempt+1}次): {str(main_e)}")
                                    if main_attempt < max_update_attempts - 1:
                                        log_step(f"{country}: 发生异常，刷新页面重试")
                                        try:
                                            driver.refresh()
                                            time.sleep(5)

                                            # 重新检查编辑页面是否加载
                                            if not self.check_edit_page_loaded(driver, country, "unfulfillable"):
                                                log_error(f"{country}: 刷新后不可售商品编辑页面仍未正确加载")
                                                continue
                                        except Exception as refresh_e:
                                            log_error(f"{country}: 页面刷新失败: {str(refresh_e)}")
                                            continue

                            if not update_success:
                                log_error(f"{country}: 不可售商品设置更新失败，已尝试{max_update_attempts}次（包括刷新重试）")
                                raise Exception("不可售商品设置更新失败")
                        else:
                            log_error(f"{country}: 勾选批量清货选项失败")
                            raise Exception("勾选批量清货选项失败")
                    else:
                        log_error(f"{country}: 不可售商品自动设置编辑页面加载失败，尝试返回FBA设置页面")
                        # 编辑页面加载失败，尝试返回FBA设置页面
                        if self.recover_to_fba_page_from_edit(driver, country, store_info):
                            log_info(f"{country}: 已返回FBA设置页面，跳过不可售商品设置")
                        else:
                            log_error(f"{country}: 返回FBA设置页面也失败了")
                else:
                    log_error(f"{country}: 不可售商品自动设置编辑按钮点击失败，已尝试3次")
            else:
                log_info(f"{country}: 不可售商品自动设置已符合要求，无需编辑")

        except Exception as e:
            log_error(f"{country}: 处理不可售商品自动设置时发生错误: {str(e)}")
            log_error(f"{country}: 不可售商品自动设置详细错误信息: {traceback.format_exc()}")
            # 重新抛出异常，确保上层能正确处理失败状态
            raise

    def verify_new_unfulfillable_settings_update(self, driver, country):
        """验证新不可售商品自动设置更新是否成功"""
        try:
            log_step(f"{country}: 验证不可售商品自动设置更新结果")

            # 等待页面刷新完成
            time.sleep(2)

            verification_failed = False

            # 检查价值回收选项是否包含批量清货
            try:
                value_recovery_element = driver.find_element(By.XPATH, "//*[@id='root']/div/div/div[5]/kat-table/kat-table-body/kat-table-row[2]/kat-table-cell[2]")
                recovery_status = value_recovery_element.text
                if "批量清货" in recovery_status:
                    log_success(f"{country}: 价值回收选项包含批量清货验证成功")
                else:
                    log_error(f"{country}: 价值回收选项包含批量清货验证失败，当前状态: {recovery_status}")
                    verification_failed = True
            except Exception as e:
                log_error(f"{country}: 无法验证价值回收选项状态: {str(e)}")
                verification_failed = True

            if verification_failed:
                log_error(f"{country}: 不可售商品自动设置更新验证失败，存在不符合规定的设置")
            else:
                log_success(f"{country}: 不可售商品自动设置更新验证全部通过")

        except Exception as e:
            log_error(f"{country}: 验证不可售商品自动设置更新时发生错误: {str(e)}")

    def handle_sellable_settings(self, driver, country, store_info=None):
        """处理可售商品自动设置部分（新增）"""
        try:
            log_step(f"{country}: 检查可售商品自动设置部分")

            # 检查可售商品自动设置
            need_edit = False
            try:
                # 检查可售商品自动设置是否包含"已禁用"
                sellable_setting_element = driver.find_element(By.XPATH, "//*[@id='root']/div/div/div[6]/kat-table/kat-table-body/kat-table-row[1]/kat-table-cell[2]")
                current_status = sellable_setting_element.text
                log_info(f"{country}: 当前可售商品自动设置状态: {current_status}")

                if "已禁用" not in current_status:
                    log_step(f"{country}: 可售商品自动设置不包含'已禁用'，需要编辑")
                    need_edit = True
                else:
                    log_info(f"{country}: 可售商品自动设置已包含'已禁用'")
            except Exception as e:
                log_error(f"{country}: 检查可售商品自动设置失败: {str(e)}")
                need_edit = True

            if need_edit:
                # 点击编辑按钮，增加重试机制
                edit_success = False
                for attempt in range(3):
                    try:
                        log_step(f"{country}: 尝试点击可售商品自动设置编辑按钮 (第{attempt+1}次)")
                        edit_button = WebDriverWait(driver, 15).until(
                            EC.element_to_be_clickable((By.XPATH, "//*[@id='root']/div/div/div[6]/kat-table/kat-table-head/kat-table-row/kat-table-cell[2]/span/a/kat-button"))
                        )

                        # 滚动到编辑按钮
                        driver.execute_script("arguments[0].scrollIntoView(true);", edit_button)
                        time.sleep(1)

                        if self.safe_click(driver, edit_button, f"{country}可售商品自动设置编辑按钮"):
                            log_success(f"{country}: 可售商品自动设置编辑按钮点击成功")
                            edit_success = True
                            # 设置编辑模式标志
                            self.in_edit_mode = True
                            time.sleep(5)  # 等待页面加载
                            break
                        else:
                            log_error(f"{country}: 可售商品自动设置编辑按钮点击失败 (第{attempt+1}次)")
                            time.sleep(2)
                    except Exception as e:
                        log_error(f"{country}: 点击可售商品自动设置编辑按钮异常 (第{attempt+1}次): {str(e)}")
                        time.sleep(2)

                if edit_success:
                    # 检查编辑页面是否真正加载成功
                    edit_page_loaded = self.check_edit_page_loaded(driver, country, "sellable", timeout=15)

                    if edit_page_loaded:

                        # 处理可能的弹窗
                        try:
                            confirm_button = WebDriverWait(driver, 5).until(
                                EC.element_to_be_clickable((By.XPATH, "//*[@id='sc-content-container']/section/ngb-modal-window/div/div/div[3]/kat-button"))
                            )
                            if self.safe_click(driver, confirm_button, f"{country}确定配置弹窗"):
                                log_success(f"{country}: 已点击确定以配置设置")
                                time.sleep(2)
                        except:
                            log_info(f"{country}: 未发现配置弹窗")

                        # 完整的可售商品设置流程 - 包括禁用选择和更新按钮
                        complete_success = False
                        max_full_attempts = 2  # 最多完整重试2次

                        for full_attempt in range(max_full_attempts):
                            try:
                                log_step(f"{country}: 开始完整的可售商品设置流程 (第{full_attempt+1}/{max_full_attempts}次)")

                                # 步骤1: 选择禁用选项
                                radio_success = False
                                log_step(f"{country}: 步骤1 - 选择禁用可售商品自动设置")

                                # 在操作前检查弹窗
                                log_step(f"{country}: 操作前检查是否有弹窗需要处理")
                                popup_handled = self.handle_edit_page_popup(driver, country)
                                if popup_handled:
                                    time.sleep(2)  # 等待弹窗处理完成

                                # 弹窗处理后，等待页面重新加载和JavaScript初始化
                                log_step(f"{country}: 弹窗处理后等待页面重新加载...")
                                time.sleep(5)  # 等待页面重新渲染

                                # 尝试多个可点击的元素xpath（基于用户提供的精确路径和错误分析）
                                radio_xpaths = [
                                    # 优先：用户提供的精确span路径（直接点击实际可见的span元素）
                                    "//*[@id='sc-content-container']/section/app-root/recovery-automated-sellable-x-month/div[1]/div[2]/recovery-general-setting-card/kat-card/div/div[2]/recovery-rsf-enable-row/kat-table/kat-table-body/kat-table-row/kat-table-cell/recovery-setting-row/div/div[2]/kat-radiobutton[2]/span",
                                    # 备用：通过第二个radiobutton查找span元素
                                    "//kat-radiobutton[2]/span[@class='kat-radiobutton-icon']",
                                    "//kat-radiobutton[2]/span",
                                    # 备用：通过aria-label查找禁用选项的span
                                    "//input[@aria-label='禁用']/../span[@class='kat-radiobutton-icon']",
                                    # 备用：原有的通过id查找（但input被遮挡，优先级降低）
                                    "//*[@id='katal-id-2']//span[@class='kat-radiobutton-icon']",
                                    "//*[@id='katal-id-2']/parent::*",
                                    "//*[@id='katal-id-2']"
                                ]

                                disable_radio = None
                                selected_xpath = None
                                for radio_xpath in radio_xpaths:
                                    try:
                                        disable_radio = WebDriverWait(driver, 8).until(
                                            EC.presence_of_element_located((By.XPATH, radio_xpath))
                                        )
                                        selected_xpath = radio_xpath
                                        log_info(f"{country}: 找到禁用选项元素: {radio_xpath}")
                                        break
                                    except TimeoutException:
                                        log_warning(f"{country}: xpath失败: {radio_xpath}")
                                        continue

                                if not disable_radio:
                                    raise Exception("无法找到任何可点击的禁用选项元素")
                                log_info(f"{country}: 找到禁用选项，使用多重点击策略")

                                # 使用多重点击策略处理禁用选项
                                if self.try_multiple_click_strategies(driver, disable_radio, country, "可售商品自动设置禁用选项"):
                                    log_info(f"{country}: 禁用选项点击完成，验证页面状态...")

                                    # 等待一下让页面响应
                                    time.sleep(3)

                                    # 操作后检查是否出现配置弹窗，并处理
                                    log_step(f"{country}: 操作后检查是否有配置确认弹窗")
                                    popup_handled = self.handle_edit_page_popup(driver, country)
                                    if popup_handled:
                                        log_step(f"{country}: 弹窗已处理，等待页面重新初始化...")
                                        time.sleep(5)  # 等待弹窗处理完成和页面重新初始化

                                    # 验证禁用选项是否真的被选中了（关键验证）
                                    log_step(f"{country}: 验证禁用选项是否真的被勾选")
                                    try:
                                        # 等待一下确保状态更新
                                        time.sleep(2)

                                        # 重新找到禁用选项元素，检查其选中状态
                                        verification_success = False
                                        verification_xpaths = [
                                            # 和点击时使用的相同优先级顺序
                                            "//*[@id='sc-content-container']/section/app-root/recovery-automated-sellable-x-month/div[1]/div[2]/recovery-general-setting-card/kat-card/div/div[2]/recovery-rsf-enable-row/kat-table/kat-table-body/kat-table-row/kat-table-cell/recovery-setting-row/div/div[2]/kat-radiobutton[2]/span",
                                            "//kat-radiobutton[2]/span[@class='kat-radiobutton-icon']",
                                            "//kat-radiobutton[2]/span",
                                            "//input[@aria-label='禁用']/../span[@class='kat-radiobutton-icon']",
                                            "//*[@id='katal-id-2']//span[@class='kat-radiobutton-icon']",
                                            "//*[@id='katal-id-2']/parent::*",
                                            "//*[@id='katal-id-2']"
                                        ]
                                        for radio_xpath in verification_xpaths:
                                            try:
                                                radio_element = driver.find_element(By.XPATH, radio_xpath)

                                                # 检查radio button是否被选中
                                                if radio_xpath.endswith("//*[@id='katal-id-2']"):
                                                    # 对于input元素，检查selected属性
                                                    is_selected = radio_element.is_selected()
                                                elif radio_xpath.endswith("/span") or "span" in radio_xpath:
                                                    # 对于span元素，检查checked属性、aria-checked或class
                                                    checked_attr = radio_element.get_attribute("checked")
                                                    aria_checked = radio_element.get_attribute("aria-checked")
                                                    element_class = radio_element.get_attribute("class") or ""
                                                    is_selected = (checked_attr is not None) or aria_checked == "true" or "checked" in element_class.lower()
                                                else:
                                                    # 对于容器元素，查找内部的input并检查其状态
                                                    try:
                                                        inner_input = radio_element.find_element(By.XPATH, ".//input[@type='radio']")
                                                        is_selected = inner_input.is_selected()
                                                    except:
                                                        # 如果找不到内部input，检查容器的aria-checked或class
                                                        aria_checked = radio_element.get_attribute("aria-checked")
                                                        element_class = radio_element.get_attribute("class") or ""
                                                        is_selected = aria_checked == "true" or "checked" in element_class.lower()

                                                if is_selected:
                                                    log_success(f"{country}: ✅ 禁用选项已确认被选中 (xpath: {radio_xpath})")
                                                    verification_success = True
                                                    break
                                                else:
                                                    log_warning(f"{country}: 禁用选项未被选中 (xpath: {radio_xpath})")

                                            except Exception as verify_e:
                                                log_warning(f"{country}: 验证xpath {radio_xpath} 失败: {str(verify_e)}")
                                                continue

                                        if verification_success:
                                            log_success(f"{country}: ✅ 禁用选项状态验证成功")
                                            radio_success = True

                                            log_info(f"{country}: ✅ 禁用选项验证成功，进入更新按钮处理")
                                        else:
                                            log_error(f"{country}: ❌ 禁用选项未被真正选中，操作失败")
                                            radio_success = False
                                            raise Exception("禁用选项未被真正选中")

                                    except Exception as verify_e:
                                        log_error(f"{country}: 验证禁用选项状态失败: {str(verify_e)}")
                                        radio_success = False
                                        raise verify_e

                                    # 使用分层滚动策略（键盘优先，JavaScript作为备用）
                                    log_step(f"{country}: 滚动到页面底部")
                                    scroll_success = False

                                    # 策略1: 键盘滚动（优先，更稳定）
                                    try:
                                        body = driver.find_element(By.TAG_NAME, "body")
                                        body.send_keys(Keys.END)
                                        time.sleep(1)
                                        log_info(f"{country}: 键盘滚动成功")
                                        scroll_success = True
                                    except Exception as key_scroll_e:
                                        log_warning(f"{country}: 键盘滚动失败: {str(key_scroll_e)}")

                                    # 策略2: JavaScript滚动（备用）
                                    if not scroll_success:
                                        try:
                                            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                                            time.sleep(1)
                                            log_info(f"{country}: JavaScript滚动成功")
                                            scroll_success = True
                                        except Exception as js_scroll_e:
                                            log_warning(f"{country}: JavaScript滚动失败: {str(js_scroll_e)}")

                                    if not scroll_success:
                                        log_info(f"{country}: 所有滚动策略失败，继续执行")
                                else:
                                    log_error(f"{country}: 选择禁用可售商品自动设置失败")
                                    raise Exception("选择禁用可售商品自动设置失败")

                                # 步骤2: 点击更新按钮
                                if radio_success:
                                    log_step(f"{country}: 步骤2 - 检查并点击更新按钮")
                                    update_success = False

                                    # 使用纯WebDriver方法处理更新按钮（避免JavaScript限制）
                                    log_step(f"{country}: 查找更新按钮并尝试点击")
                                    update_success = False

                                    # 更新按钮的多个可能xpath
                                    update_button_xpaths = [
                                        "//*[@id='sc-content-container']/section/app-root/recovery-automated-sellable-x-month/div[2]/div/kat-button[2]",
                                        "//kat-button[contains(text(), '更新') or contains(text(), 'Update')]",
                                        "//button[contains(text(), '更新') or contains(text(), 'Update')]",
                                        "//kat-button[last()]",  # 最后一个按钮通常是保存/更新按钮
                                        "//*[contains(@class, 'update') or contains(@class, 'save')]//button"
                                    ]

                                    # 子重试：普通重试3次查找和点击更新按钮
                                    for sub_attempt in range(3):
                                        try:
                                            update_button = None
                                            successful_xpath = None

                                            # 尝试多个xpath查找更新按钮
                                            for btn_xpath in update_button_xpaths:
                                                try:
                                                    update_button = WebDriverWait(driver, 5).until(
                                                        EC.presence_of_element_located((By.XPATH, btn_xpath))
                                                    )
                                                    successful_xpath = btn_xpath
                                                    log_info(f"{country}: 找到更新按钮 (xpath: {btn_xpath})")
                                                    break
                                                except:
                                                    continue

                                            if update_button is None:
                                                log_warning(f"{country}: 未找到更新按钮 (第{sub_attempt+1}次)")
                                                time.sleep(2)
                                                continue

                                            # 检查更新按钮是否可用（纯WebDriver方法）
                                            try:
                                                is_enabled = update_button.is_enabled()
                                                button_class = update_button.get_attribute("class") or ""
                                                is_disabled = "disabled" in button_class.lower()

                                                if not is_enabled or is_disabled:
                                                    log_info(f"{country}: 更新按钮不可用，可能已经是目标状态，无需更新")
                                                    update_success = True
                                                    break
                                                else:
                                                    log_info(f"{country}: 更新按钮可用，需要点击保存设置")
                                            except Exception as check_e:
                                                log_warning(f"{country}: 检查更新按钮状态失败，直接尝试点击: {str(check_e)}")

                                            # 使用多重点击策略（WebDriver原生优先，JavaScript作为备用）
                                            log_info(f"{country}: 尝试多重点击策略点击更新按钮 (第{sub_attempt+1}次)")
                                            try:
                                                # 确保按钮可点击
                                                WebDriverWait(driver, 10).until(
                                                    EC.element_to_be_clickable((By.XPATH, successful_xpath))
                                                )
                                                # 使用多重点击策略
                                                if self.try_multiple_click_strategies(driver, update_button, country, "可售商品设置更新按钮"):
                                                    log_success(f"{country}: 多重点击策略成功")
                                                    update_success = True
                                                    break
                                                else:
                                                    log_error(f"{country}: 所有点击策略失败 (第{sub_attempt+1}次)")
                                                    time.sleep(2)
                                            except Exception as click_e:
                                                log_error(f"{country}: 更新按钮点击异常 (第{sub_attempt+1}次): {str(click_e)}")
                                                time.sleep(2)

                                        except Exception as e:
                                            log_error(f"{country}: 更新按钮处理异常 (第{sub_attempt+1}次): {str(e)}")
                                            time.sleep(2)

                                    if update_success:
                                        log_success(f"{country}: 可售商品设置更新流程完成")
                                        time.sleep(5)  # 等待页面更新

                                        # 验证可售商品设置更新是否成功
                                        self.verify_new_sellable_settings_update(driver, country)

                                        # 处理编辑页面弹窗
                                        self.handle_edit_page_popup(driver, country)
                                        complete_success = True
                                        break  # 完整流程成功，退出大循环
                                    else:
                                        log_error(f"{country}: 更新按钮操作失败，需要重新开始完整流程")
                                        raise Exception("更新按钮操作失败")

                            except Exception as e:
                                log_error(f"{country}: 完整流程异常 (第{full_attempt+1}次): {str(e)}")
                                if full_attempt < max_full_attempts - 1:  # 不是最后一次尝试
                                    log_step(f"{country}: 完整流程失败，刷新页面重新开始")
                                    try:
                                        driver.refresh()
                                        time.sleep(5)  # 等待页面重新加载

                                        # 重新检查编辑页面是否加载
                                        if not self.check_edit_page_loaded(driver, country, "sellable"):
                                            log_error(f"{country}: 刷新后编辑页面仍未正确加载")
                                            continue
                                    except Exception as refresh_e:
                                        log_error(f"{country}: 页面刷新失败: {str(refresh_e)}")
                                        continue

                        if not complete_success:
                            log_error(f"{country}: 完整的可售商品设置流程失败，已尝试{max_full_attempts}次")
                            raise Exception("可售商品设置流程失败")
                    else:
                        log_error(f"{country}: 可售商品自动设置编辑页面加载失败，尝试返回FBA设置页面")
                        # 编辑页面加载失败，尝试返回FBA设置页面
                        if self.recover_to_fba_page_from_edit(driver, country, store_info):
                            log_info(f"{country}: 已返回FBA设置页面，跳过可售商品设置")
                        else:
                            log_error(f"{country}: 返回FBA设置页面也失败了")
                else:
                    log_error(f"{country}: 可售商品自动设置编辑按钮点击失败，已尝试3次")
            else:
                log_info(f"{country}: 可售商品自动设置已符合要求，无需编辑")

        except Exception as e:
            log_error(f"{country}: 处理可售商品自动设置时发生错误: {str(e)}")
            log_error(f"{country}: 可售商品自动设置详细错误信息: {traceback.format_exc()}")
            # 重新抛出异常，确保上层能正确处理失败状态
            raise

    def verify_new_sellable_settings_update(self, driver, country):
        """验证新可售商品自动设置更新是否成功"""
        try:
            log_step(f"{country}: 验证可售商品自动设置更新结果")

            # 等待页面刷新完成
            time.sleep(2)

            verification_failed = False

            # 检查可售商品自动设置是否已禁用
            try:
                sellable_setting_element = driver.find_element(By.XPATH, "//*[@id='root']/div/div/div[6]/kat-table/kat-table-body/kat-table-row[1]/kat-table-cell[2]")
                sellable_status = sellable_setting_element.text
                if "已禁用" in sellable_status or "禁用" in sellable_status:
                    log_success(f"{country}: 可售商品自动设置禁用验证成功")
                else:
                    log_error(f"{country}: 可售商品自动设置禁用验证失败，当前状态: {sellable_status}")
                    verification_failed = True
            except Exception as e:
                log_error(f"{country}: 无法验证可售商品自动设置状态: {str(e)}")
                verification_failed = True

            if verification_failed:
                log_error(f"{country}: 可售商品自动设置更新验证失败，存在不符合规定的设置")
            else:
                log_success(f"{country}: 可售商品自动设置更新验证全部通过")

        except Exception as e:
            log_error(f"{country}: 验证可售商品自动设置更新时发生错误: {str(e)}")

    def drag_and_drop_row(self, driver, standard_shipping_xpath, source_xpath, target_xpath):
        """拖动包含特殊地区的行到目标行"""
        # //*[@id="JP_STANDARD.DOMESTIC"]/div[4]/div[2]/div/table/tbody/tr[6]/td[2]/div/div[2]
        # 获取源xpath的编辑按钮xpath
        source_edit_button_xpath = f"{source_xpath}/div/div[2]"
        # 获取source_edit_button_xpath下有编辑两个字的a标签超链接点击它
        source_edit_button = WebDriverWait(driver, 3).until(
            EC.element_to_be_clickable((By.XPATH, f"{source_edit_button_xpath}/a[contains(text(), '编辑')]"))
        )
        if self.safe_click(driver, source_edit_button, f"点击包含冲绳离岛行编辑按钮"):
            log_success(f"已点击包含冲绳离岛行编辑按钮")
            time.sleep(2)
        else:
            log_error(f"点击包含冲绳离岛行编辑按钮失败")
            return False
        # 点击编辑后，出现的弹窗找到//*[@id="JP_STANDARD.DOMESTIC~JP1A"]的单选框如果是勾选则点击取消勾选
        try:
            checkbox = WebDriverWait(driver, 3).until(
                EC.element_to_be_clickable((By.XPATH, "//*[@id='JP_STANDARD.DOMESTIC~JP1A']"))
            )
            if checkbox.is_selected():
                if self.safe_click(driver, checkbox, f"取消勾选"):
                    log_success(f"已取消勾选")
                else:
                    log_error(f"取消勾选失败")
            else:
                log_info(f"无需取消勾选")
                return False
            # 点击确定按钮 xpath：//*[@id="a-popover-8"]/div/div[2]/span[2]
            try:
                WebDriverWait(driver, 3).until(
                    EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'a-popover-footer')]"))
                )
                time.sleep(0.5)  # 稍等弹窗渲染
                confirm_buttons = driver.find_elements(By.XPATH, "//div[contains(@class, 'a-popover-footer')]//button[normalize-space(text())='确定']")
                log_info(f"找到{len(confirm_buttons)}个'确定'按钮")
                found = False
                for btn in confirm_buttons:
                    log_info(f"按钮显示:{{btn.is_displayed()}} 可用:{{btn.is_enabled()}} text:{{btn.text}}")
                    if btn.is_displayed() and btn.is_enabled():
                        if self.safe_click(driver, btn, "点击确定按钮"):
                            log_success("已点击确定按钮")
                            found = True
                            break
                if not found:
                    log_error("没有可点击的'确定'按钮")
                    try:
                        driver.save_screenshot("popover_confirm_error.png")
                        log_info("已截图popover_confirm_error.png")
                    except Exception as se:
                        log_error(f"截图失败: {{str(se)}}")
                    return False
            except Exception as e:
                log_error(f"查找或点击'确定'按钮时异常: {{str(e)}}")
                try:
                    driver.save_screenshot("popover_confirm_exception.png")
                    log_info("已截图popover_confirm_exception.png")
                except Exception as se:
                    log_error(f"截图失败: {{str(se)}}")
                return False
        except Exception as e:
            log_error(f":弹窗取消勾选出错,{str(e)}")
            return False
        # 去目标行，点击编辑勾选上冲绳离岛
        target_edit_button_xpath = f"{target_xpath}/div/div[2]"
        target_edit_button = WebDriverWait(driver, 3).until(
            EC.element_to_be_clickable((By.XPATH, f"{target_edit_button_xpath}/a[contains(text(), '编辑')]"))
        )
        if self.safe_click(driver, target_edit_button, f"点击冲绳县行编辑按钮"):
            log_success(f"已点击冲绳县行编辑按钮")
            time.sleep(2)
        else:
            log_error(f"点击冲绳县行编辑按钮失败")
            return False
        # 将冲绳离岛勾选上//*[@id="JP_STANDARD.DOMESTIC~JP1A"]
        try:
            checkbox = WebDriverWait(driver, 3).until(
                EC.element_to_be_clickable((By.XPATH, "//*[@id='JP_STANDARD.DOMESTIC~JP1A']"))
            )
            if not checkbox.is_selected():
                if self.safe_click(driver, checkbox, f"勾选"):
                    log_success(f"已勾选")
                else:
                    log_error(f"勾选失败")
            else:
                log_info(f"无需勾选")
                return False
            # 点击确定按钮//*[@id="a-popover-8"]/div/div[2]/span[2]
            try:
                WebDriverWait(driver, 3).until(
                    EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'a-popover-footer')]"))
                )
                time.sleep(0.5)  # 稍等弹窗渲染
                confirm_buttons = driver.find_elements(By.XPATH, "//div[contains(@class, 'a-popover-footer')]//button[normalize-space(text())='确定']")
                log_info(f"找到{len(confirm_buttons)}个'确定'按钮")
                found = False
                for btn in confirm_buttons:
                    log_info(f"按钮显示:{{btn.is_displayed()}} 可用:{{btn.is_enabled()}} text:{{btn.text}}")
                    if btn.is_displayed() and btn.is_enabled():
                        if self.safe_click(driver, btn, "点击确定按钮"):
                            log_success("已点击确定按钮")
                            found = True
                            break
                if not found:
                    log_error("没有可点击的'确定'按钮")
                    try:
                        driver.save_screenshot("popover_confirm_error.png")
                        log_info("已截图popover_confirm_error.png")
                    except Exception as se:
                        log_error(f"截图失败: {{str(se)}}")
                    return False
            except Exception as e:
                log_error(f"查找或点击'确定'按钮时异常: {{str(e)}}")
                try:
                    driver.save_screenshot("popover_confirm_exception.png")
                    log_info("已截图popover_confirm_exception.png")
                except Exception as se:
                    log_error(f"截图失败: {{str(se)}}")
                return False

        except Exception as e:
            log_error(f"添加冲绳离岛出错,{str(e)}")
            return False
        # 校验下包含冲绳县的target_xpath下现在是否已经包含冲绳离岛
        try:
            target_region_cell = WebDriverWait(driver, 3).until(
                EC.presence_of_element_located((By.XPATH, target_xpath))
            )
            target_region_text = target_region_cell.text.strip()
            if "冲绳离岛" in target_region_text:
                log_success(f"已成功将包含冲绳离岛的行拖动到包含冲绳县的行")
            else:
                log_error(f"将包含冲绳离岛拖动到包含冲绳县的行失败")
                return False
        except Exception as e:
            log_error(f"校验将包含冲绳离岛的行拖动到包含冲绳县的行失败,{str(e)}")
            return False
        return True

    def set_fee_unit(self, driver, table_xpath, row_index,log_prefix):
        # 获取table_xpath/tr[{row_index}]下所有td 找到class包含shippingFee的那一列
        tr_elements = driver.find_elements(By.XPATH, f"{table_xpath}/tr[{row_index}]")
        # 如果是多个tr则日志报错，并返回
        if len(tr_elements) > 1:
            log_error(f"{log_prefix}找到多个tr元素，无法确定目标行")
            return False
        tr_element = tr_elements[0]
        td_elements = tr_element.find_elements(By.XPATH, "./td")
        shipping_fee_td = None
        for td in td_elements:
            if "shippingFee" in td.get_attribute("class"):
                shipping_fee_td = td
                break
        if not shipping_fee_td:
            log_error(f"{log_prefix}未找到运费列")
            return False

        # 直接在shipping_fee_td下查找select元素并使用Select类选择
        try:
            # 查找select元素
            select_element = shipping_fee_td.find_element(By.XPATH, ".//select[@name='unitMeasure']")

            # 使用Select类选择"商品"选项
            select = Select(select_element)
            select.select_by_visible_text("商品")

            log_success(f"{log_prefix}已选择商品单位")
            time.sleep(0.5)  # 等待页面联动
            return True

        except Exception as e:
            log_error(f"{log_prefix}选择商品单位失败,{str(e)}")
            return False

    def get_custom_gui_components(self):
        """获取自定义GUI组件配置"""
        from gui_framework import ComponentConfig

        return [
            # 注意：已移除浏览器配置组（紫鸟浏览器不支持无头模式）

            # 模块选择组
            ComponentConfig("frame_group", "🔧 任务模块选择",
                          order=85,
                          components=[
                              ComponentConfig("label", "请选择要执行的模块（可以自由勾选需要执行的流程）:",
                                            order=1),

                              # 退货设置模块（主任务）
                              ComponentConfig("checkbox", "✅ 退货设置模块 (主RPA任务)",
                                            order=2,
                                            variable="module_return_settings",
                                            tooltip="处理店铺退货政策和地址设置"),

                              # 配送设置模块
                              ComponentConfig("checkbox", "🚚 配送设置模块",
                                            order=3,
                                            variable="module_shipping_settings",
                                            tooltip="处理配送模板和运费设置"),

                              # 配送设置子模块
                              ComponentConfig("checkbox", "  └─ 取消非标准配送勾选",
                                            order=4,
                                            variable="submodule_cancel_non_standard_shipping",
                                            parent_module="module_shipping_settings",
                                            tooltip="取消勾选非标准配送服务选项"),

                              ComponentConfig("checkbox", "  └─ 特殊地区运费设置",
                                            order=5,
                                            variable="submodule_special_region_shipping",
                                            parent_module="module_shipping_settings",
                                            tooltip="设置特殊地区(如夏威夷、阿拉斯加、德国配送)的运费"),
                              
                              # FBA设置模块
                              ComponentConfig("checkbox", "📦 亚马逊物流(FBA)设置模块 (仅美国)", 
                                            order=6,
                                            variable="module_fba_settings",
                                            tooltip="处理FBA相关设置(仅美国国家生效)"),
                              
                              # FBA子模块
                              ComponentConfig("checkbox", "  └─ 入库设置", 
                                            order=7,
                                            variable="submodule_inbound_settings",
                                            parent_module="module_fba_settings",
                                            tooltip="配置商品入库相关设置"),
                              
                              ComponentConfig("checkbox", "  └─ 不可售商品设置", 
                                            order=8,
                                            variable="submodule_unfulfillable_settings",
                                            parent_module="module_fba_settings",
                                            tooltip="配置不可售商品处理方式"),
                              
                              ComponentConfig("checkbox", "  └─ 条形码首选项", 
                                            order=9,
                                            variable="submodule_barcode_preferences",
                                            parent_module="module_fba_settings",
                                            tooltip="设置商品条形码偏好选项"),
                              
                              ComponentConfig("checkbox", "  └─ 可售商品设置", 
                                            order=10,
                                            variable="submodule_sellable_settings",
                                            parent_module="module_fba_settings",
                                            tooltip="配置可售商品自动化设置"),
                          ])
        ]
    
    def setup_module_selection_logic(self, variable_manager):
        """设置模块选择的逻辑处理"""
        # 获取所有模块相关的变量
        self.module_variables = {
            "return_settings": variable_manager.get_variable("module_return_settings"),
            "shipping_settings": variable_manager.get_variable("module_shipping_settings"), 
            "fba_settings": variable_manager.get_variable("module_fba_settings"),
            "cancel_non_standard_shipping": variable_manager.get_variable("submodule_cancel_non_standard_shipping"),
            "special_region_shipping": variable_manager.get_variable("submodule_special_region_shipping"),
            "inbound_settings": variable_manager.get_variable("submodule_inbound_settings"),
            "unfulfillable_settings": variable_manager.get_variable("submodule_unfulfillable_settings"),
            "barcode_preferences": variable_manager.get_variable("submodule_barcode_preferences"),
            "sellable_settings": variable_manager.get_variable("submodule_sellable_settings")
        }
        
        # 浏览器配置已移除无头模式（紫鸟浏览器不支持）
        self.browser_config = {}

        # 调试：浏览器配置检查
        log_info("=== 浏览器配置检查 ===")
        log_info("📋 紫鸟浏览器统一使用普通模式（不支持无头模式）")
        
        # 调试：检查模块变量是否正确获取
        log_info("=== 模块变量获取情况 ===")
        for var_name, var_obj in self.module_variables.items():
            if var_obj:
                log_info(f"✅ {var_name}: 变量已获取")
            else:
                log_info(f"❌ {var_name}: 变量未找到")
        
        # 确保submodule_widgets已初始化（可能在GUI创建时已经设置）
        if not hasattr(self, 'submodule_widgets'):
            self.submodule_widgets = {}
        
        # 调试：检查子模块组件记录情况
        log_info("=== 子模块组件记录情况 ===")
        for parent_module, sub_widgets in self.submodule_widgets.items():
            log_info(f"父模块 {parent_module}: 记录了 {len(sub_widgets)} 个子模块组件")
            for sub_widget in sub_widgets:
                log_info(f"  - 子模块: {sub_widget['variable']}")
        
        # 设置默认值（默认都不勾选）
        for var in self.module_variables.values():
            if var:
                var.set(False)
        
        # 添加变量变化监听器
        if self.module_variables["shipping_settings"]:
            self.module_variables["shipping_settings"].trace_add("write", self._on_shipping_module_changed)
            log_info("✅ 配送设置模块监听器已设置")
        else:
            log_info("❌ 配送设置模块监听器设置失败")
            
        if self.module_variables["fba_settings"]:
            self.module_variables["fba_settings"].trace_add("write", self._on_fba_module_changed)
            log_info("✅ FBA设置模块监听器已设置")
        else:
            log_info("❌ FBA设置模块监听器设置失败")
        
        # 保存variable_manager的引用以便后续使用
        self.variable_manager = variable_manager
    
    def _on_shipping_module_changed(self, *args):
        """配送设置主模块状态变化时的处理"""
        if self.module_variables["shipping_settings"]:
            enabled = self.module_variables["shipping_settings"].get()
            log_info(f"🔄 配送设置模块状态变化: {'启用' if enabled else '禁用'}")
            
            # 控制子模块显示/隐藏
            if hasattr(self, 'submodule_widgets') and "module_shipping_settings" in self.submodule_widgets:
                sub_widgets = self.submodule_widgets["module_shipping_settings"]
                log_info(f"找到 {len(sub_widgets)} 个配送设置子模块组件")
                
                for submodule_info in sub_widgets:
                    widget = submodule_info['widget']
                    var_name = submodule_info['variable']
                    
                    if enabled:
                        # 显示子模块
                        widget.grid()
                        log_info(f"  ✅ 显示子模块: {var_name}")
                    else:
                        # 隐藏子模块并取消勾选
                        widget.grid_remove()
                        log_info(f"  🚫 隐藏子模块: {var_name}")
                        
                        # 取消勾选对应的变量
                        var_key = var_name.replace('submodule_', '')
                        if var_key in self.module_variables and self.module_variables[var_key]:
                            self.module_variables[var_key].set(False)
                            log_info(f"  📝 取消勾选变量: {var_key}")
            else:
                log_info("❌ 未找到配送设置子模块组件")
    
    def _on_fba_module_changed(self, *args):
        """FBA设置主模块状态变化时的处理"""
        if self.module_variables["fba_settings"]:
            enabled = self.module_variables["fba_settings"].get()
            log_info(f"🔄 FBA设置模块状态变化: {'启用' if enabled else '禁用'}")
            
            # 控制子模块显示/隐藏
            if hasattr(self, 'submodule_widgets') and "module_fba_settings" in self.submodule_widgets:
                sub_widgets = self.submodule_widgets["module_fba_settings"]
                log_info(f"找到 {len(sub_widgets)} 个FBA设置子模块组件")
                
                for submodule_info in sub_widgets:
                    widget = submodule_info['widget']
                    var_name = submodule_info['variable']
                    
                    if enabled:
                        # 显示子模块
                        widget.grid()
                        log_info(f"  ✅ 显示子模块: {var_name}")
                    else:
                        # 隐藏子模块并取消勾选
                        widget.grid_remove()
                        log_info(f"  🚫 隐藏子模块: {var_name}")
                        
                        # 取消勾选对应的变量
                        var_key = var_name.replace('submodule_', '')
                        if var_key in self.module_variables and self.module_variables[var_key]:
                            self.module_variables[var_key].set(False)
                            log_info(f"  📝 取消勾选变量: {var_key}")
            else:
                log_info("❌ 未找到FBA设置子模块组件")
    
    def update_module_config_from_gui(self, variable_manager):
        """从GUI更新模块配置"""
        if hasattr(self, 'module_variables'):
            # 更新主模块状态
            self.module_config["return_settings"]["enabled"] = self.module_variables["return_settings"].get() if self.module_variables["return_settings"] else True
            self.module_config["shipping_settings"]["enabled"] = self.module_variables["shipping_settings"].get() if self.module_variables["shipping_settings"] else True
            self.module_config["fba_settings"]["enabled"] = self.module_variables["fba_settings"].get() if self.module_variables["fba_settings"] else True
            
            # 更新配送设置子模块状态
            if "sub_modules" in self.module_config["shipping_settings"]:
                self.module_config["shipping_settings"]["sub_modules"]["cancel_non_standard_shipping"]["enabled"] = self.module_variables["cancel_non_standard_shipping"].get() if self.module_variables["cancel_non_standard_shipping"] else True
                self.module_config["shipping_settings"]["sub_modules"]["special_region_shipping"]["enabled"] = self.module_variables["special_region_shipping"].get() if self.module_variables["special_region_shipping"] else True
            
            # 更新FBA设置子模块状态
            if "sub_modules" in self.module_config["fba_settings"]:
                self.module_config["fba_settings"]["sub_modules"]["inbound_settings"]["enabled"] = self.module_variables["inbound_settings"].get() if self.module_variables["inbound_settings"] else True
                self.module_config["fba_settings"]["sub_modules"]["unfulfillable_settings"]["enabled"] = self.module_variables["unfulfillable_settings"].get() if self.module_variables["unfulfillable_settings"] else True
                self.module_config["fba_settings"]["sub_modules"]["barcode_preferences"]["enabled"] = self.module_variables["barcode_preferences"].get() if self.module_variables["barcode_preferences"] else True
                self.module_config["fba_settings"]["sub_modules"]["sellable_settings"]["enabled"] = self.module_variables["sellable_settings"].get() if self.module_variables["sellable_settings"] else True
            
            log_info("已根据GUI选择更新模块配置")
            
            # 记录当前启用的模块
            enabled_main_modules = []
            if self.module_config["return_settings"]["enabled"]:
                enabled_main_modules.append("退货设置模块")
            if self.module_config["shipping_settings"]["enabled"]:
                enabled_main_modules.append("配送设置模块")
            if self.module_config["fba_settings"]["enabled"]:
                enabled_main_modules.append("FBA设置模块")
                
            log_info(f"启用的主模块: {', '.join(enabled_main_modules) if enabled_main_modules else '无'}")
    
    def validate_task_requirements(self, variable_manager, selected_stores):
        """验证任务要求（重写以支持模块配置）"""
        # 更新模块配置
        self.update_module_config_from_gui(variable_manager)
        
        # 检查是否至少选择了一个模块
        enabled_modules = self.get_enabled_modules()
        if not enabled_modules:
            return False, "请至少选择一个要执行的模块"
        
        log_info("店铺设置任务验证通过，模块配置已更新")
        return True, None

    def try_liquidation_checkbox_with_refresh(self, driver, country, max_attempts=2):
        """
        带页面刷新的批量清货勾选尝试
        """
        # 只保留测试中能找到元素的有效xpath
        liquidation_xpaths = [
            # 备用xpath (测试中xpath2能找到元素)
            "//*[@id='sc-content-container']/section/app-root/recovery-rsu-automated-unfulfillable/div[1]/div[2]/recovery-rsu-value-recovery-options/kat-table/kat-table-body/kat-table-row[2]/kat-table-cell/div/div[2]/recovery-setting-liquidation/div/div[1]/recovery-checkbox/kat-checkbox",
            # 更简洁xpath (测试中xpath3能找到元素)
            "//recovery-setting-liquidation//recovery-checkbox//kat-checkbox"
        ]
        
        for attempt in range(max_attempts):
            log_step(f"{country}: 批量清货勾选尝试 (第{attempt+1}/{max_attempts}次)")
            
            # 如果是重试，先刷新页面重新初始化JavaScript环境
            if attempt > 0:
                log_step(f"{country}: 刷新编辑页面以重新初始化JavaScript环境")
                try:
                    driver.refresh()
                    time.sleep(10)  # 充分等待页面加载
                    
                    # 验证页面刷新后关键元素仍然存在
                    try:
                        WebDriverWait(driver, 15).until(
                            EC.presence_of_element_located((By.XPATH, "//*[@id='sc-content-container']/section/app-root/recovery-rsu-automated-unfulfillable"))
                        )
                        log_success(f"{country}: 页面刷新后编辑页面元素仍然存在")
                    except TimeoutException:
                        log_error(f"{country}: 页面刷新后关键元素丢失")
                        continue
                        
                except Exception as refresh_e:
                    log_error(f"{country}: 页面刷新失败: {str(refresh_e)}")
                    continue
            
            # 尝试所有xpath进行元素查找和点击
            for xpath_idx, xpath in enumerate(liquidation_xpaths):
                try:
                    log_info(f"{country}: 尝试xpath方案{xpath_idx+1}: {xpath[:60]}...")
                    
                    # 等待元素存在
                    element = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, xpath))
                    )
                    log_success(f"{country}: 找到批量清货元素")
                    
                    # 跳过JavaScript依赖的滚动和可见性检查，直接尝试点击
                    # 这些操作在亚马逊的反自动化环境下会失败
                    log_info(f"{country}: 跳过滚动和可见性检查，直接尝试多种点击策略")
                    
                    # 多种点击策略，重点解决JavaScript点击问题
                    click_success = self.try_multiple_click_strategies(driver, element, country, "批量清货")
                    
                    if click_success:
                        time.sleep(2)  # 等待UI响应
                        return True
                        
                except TimeoutException:
                    log_info(f"{country}: xpath{xpath_idx+1}未找到元素")
                    continue
                except Exception as e:
                    log_warning(f"{country}: xpath{xpath_idx+1}异常: {str(e)}")
                    continue
            
            # 当前尝试失败，如果还有重试机会则等待
            if attempt < max_attempts - 1:
                log_warning(f"{country}: 第{attempt+1}次尝试失败，等待后重试")
                time.sleep(5)
        
        return False

    def try_multiple_click_strategies(self, driver, element, country, element_name, max_strategies=8):
        """
        尝试多种点击策略来解决JavaScript环境问题
        """
        # 优先使用已验证有效的策略，减少尝试次数
        strategies = [
            # 策略1: WebDriver原生点击 ✅ (已验证最有效)
            ("WebDriver原生点击", lambda: element.click()),
            
            # 策略2: safe_click方法 (备选方案1)
            ("safe_click方法", lambda: self.safe_click(driver, element, f"{country}{element_name}(safe_click)")),
            
            # 策略3: JavaScript直接点击 (备选方案2)  
            ("JavaScript直接点击", lambda: driver.execute_script("arguments[0].click();", element))
        ]
        
        for strategy_name, strategy_func in strategies[:min(max_strategies, 3)]:
            try:
                log_info(f"{country}: 尝试{strategy_name}")
                result = strategy_func()
                
                # 检查策略是否成功 (某些策略返回布尔值)
                if result is False:
                    log_warning(f"{country}: {strategy_name}返回失败")
                    continue
                
                # 给元素响应时间
                time.sleep(1)
                
                # 验证点击是否生效 (可选的验证逻辑)
                log_success(f"{country}: {strategy_name}执行成功")
                return True
                
            except Exception as e:
                log_warning(f"{country}: {strategy_name}失败: {str(e)}")
                continue
        
        log_error(f"{country}: 所有{max_strategies}种点击策略均失败")
        return False
    
    def _action_chains_click(self, driver, element):
        """ActionChains鼠标操作"""
        from selenium.webdriver.common.action_chains import ActionChains
        actions = ActionChains(driver)
        actions.move_to_element(element).pause(0.5).click().perform()
        
    def _coordinate_click(self, driver, element):
        """坐标点击"""
        from selenium.webdriver.common.action_chains import ActionChains
        location = element.location
        size = element.size
        x = location['x'] + size['width'] // 2
        y = location['y'] + size['height'] // 2
        
        actions = ActionChains(driver)
        actions.move_by_offset(x, y).click().perform()
        
    def _force_trigger_events(self, driver, element):
        """强制触发事件"""
        driver.execute_script("""
            var element = arguments[0];
            var events = ['mousedown', 'mouseup', 'click', 'change'];
            
            events.forEach(function(eventType) {
                var event = new MouseEvent(eventType, {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                element.dispatchEvent(event);
            });
            
            // 如果是checkbox，直接修改状态
            if (element.type === 'checkbox') {
                element.checked = !element.checked;
                element.dispatchEvent(new Event('change', { bubbles: true }));
            }
        """, element)
        
    def _keyboard_space_trigger(self, driver, element):
        """键盘空格触发"""
        from selenium.webdriver.common.keys import Keys
        element.send_keys(Keys.SPACE)
        
    def _dom_property_change(self, driver, element):
        """DOM属性直接修改"""
        driver.execute_script("""
            var element = arguments[0];
            if (element.type === 'checkbox') {
                element.checked = !element.checked;
                
                // 触发所有可能的事件
                var events = ['input', 'change', 'click', 'focus', 'blur'];
                events.forEach(function(eventType) {
                    try {
                        var event = new Event(eventType, { bubbles: true, cancelable: true });
                        element.dispatchEvent(event);
                    } catch(e) {
                        // 忽略事件触发错误
                    }
                });
            } else {
                // 对于非checkbox元素，尝试触发点击
                element.click();
            }
        """, element)

    def _navigate_to_shipping_settings_page(self, driver, country):
        """导航到运输设置页面"""
        try:
            log_step(f"{country}: 恢复运输设置页面状态...")
            
            # 1. 导航到商铺主页
            if not self.navigate_to_seller_central_home(driver):
                log_error(f"{country}: 导航到主页失败")
                return False
            
            # 2. 导航到运输设置页面
            if not self.navigate_to_shipping_settings(driver, country, None):
                log_error(f"{country}: 导航到运输设置页面失败")
                return False
            
            # 3. 重新执行运输设置流程（因为崩溃时可能在编辑页面）
            log_step(f"{country}: 重新执行运输设置流程...")
            
            # 点击设置按钮
            if not self.click_settings_button(driver, country):
                log_error(f"{country}: 点击设置按钮失败")
                return False
            
            # 点击配送设置
            try:
                shipping_settings_link = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), '配送设置')]"))
                )
                if self.safe_click(driver, shipping_settings_link, f"{country}配送设置链接"):
                    time.sleep(3)
                    log_success(f"{country}: 已点击配送设置")
                else:
                    log_error(f"{country}: 点击配送设置失败")
                    return False
            except Exception as e:
                log_error(f"{country}: 点击配送设置时出错: {str(e)}")
                return False
            
            # 点击配送模板
            try:
                shipping_template_tab = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), '配送模板')]"))
                )
                if self.safe_click(driver, shipping_template_tab, f"{country}配送模板标签"):
                    time.sleep(3)
                    log_success(f"{country}: 已点击配送模板")
                else:
                    log_error(f"{country}: 点击配送模板失败")
                    return False
            except Exception as e:
                log_error(f"{country}: 点击配送模板时出错: {str(e)}")
                return False
            
            # 点击编辑模板
            try:
                edit_template_button = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '编辑模板')]"))
                )
                if self.safe_click(driver, edit_template_button, f"{country}编辑模板按钮"):
                    time.sleep(3)
                    log_success(f"{country}: 已点击编辑模板")
                else:
                    log_error(f"{country}: 点击编辑模板失败")
                    return False
            except Exception as e:
                log_error(f"{country}: 点击编辑模板时出错: {str(e)}")
                return False
            
            log_success(f"{country}: 运输设置页面状态恢复成功")
            return True
        except Exception as e:
            log_error(f"{country}: 导航到运输设置页面失败: {str(e)}")
            return False

    def _navigate_to_fba_settings_page(self, driver, country):
        """导航到FBA设置页面"""
        try:
            log_step(f"{country}: 恢复FBA设置页面状态...")
            
            # 1. 导航到商铺主页
            if not self.navigate_to_seller_central_home(driver):
                log_error(f"{country}: 导航到主页失败")
                return False
            
            # 2. 导航到FBA设置页面
            if not self.navigate_to_fba_settings(driver, country, None):
                log_error(f"{country}: 导航到FBA设置页面失败")
                return False
            
            log_success(f"{country}: FBA设置页面状态恢复成功")
            return True
        except Exception as e:
            log_error(f"{country}: 导航到FBA设置页面失败: {str(e)}")
            return False

    def _navigate_to_return_settings_page(self, driver, country):
        """导航到退货设置页面"""
        try:
            log_step(f"{country}: 恢复退货设置页面状态...")
            
            # 1. 导航到商铺主页
            if not self.navigate_to_seller_central_home(driver):
                log_error(f"{country}: 导航到主页失败")
                return False
            
            # 2. 导航到退货设置页面（这里需要实现具体的导航逻辑）
            log_info(f"{country}: 退货设置页面导航逻辑待实现")
            return True
        except Exception as e:
            log_error(f"{country}: 导航到退货设置页面失败: {str(e)}")
            return False

    def set_breakpoint(self, url, country, store_info, execution_point=None):
        """
        设置断点，记录编辑页面状态和执行位置
        
        Args:
            url: 断点URL
            country: 国家
            store_info: 店铺信息
            execution_point: 执行位置标识
        """
        self.breakpoint_manager['url'] = url
        self.breakpoint_manager['country'] = country
        self.breakpoint_manager['store_info'] = store_info
        self.breakpoint_manager['edit_page_loaded'] = True
        self.breakpoint_manager['execution_point'] = execution_point
        
        # 初始化店铺重试计数器
        if not hasattr(self, 'store_recovery_attempts'):
            self.store_recovery_attempts = {}
        
        store_key = f"{store_info.get('storeId', 'unknown')}_{country}"
        if store_key not in self.store_recovery_attempts:
            self.store_recovery_attempts[store_key] = 0
            
        log_info(f"{country}: 设置断点 - URL: {url}, 执行位置: {execution_point}")

    def clear_breakpoint(self):
        """
        清除断点
        """
        self.breakpoint_manager = {
            'url': None,
            'country': None,
            'store_info': None,
            'edit_page_loaded': False,
            'execution_point': None
        }
        log_info("断点已清除")

    def detect_crash(self, driver, country):
        """
        检测页面是否崩溃
        """
        try:
            # 检查WebDriver连接状态
            driver.current_url
            return False
        except Exception as e:
            error_msg = str(e).lower()
            if "tab crashed" in error_msg or "chrome not reachable" in error_msg:
                log_error(f"{country}: 检测到页面崩溃: {error_msg}")
                return True
            return False

    def recover_from_crash_with_refresh(self, driver, country, store_info):
        """
        从崩溃中恢复 - 使用F5刷新页面，支持重新执行模块
        """
        try:
            log_step(f"{country}: 开始崩溃恢复流程（F5刷新）")
            
            # 检查是否有断点
            if not self.breakpoint_manager['url']:
                log_warning(f"{country}: 没有断点信息，无法恢复")
                return False
            
            # 检查店铺重试次数
            store_key = f"{store_info.get('storeId', 'unknown')}_{country}"
            if self.store_recovery_attempts.get(store_key, 0) >= 1:
                log_warning(f"{country}: 店铺 {store_key} 已达到最大重试次数(1次)，跳过恢复")
                return False
            
            # 增加重试计数
            self.store_recovery_attempts[store_key] = self.store_recovery_attempts.get(store_key, 0) + 1
            log_info(f"{country}: 店铺 {store_key} 第 {self.store_recovery_attempts[store_key]} 次重试")
            
            # 使用F5刷新页面
            log_info(f"{country}: 使用F5刷新页面")
            from selenium.webdriver.common.keys import Keys
            from selenium.webdriver.common.action_chains import ActionChains
            
            actions = ActionChains(driver)
            actions.key_down(Keys.F5).key_up(Keys.F5).perform()
            
            # 等待页面加载
            time.sleep(5)
            log_info(f"{country}: 页面刷新完成")
            
            # 检查是否需要重新登录
            if self.is_login_page(driver, country):
                log_info(f"{country}: 检测到登录页面，处理自动登录")
                if not self.handle_automatic_login(driver, country):
                    log_error(f"{country}: 自动登录失败")
                    return False
            
            # 检查是否需要重新选择国家
            if self.breakpoint_manager['country']:
                target_country = self.breakpoint_manager['country']
                log_info(f"{country}: 检查是否需要重新选择国家: {target_country}")
                # 这里可以添加国家选择逻辑
            
            # 检查执行位置，如果是配送设置模块，重新执行
            execution_point = self.breakpoint_manager.get('execution_point')
            if execution_point == "shipping_settings_module_start":
                log_info(f"{country}: 检测到配送设置模块断点，重新执行模块")
                # 重新执行配送设置模块，从子模块开始
                return self._execute_shipping_settings_from_breakpoint(driver, country, store_info)
            
            log_success(f"{country}: 崩溃恢复成功，页面已刷新")
            return True
            
        except Exception as e:
            log_error(f"{country}: 崩溃恢复过程中发生错误: {str(e)}")
            return False

    def _execute_shipping_settings_from_breakpoint(self, driver, country, store_info):
        """
        从断点重新执行配送设置模块，从子模块开始
        
        Args:
            driver: WebDriver实例
            country: 国家
            store_info: 店铺信息
            
        Returns:
            bool: 执行是否成功
        """
        try:
            log_step(f"{country}: 从断点重新执行配送设置模块")
            
            # 检查子模块配置
            sub_modules = self.module_config["shipping_settings"]["sub_modules"]
            
            # 从 if self.is_module_enabled("shipping_settings", "cancel_non_standard_shipping"): 开始执行
            sub_module_success_count = 0
            total_sub_modules = 0

            if self.is_module_enabled("shipping_settings", "cancel_non_standard_shipping"):
                total_sub_modules += 1
                log_step(f"{country}: 执行子模块 - 取消非标准配送勾选")
                try:
                    self.handle_shipping_services(driver, country)
                    sub_module_success_count += 1
                    log_success(f"{country}: 取消非标准配送勾选子模块执行成功")
                except Exception as e:
                    log_error(f"{country}: 取消非标准配送勾选子模块执行失败: {str(e)}")
                    self.log_setting_exception(store_info, country, "取消非标准配送勾选子模块", f"取消非标准配送勾选子模块执行失败: {str(e)}")
            else:
                log_info(f"{country}: 跳过子模块 - 取消非标准配送勾选")

            if self.is_module_enabled("shipping_settings", "special_region_shipping"):
                total_sub_modules += 1
                log_step(f"{country}: 执行子模块 - 特殊地区运费设置")
                try:
                    self.handle_special_regions(driver, country)
                    sub_module_success_count += 1
                    log_success(f"{country}: 特殊地区运费设置子模块执行成功")
                except Exception as e:
                    log_error(f"{country}: 特殊地区运费设置子模块执行失败: {str(e)}")
                    self.log_setting_exception(store_info, country, "特殊地区运费设置子模块", f"特殊地区运费设置子模块执行失败: {str(e)}")
            else:
                log_info(f"{country}: 跳过子模块 - 特殊地区运费设置")

            # 记录配送设置子模块执行情况
            if total_sub_modules > 0:
                log_info(f"{country}: 配送设置子模块执行情况: {sub_module_success_count}/{total_sub_modules}")

            # 保存配送设置
            log_step(f"{country}: 保存配送设置")
            result = self.save_shipping_settings_with_validation(driver, country, store_info)
            
            # 保存成功后清除断点
            if result:
                self.clear_breakpoint()
                log_success(f"{country}: 配送设置模块从断点重新执行完成")
            
            return result
            
        except Exception as e:
            self.log_setting_exception(store_info, country, "配送设置", f"从断点重新执行配送设置模块错误: {str(e)}")
            return False

    def recover_from_crash(self, driver, country, store_info):
        """
        从崩溃中恢复
        """
        try:
            log_step(f"{country}: 开始崩溃恢复流程")
            
            # 检查是否有断点
            if not self.breakpoint_manager['url']:
                log_warning(f"{country}: 没有断点信息，无法恢复")
                return False
            
            # 获取当前标签页
            current_window = driver.current_window_handle
            all_windows = driver.window_handles
            
            # 创建新标签页
            driver.execute_script("window.open('');")
            new_window = None
            for window in driver.window_handles:
                if window not in all_windows:
                    new_window = window
                    break
            
            if not new_window:
                log_error(f"{country}: 无法创建新标签页")
                return False
            
            # 切换到新标签页
            driver.switch_to.window(new_window)
            log_info(f"{country}: 已切换到新标签页")
            
            # 关闭原标签页
            try:
                driver.switch_to.window(current_window)
                driver.close()
                log_info(f"{country}: 已关闭崩溃的标签页")
            except:
                log_warning(f"{country}: 关闭原标签页失败")
            
            # 切换回新标签页
            driver.switch_to.window(new_window)
            
            # 导航到断点URL
            breakpoint_url = self.breakpoint_manager['url']
            log_info(f"{country}: 导航到断点URL: {breakpoint_url}")
            
            try:
                driver.get(breakpoint_url)
                time.sleep(5)  # 等待页面加载
                
                # 检查是否需要重新登录
                if self.is_login_page(driver, country):
                    log_info(f"{country}: 检测到登录页面，处理自动登录")
                    if not self.handle_automatic_login(driver, country):
                        log_error(f"{country}: 自动登录失败")
                        return False
                
                # 检查是否需要重新选择国家
                if self.breakpoint_manager['country']:
                    target_country = self.breakpoint_manager['country']
                    log_info(f"{country}: 检查是否需要重新选择国家: {target_country}")
                    # 这里可以添加国家选择逻辑
                
                log_success(f"{country}: 崩溃恢复成功，已回到断点")
                return True
                
            except Exception as e:
                log_error(f"{country}: 导航到断点URL失败: {str(e)}")
                return False
                
        except Exception as e:
            log_error(f"{country}: 崩溃恢复过程中发生错误: {str(e)}")
            return False

    def handle_shipping_settings_with_recovery(self, driver, country, store_info):
        """
        带崩溃恢复的配送设置处理
        """
        max_recovery_attempts = 3
        recovery_attempt = 0
        
        while recovery_attempt < max_recovery_attempts:
            try:
                log_step(f"{country}: 开始处理配送设置模块（第{recovery_attempt + 1}次尝试）")
                
                # 检查任务控制
                if self.check_task_control():
                    log_info(f"{country}: 任务被暂停")
                    return False
                
                # 导航到配送设置页面
                if not self.navigate_to_shipping_settings(driver, country, store_info):
                    log_error(f"{country}: 导航到配送设置页面失败")
                    return False
                
                # 处理配送设置（带恢复机制）
                if not self.handle_shipping_settings_logic_with_recovery(driver, country, store_info):
                    log_error(f"{country}: 处理配送设置逻辑失败")
                    return False
                
                log_success(f"{country}: 配送设置模块处理完成")
                return True
                
            except Exception as e:
                error_msg = f"处理配送设置模块时发生错误: {str(e)}"
                log_error(f"{country}: {error_msg}")
                
                # 检测是否崩溃
                if self.detect_crash(driver, country):
                    recovery_attempt += 1
                    if recovery_attempt < max_recovery_attempts:
                        log_warning(f"{country}: 检测到崩溃，尝试恢复（第{recovery_attempt}次）")
                        if self.recover_from_crash_with_refresh(driver, country, store_info):
                            continue  # 恢复成功，继续执行
                        else:
                            log_error(f"{country}: 崩溃恢复失败")
                            break
                    else:
                        log_error(f"{country}: 达到最大恢复尝试次数，放弃处理")
                        break
                else:
                    # 非崩溃错误，记录并返回
                    self.log_setting_exception(store_info, country, "配送设置", error_msg)
                    return False
        
        return False

    def handle_shipping_settings_logic_with_recovery(self, driver, country, store_info):
        """
        带崩溃恢复的配送设置逻辑处理
        """
        try:
            log_step(f"{country}: 开始处理配送设置逻辑（带崩溃恢复）")
            
            # 步骤1: 点击设置按钮
            log_step(f"{country}: 点击设置按钮")
            if not self.click_settings_button(driver, country):
                self.log_setting_exception(store_info, country, "配送设置", "点击设置按钮失败")
                return False

            # 设置按钮点击后检查弹窗
            self.handle_edit_page_popup(driver, country)

            # 步骤2: 点击配送设置
            log_step(f"{country}: 点击配送设置")
            try:
                shipping_settings_link = WebDriverWait(driver, 15).until(
                    EC.element_to_be_clickable((By.XPATH, "//*[@id='navbar']/div[3]/div/div[2]/div/div[2]/div[1]/div/div[1]/a[7]"))
                )
                if self.safe_click(driver, shipping_settings_link, f"{country}配送设置链接"):
                    log_success(f"{country}: 已点击配送设置")
                    time.sleep(2)  # 从5秒减少到2秒
                    # 配送设置页面加载后检查弹窗
                    self.handle_edit_page_popup(driver, country)
                else:
                    self.log_setting_exception(store_info, country, "配送设置", "点击配送设置链接失败")
                    return False
            except TimeoutException:
                self.log_setting_exception(store_info, country, "配送设置", "未找到配送设置链接")
                return False

            # 步骤3: 点击配送模板
            log_step(f"{country}: 点击配送模板")
            try:
                shipping_template_tab = WebDriverWait(driver, 15).until(
                    EC.element_to_be_clickable((By.XPATH, "//*[@id='shipping_templates_tab']/a"))
                )
                if self.safe_click(driver, shipping_template_tab, f"{country}配送模板标签"):
                    log_success(f"{country}: 已点击配送模板")
                    time.sleep(3)
                    # 配送模板页面加载后检查弹窗
                    self.handle_edit_page_popup(driver, country)
                else:
                    self.log_setting_exception(store_info, country, "配送设置", "点击配送模板标签失败")
                    return False
            except TimeoutException:
                self.log_setting_exception(store_info, country, "配送设置", "未找到配送模板标签")
                return False

            # 步骤4: 点击编辑模板
            log_step(f"{country}: 点击编辑模板")
            try:
                edit_template_button = WebDriverWait(driver, 15).until(
                    EC.element_to_be_clickable((By.XPATH, "//*[@id='template_actions_split_dropdown']/span[1]/span/button/span"))
                )
                if self.safe_click(driver, edit_template_button, f"{country}编辑模板按钮"):
                    log_success(f"{country}: 已点击编辑模板")
                    time.sleep(5)
                    
                    # 设置断点 - 记录编辑页面URL（在点击编辑模板后）
                    current_url = driver.current_url
                    self.set_breakpoint(current_url, country, store_info)
                    log_info(f"{country}: 已设置断点，编辑页面URL: {current_url}")
                    
                    # 编辑模板页面加载后检查弹窗
                    self.handle_edit_page_popup(driver, country)
                else:
                    self.log_setting_exception(store_info, country, "配送设置", "点击编辑模板按钮失败")
                    return False
            except TimeoutException:
                self.log_setting_exception(store_info, country, "配送设置", "未找到编辑模板按钮，无法进入编辑页面")
                return False

            # 步骤4.5: 处理编辑页面弹窗
            log_step(f"{country}: 检查并关闭编辑页面弹窗")
            self.handle_edit_page_popup(driver, country)

            # 步骤5: 处理配送设置自动化弹窗
            log_step(f"{country}: 处理配送设置自动化弹窗")
            self.handle_shipping_automation_popup(driver, country)

            # 步骤6: 取消非标准配送的勾选
            log_step(f"{country}: 处理配送服务勾选")
            self.handle_shipping_services(driver, country)

            # 步骤7: 处理特殊地区运费设置
            self.handle_special_regions(driver, country, store_info)

            # 步骤8: 保存设置
            log_step(f"{country}: 保存配送设置")
            result = self.save_shipping_settings_with_validation(driver, country, store_info)
            
            # 保存成功后清除断点
            if result:
                self.clear_breakpoint()
                log_info(f"{country}: 保存成功，断点已清除")
            
            return result

        except Exception as e:
            self.log_setting_exception(store_info, country, "配送设置", f"处理配送设置时发生错误: {str(e)}")
            return False


class ShopSetupGUI(ConfigurableGUI):
    def __init__(self, rpa_instance):
        super().__init__(rpa_instance)

def main():
    """主程序入口 - 启动模块化店铺设置GUI"""
    import platform
    
    # 检查操作系统
    is_windows = platform.system() == 'Windows'
    is_mac = platform.system() == 'Darwin'
    if not is_windows and not is_mac:
        print("webdriver/cdp只支持windows和mac操作系统")
        exit()

    # 创建RPA实例
    rpa = ShopSetup()
    
    # 启动可配置GUI程序
    from gui_framework import ConfigurableGUI
    
    print("正在启动店铺设置模块化GUI...")
    
    # 获取自定义组件配置
    custom_components = rpa.get_custom_gui_components()
    
    # 创建GUI
    app = ConfigurableGUI(rpa, custom_components)
    
    # 设置模块选择逻辑
    rpa.setup_module_selection_logic(app.variable_manager)
    
    print("GUI启动完成，用户可以自由选择执行模块")
    
    # 运行GUI
    app.run()

if __name__ == "__main__":
    main()